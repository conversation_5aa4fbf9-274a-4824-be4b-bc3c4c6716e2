import aidge_core
import numpy as np

# Example 1: Basic Sequential Graph using Connectors
def build_sequential_with_connectors():
    """Build a sequential graph using connectors"""
    
    # Create input data and producer
    input_data = np.random.randn(1, 3, 32, 32).astype(np.float32)
    input_tensor = aidge_core.Tensor(input_data)
    input_node = aidge_core.Producer(input_tensor, "input")
    
    # Create connector from input node
    x = aidge_core.Connector(input_node)
    
    # Chain operations using functional style
    conv1_node = aidge_core.Conv2D(3, 64, [3, 3], name="conv1")
    x = conv1_node(x)  # Apply conv1 to x
    
    relu1_node = aidge_core.ReLU(name="relu1")
    x = relu1_node(x)  # Apply relu1 to x
    
    conv2_node = aidge_core.Conv2D(64, 128, [3, 3], name="conv2")
    x = conv2_node(x)  # Apply conv2 to x
    
    relu2_node = aidge_core.ReLU(name="relu2")
    x = relu2_node(x)  # Apply relu2 to x
    
    # Generate GraphView from final connector
    graph = aidge_core.generate_graph([x])
    
    return graph

# Example 2: Parallel Branches using Connectors
def build_parallel_with_connectors():
    """Build a graph with parallel branches using connectors"""
    
    # Create input
    input_data = np.random.randn(1, 3, 32, 32).astype(np.float32)
    input_tensor = aidge_core.Tensor(input_data)
    input_node = aidge_core.Producer(input_tensor, "input")
    
    # Create connector from input
    x = aidge_core.Connector(input_node)
    
    # Common stem
    conv_stem = aidge_core.Conv2D(3, 64, [3, 3], name="conv_stem")
    x = conv_stem(x)
    
    # Branch 1
    conv1_branch1 = aidge_core.Conv2D(64, 32, [1, 1], name="conv1_branch1")
    branch1 = conv1_branch1(x)
    
    relu1_branch1 = aidge_core.ReLU(name="relu1_branch1")
    branch1 = relu1_branch1(branch1)
    
    # Branch 2
    conv1_branch2 = aidge_core.Conv2D(64, 32, [3, 3], name="conv1_branch2")
    branch2 = conv1_branch2(x)
    
    relu1_branch2 = aidge_core.ReLU(name="relu1_branch2")
    branch2 = relu1_branch2(branch2)
    
    # Merge branches with Add (2 inputs)
    add_node = aidge_core.Add(2, name="merge")
    merged = add_node([branch1, branch2])  # Pass list of connectors for multiple inputs
    
    # Final layer
    final_relu = aidge_core.ReLU(name="final_relu")
    output = final_relu(merged)
    
    # Generate GraphView
    graph = aidge_core.generate_graph([output])
    
    return graph

# Example 3: Multiple Outputs using Connectors
def build_multi_output_with_connectors():
    """Build a graph with multiple outputs using connectors"""
    
    # Create input
    input_data = np.random.randn(1, 3, 32, 32).astype(np.float32)
    input_tensor = aidge_core.Tensor(input_data)
    input_node = aidge_core.Producer(input_tensor, "input")
    
    x = aidge_core.Connector(input_node)
    
    # Shared feature extractor
    conv1 = aidge_core.Conv2D(3, 64, [3, 3], name="shared_conv1")
    x = conv1(x)
    
    relu1 = aidge_core.ReLU(name="shared_relu1")
    x = relu1(x)
    
    conv2 = aidge_core.Conv2D(64, 128, [3, 3], name="shared_conv2")
    x = conv2(x)
    
    # Output 1: Classification head
    cls_conv = aidge_core.Conv2D(128, 10, [1, 1], name="cls_conv")
    cls_output = cls_conv(x)
    
    # Output 2: Regression head
    reg_conv = aidge_core.Conv2D(128, 4, [1, 1], name="reg_conv")
    reg_output = reg_conv(x)
    
    # Generate GraphView with multiple outputs
    graph = aidge_core.generate_graph([cls_output, reg_output])
    
    return graph

# Example 4: Complex Graph from ONNX Tutorial
def build_swish_metaop_with_connectors():
    """Build a Swish activation using connectors (from ONNX tutorial)"""
    
    # Create input nodes
    input_op = aidge_core.Producer([10], "input")
    beta_prod = aidge_core.Producer([1.0] * 10, "beta")
    e_prod = aidge_core.Producer([np.e], "e")
    one_prod = aidge_core.Producer([1.0], "one")
    
    # Create operator nodes
    mul_op = aidge_core.Mul(2, name="mul")
    pow_op = aidge_core.Pow(name="pow")
    add_op = aidge_core.Add(2, name="add")
    div_op = aidge_core.Div(name="div")
    
    # Create connectors
    x = aidge_core.Connector(input_op)
    b = aidge_core.Connector(beta_prod)
    e = aidge_core.Connector(e_prod)
    o = aidge_core.Connector(one_prod)
    
    # Build the expression: x / (1 + e^(x * beta))
    # This creates: div_op(x, add_op(pow_op(e, mul_op(x, b)), o))
    y = div_op([x, add_op([pow_op([e, mul_op([x, b])]), o])])
    
    # Generate the micro graph
    swish_micro_graph = aidge_core.generate_graph([y])
    
    return swish_micro_graph

# Example 5: Working with Multi-Output Operators
def build_with_multi_output_ops():
    """Example of working with operators that have multiple outputs"""
    
    input_data = np.random.randn(1, 6, 32, 32).astype(np.float32)
    input_tensor = aidge_core.Tensor(input_data)
    input_node = aidge_core.Producer(input_tensor, "input")
    
    x = aidge_core.Connector(input_node)
    
    # Assume we have a Split operation that splits into 2 outputs
    # (This is conceptual - actual Split operator may differ)
    split_node = aidge_core.GenericOperator("Split", nb_data=1, nb_param=0, nb_out=2, name="split")
    split_result = split_node(x)
    
    # Access individual outputs using indexing
    output1 = split_result[0]  # First output
    output2 = split_result[1]  # Second output
    
    # Process each output separately
    conv1 = aidge_core.Conv2D(3, 32, [3, 3], name="conv1")
    branch1 = conv1(output1)
    
    conv2 = aidge_core.Conv2D(3, 32, [3, 3], name="conv2")
    branch2 = conv2(output2)
    
    # Generate graph with both outputs
    graph = aidge_core.generate_graph([branch1, branch2])
    
    return graph

if __name__ == "__main__":
    # Test the examples
    print("Building sequential graph with connectors...")
    seq_graph = build_sequential_with_connectors()
    print(f"Sequential graph has {len(seq_graph.get_nodes())} nodes")
    
    print("\nBuilding parallel graph with connectors...")
    par_graph = build_parallel_with_connectors()
    print(f"Parallel graph has {len(par_graph.get_nodes())} nodes")
    
    print("\nBuilding multi-output graph with connectors...")
    multi_graph = build_multi_output_with_connectors()
    print(f"Multi-output graph has {len(multi_graph.get_nodes())} nodes")
    
    print("\nBuilding Swish metaop with connectors...")
    swish_graph = build_swish_metaop_with_connectors()
    print(f"Swish graph has {len(swish_graph.get_nodes())} nodes")
