from pathlib import Path

import aidge_core

__all__ = [
    "export",
]

def export(model: aidge_core.GraphView, inputs: list[aidge_core.Tensor], export_dir: str) -> None:
    export_path = Path(export_dir)
    export_path.mkdir(parents=True, exist_ok=True)

    scheduler = aidge_core.SequentialScheduler(model)
    scheduler.forward(inputs)
    aidge_core.export_utils.scheduler_export(
        scheduler,
        export_dir,
        aidge_export_compile.ExportLibCompile,
        memory_manager=aidge_core.mem_info.compute_default_mem_info
    )
