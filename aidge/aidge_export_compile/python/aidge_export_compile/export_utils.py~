from pathlib import Path

import aidge_core
from aidge_export_cpp import ExportLibCpp

from .export_lib import ExportLibCompile

__all__ = [
    "export",
]

def model_export(model: aidge_core.GraphView, inputs: list[aidge_core.Tensor], export_dir: str) -> None:
    export_path = Path(export_dir)
    export_path.mkdir(parents=True, exist_ok=True)

    scheduler = aidge_core.SequentialScheduler(model)
    scheduler.forward(data=inputs)
    aidge_core.export_utils.scheduler_export(
        scheduler,
        export_dir,
        [ExportLibCompile, ExportLibCpp],
        memory_manager=aidge_core.mem_info.compute_default_mem_info
    )
