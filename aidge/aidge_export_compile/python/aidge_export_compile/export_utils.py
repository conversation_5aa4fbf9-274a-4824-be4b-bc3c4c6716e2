from pathlib import Path
import shutil

import aidge_core
from aidge_core import GraphView, Tensor, Node, Scheduler, MemoryManager
from aidge_core.export_utils import ExportLib, generate_file, copy_file, copy_folder, generate_main_cpp
from aidge_export_cpp import ExportLibCpp

from .export_lib import ExportLibCompile

__all__ = [
    "model_export",
    "scheduler_export",
]

def model_export(
        model: aidge_core.GraphView,
        inputs: list[aidge_core.Tensor],
        export_dir: str,
) -> None:
    """
    Model export: use sequential scheduler
    """
    assert len(inputs) == 1
    scheduler = aidge_core.SequentialScheduler(model)
    scheduler.forward(data=inputs)
    scheduler_export(
        scheduler,
        export_dir,
        [ExportLibCompile, ExportLibCpp],
        memory_manager=aidge_core.mem_info.compute_default_mem_info
    )
    # Generate main file
    generate_main_cpp(export_dir, model, labels=None, inputs_tensor=inputs[0])

def scheduler_export(
        scheduler: Scheduler,
        export_dir: str,
        export_libs: list[ExportLib],
        memory_manager: MemoryManager = None,
        memory_manager_args: dict|None = None,
        dev_mode: bool = False,
) -> None:
    """
    Scheduler export: ref to aidge_core
    """
    print("YYYY")
    graphview = scheduler.graph_view()
    export_folder = Path(export_dir)
    if export_folder.exists():
        shutil.rmtree(export_folder)
    export_folder.mkdir(parents=True, exist_ok=True)
    dnn_folder = export_folder / "dnn"
    dnn_folder.mkdir(parents=True, exist_ok=True)

    if memory_manager_args is None:
        memory_manager_args = {}

    if memory_manager is None:
        raise ValueError("A memory manager is required (no default value yet).")
    peak_mem, mem_info = memory_manager(
        scheduler, **memory_manager_args)

    # List of function call for forward.cpp
    list_actions: list[str] = []
    # List of headers for forward.cpp
    list_configs: list[str] = []

    inputs_name: list[str] = []
    inputs_dtype: list[str] = []
    outputs_name: list[str] = []
    outputs_dtype: list[str] = []
    outputs_size: list[int] = []

    # List of aidge_core.Node ordered by scheduler
    list_forward_nodes: List[aidge_core.Node] = scheduler.get_sequential_static_scheduling()

    def get_export_node(node: Node):
        print("YYYY GET IMPL", str(node.type()))
        op_impl = node.get_operator().get_impl()
        if op_impl is None:
            return None, f"Operator {node.name()}[{node.type()}] doesn't have an implementation."
        if not isinstance(op_impl, ExportLib):
            return None, f"Operator {node.name()}[{node.type()}] doesn't have an exportable backend ({op_impl})."
        # Get operator current specs
        required_specs = op_impl.get_required_spec()
        # Get specs of the implementation that match current specs
        specs = op_impl.get_best_match(required_specs)
        # Retrieve said implementation
        export_node = op_impl.get_export_node(specs)
        if export_node is None:
            return None, f"Could not find export node for {node.name()}[{node.type()}]."
        return export_node, ""

    for node in list_forward_nodes:
        export_node, error = None, None
        for export_lib in export_libs:
            aidge_core.Log.debug(f"Setting backend {export_lib._name} to {node.name()}[{node.type()}].")
            if node.type() not in export_lib._export_node_registry:
                print("YYYY NODE NOT IN REGISTRY", node.type(), export_lib._name)
            else:
                print("YYYY SET BACKEND", export_lib._name, str(node.type()))
                node.get_operator().set_backend(export_lib._name)
                print("YYYY GET EXPORT NODE", export_lib._name, str(node.type()))
                export_node, error = get_export_node(node)
                if export_node is not None:
                    break
        if export_node is None:
            raise RuntimeError(error)

        # Instanciate ExportNode
        op = export_node(node, mem_info[node])

        is_input:bool  = node in graphview.get_input_nodes()
        is_output:bool = node in graphview.get_output_nodes()

        if is_input:
            flag_not_input = True
            # GraphView.get_inputs_nodes() returns the nodes that have an Input set to None or not in the graph
            # However, some inputs are Optional and thus the node may not be an input of the graph!
            # So we need to check that at least one input of the nodes is not in the graph and not optional
            # This is what the following code block is checking.
            for idx, node_in in enumerate(node.inputs()):
                optional:bool = node.get_operator().is_optional_input(idx)
                # Note: node_in is a Tuple(Node, out_idx)
                in_graph:bool = node_in[0] in graphview.get_nodes()
                flag_not_input &= (in_graph or optional)
            is_input = not flag_not_input

        # For configuration files
        list_configs += op.export(dnn_folder)
        # For forward file
        list_actions += op.forward()
        if is_input:
            for idx, node_in in enumerate(node.inputs()):
                if (node.get_operator().get_input(idx) is not None) and (node_in[0] not in graphview.get_nodes()):
                    inputs_name.append(op.attributes["in_name"][idx])
                    inputs_dtype.append(
                        op.attributes["in_cdtype"][idx]
                    )

        if is_output:
            for idx in range(len(node.outputs())):
                outputs_name.append(op.attributes["out_name"][idx])
                outputs_dtype.append(
                    op.attributes["out_cdtype"][idx]
                )
                outputs_size.append(op.attributes["out_size"][idx])

    # Remove duplicates and sort headers by name
    headers = sorted(list(dict.fromkeys([str(path) for path in list_configs])))

    func_name = "model_forward"
    AIDGE_CORE_EXPORT_ROOT = Path(aidge_core.export_utils.__file__).parent

    forward_template = str(AIDGE_CORE_EXPORT_ROOT / "templates" / "forward.jinja")
    if export_libs[0].forward_template != None:
        forward_template = export_libs[0].forward_template

    list_node_names = []
    for node in list_forward_nodes:
        if node.type() != "Producer":
            list_node_names.append(node.name())

    generate_file(
        str(dnn_folder / "src" / "forward.cpp"),
        forward_template,
        func_name=func_name,
        headers=headers,
        actions=list_actions,
        # Note: Graph may not have inputs, so we need to check with output
        # In the future, we should remove this as it is not compatible
        # with a mix precision approach.
        mem_ctype=outputs_dtype[0],  # Legacy behavior ...
        mem_section=export_libs[0].mem_section,
        peak_mem=peak_mem,
        inputs_name=inputs_name,
        inputs_dtype=inputs_dtype,
        outputs_name=outputs_name,
        outputs_dtype=outputs_dtype,
        dev_mode=dev_mode,
        list_node_names=list_node_names
    )

    forward_header_template = str(AIDGE_CORE_EXPORT_ROOT / "templates" / "forward_header.jinja")
    if export_libs[0].forward_header_template != None:
        forward_header_template = export_libs[0].forward_header_template

    # Generate dnn API
    generate_file(
        str(dnn_folder / "include" / "forward.hpp"),
        forward_header_template,
        libraries=[],
        func_name=func_name,
        inputs_name=inputs_name,
        inputs_dtype=inputs_dtype,
        outputs_name=outputs_name,
        outputs_dtype=outputs_dtype,
        dev_mode=dev_mode
    )

    if len(outputs_name) != len(outputs_dtype) or len(outputs_name) != len(outputs_size):
        raise RuntimeError("FATAL: Output args list does not have the same length this is an internal bug.")

    for export_lib in export_libs:
        # Copy all static files in the export
        for source, destination in export_lib.static_files.items():
            copy_file(source, str(export_folder / destination), dev_mode)
        # Copy all static folders in the export
        for source, destination in export_lib.static_folders.items():
            copy_folder(source, str(export_folder / destination), dev_mode)
