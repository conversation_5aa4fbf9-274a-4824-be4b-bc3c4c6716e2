import aidge_core
from aidge_core.export_utils import get_node_from_metaop

from ..compiled_export_node import CompiledExportNode
from ..export_lib import ExportLibCompile as export_lib


__all__ = []

_spec_any = aidge_core.ImplSpec(aidge_core.IOSpec(aidge_core.dtype.any))

@export_lib.register_metaop("PaddedConv2D", _spec_any)
class CompiledPaddedConv2D(CompiledExportNode):
    def __init__(self, node, mem_info):
        super().__init__(node, mem_info)

        PadNode = get_node_from_metaop(node, "Pad2D")
        self.attributes["padding"] = PadNode[0].get_operator().attr.begin_end_borders
        ConvNode = get_node_from_metaop(node, "Conv2D")
        self.attributes["kernel_dims"] = ConvNode[0].get_operator().attr.kernel_dims
        self.attributes["stride_dims"] = ConvNode[0].get_operator().attr.stride_dims
        self.attributes["dilation_dims"] = ConvNode[0].get_operator().attr.dilation_dims
        assert all([d == 1 for d in self.attributes["dilation_dims"]]), f"TODO: unsupported dilatation dims {self.attributes['dilation_dims']}"
