from aidge_core import Node, ImplSpec, IOSpec, dtype
from aidge_core.export_utils import get_node_from_metaop
from aidge_export_cpp import ROOT

from ..compiled_export_node import CompiledExportNode
from ..export_lib import ExportLibCompile

__all__ = []

_spec_any = ImplSpec(IOSpec(dtype.any))

@ExportLibCompile.register_metaop("PaddedConv2D", _spec_any)
class CompiledPaddedConv2D(CompiledExportNode):
    def __init__(self, node: Node, mem_info):
        super().__init__(node, mem_info)

        self.attributes["activation"] = "Linear"
        self.attributes["aidge_cmp"] = node.attributes().has_attr("aidge_cmp")
        self.attributes["rescaling"] = "NoScaling"
        self.attributes["shift_value"] = 0

        PadNode = get_node_from_metaop(node, "Pad2D")
        self.attributes["padding"] = PadNode[0].get_operator().attr.begin_end_borders
        ConvNode = get_node_from_metaop(node, "Conv2D")
        self.attributes["kernel_dims"] = ConvNode[0].get_operator().attr.kernel_dims
        self.attributes["stride_dims"] = ConvNode[0].get_operator().attr.stride_dims
        self.attributes["dilation_dims"] = ConvNode[0].get_operator().attr.dilation_dims
        assert all([d == 1 for d in self.attributes["dilation_dims"]]), f"TODO: unsupported dilatation dims {self.attributes['dilation_dims']}"

        self.config_template = str(ROOT / "templates" / "configuration" / "convolution_config.jinja")
        self.forward_template = str(ROOT / "templates" / "kernel_forward" / "convolution_forward.jinja")
        # Files to include within the generated forward.cpp file
        self.include_list = []
        # Path to the kernel(s) files to copy
        self.add_kernel_to_copy(ROOT / "kernels" / "convolution.hpp")
        self.add_kernel_to_copy(ROOT / "static" / "macs.hpp", "include/network", fwd_include=False)

        # Include aidge outputs within the fwd file
        if self.attributes["aidge_cmp"]:
            self.include_list.append("network/utils.hpp")   # aidge_cmp function
            self.include_list.append("data/aidge_outputs/" + node.name() + ".hpp") 
