import aidge_core
from aidge_core import ImplSpec
from aidge_core.export_utils import ExportLib, ExportNode

__all__ = [
    "ExportLibCompile",
]


class ExportLibCompile(ExportLib):
    _name = "export_compile"
    _export_node_filters = {}

    def _filters_apply(self, filters) -> bool:
        operator = self.get_operator()
        return all([filter(operator) for filter in filters])

    def get_export_node(self, spec: ImplSpec) -> ExportNode:
        """
        Ref parent class. Check additionally list of filters in addition to ImplSpec.
        """
        type = self.get_operator().type()
        for (registered_spec, export_node), filters in (
                zip(self._export_node_registry[type], self._export_node_filters[type])
        ):
            if registered_spec == spec and self._filters_apply(filters):
                return export_node
        return None

    @classmethod
    def register_metaop(cls, op_type, spec, filters = []):
        """
        Ref parent class. Add list of filters in addition to ImplSpec.
        """
        def decorator(operator):
            type_list = []
            if isinstance(op_type, list):
                type_list = op_type
            elif isinstance(op_type, str):
                type_list = [op_type]
            else:
                raise TypeError("Argument 'op_type' of register method should be of type 'List[str]' or 'str', got {type(type)}")
            for type_name in type_list:
                if (type_name not in cls._export_node_registry):
                    cls._export_node_registry[type_name] = []
                    cls._export_node_filters[type_name] = []
                cls._export_node_registry[type_name].append((spec, operator))
                aidge_core.register_MetaOperatorOp([cls._name, type_name], cls)
                spec.attrs.add_attr("type", type_name) # MetaOperator specs need to verify the type
                cls._export_node_registry[type_name].append((spec, operator))
                cls._export_node_filters[type_name].append(filters)
            return operator
        return decorator
