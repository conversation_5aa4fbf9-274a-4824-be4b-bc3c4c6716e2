from aidge_core import ImplSpec
from aidge_core.export_utils import ExportLib, ExportNode
from aidge_export_cpp import ExportLibCpp

__all__ = [
    "ExportLibCompile",
]


class ExportLibCompile(ExportLib):
    _name = "export_compile"
    _export_lib_cpp = ExportLibCpp

    def __init__(self, operator):
        print("YYY export_lib_compile INIT", self.__class__.__name__)
        super().__init__(operator)

    def get_export_node(self, spec: ImplSpec) -> ExportNode:
        for registered_spec, export_node in ExportLibCpp._export_node_registry[self.get_operator().type()]:
            if registered_spec == spec:

                return export_node
        return None

    #ExportLibCpp.get_export_node(
    #     for registered_spec, export_node in self._export_node_registry[self.get_operator().type()]:
    #         if registered_spec == spec:
    #             return export_node
    #     for registered_spec, export_node in ExportLibCpp._export_node_registry[self.get_operator().type()]:
    #         if registered_spec == spec:
    #             return export_node
    #     return None
