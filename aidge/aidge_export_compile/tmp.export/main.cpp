
#include <iostream>
#include "forward.hpp"
#include "data/mini_conv0_input_0.h"

int main()
{
    // Initialize the output arrays
    float* minirelu0_output_0 = nullptr;
    

    // Call the forward function
    model_forward(mini_conv0_input_0, &minirelu0_output_0);

    // Print the results

    printf("minirelu0_output_0:\n");
    for (int o = 0; o < 4; ++o) {
        printf("%f ", minirelu0_output_0[o]);
    }
    printf("\n");
    return 0;
}
