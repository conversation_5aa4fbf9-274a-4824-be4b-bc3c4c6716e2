/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_CLIP_H_
#define AIDGE_CORE_OPERATOR_CLIP_H_

#include <memory>
#include <vector>
#include <limits>

#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/data/Tensor.hpp"

#include "aidge/graph/Node.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Types.h"


#define LIST_CLIP_ATTR(X)  \
    X(Min, "min", float),  \
    X(Max, "max", float)

namespace Aidge {
/**
 * @enum ClipAttr
 * @brief Enum class defining the attributes for the Clip operator.
 *
 * - Min: Minimum value for clipping.
 * - Max: Maximum value for clipping.
 */
enum class ClipAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_CLIP_ATTR)
};
}  // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::ClipAttr> {
    static const char* const data[];
};
constexpr const char* const EnumStrings<Aidge::ClipAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_CLIP_ATTR)
};
}

namespace Aidge {
/**
 * @brief Description of the Clip operation to limit tensor values within a specified range.
 *
 * The Clip operator ensures tensor elements are within the range `[min, max]`.
 * - Values less than `min` are set to `min`.
 * - Values greater than `max` are set to `max`.
 *
 * The input and output Tensors have the same dimensions.
 *
 * ### Attributes:
 * - `Min`: Minimum value for clipping.
 * - `Max`: Maximum value for clipping.
 *
 * @see OperatorTensor
 * @see Registrable
 */
class Clip_Op : public OperatorTensorWithImpl<Clip_Op> {
private:
    using Attributes_ = StaticAttributes<ClipAttr,
        GENERATE_LIST_ATTR_TYPE(LIST_CLIP_ATTR)
    >;

    template <ClipAttr e>
    using attr = typename Attributes_::template attr<e>;

    const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "Clip";
    static constexpr const char* const InputsName[] = {"data_input", "min_empty_tensor", "max_empty_tensor"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    /**
     * @brief Deleted default constructor.
     */
    Clip_Op() = delete;

    /**
     * @brief Constructor for Clip operator.
     * @param[in] min Minimum value for clipping.
     * @param[in] max Maximum value for clipping.
     */
    Clip_Op(float min, float max);

    /**
     * @brief Copy-constructor. Copies operator attributes and output tensors, but not input tensors.
     * @param op Clip_Op instance to copy.
     */
    Clip_Op(const Clip_Op& op);

    bool dimsForwarded() const override final;
    bool forwardDims(bool allowDataDependency = false) override final;

    /**
     * @brief Forward the data type.
     * @return True if successful, false otherwise.
     */
    bool forwardDType() override final;

    /**
     * @brief Access the attributes of the operator.
     * @return A shared pointer to the attributes.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Getter for the minimum clipping value.
     * @return Reference to the minimum value.
     */
    float& min() const;

    /**
     * @brief Getter for the maximum clipping value.
     * @return Reference to the maximum value.
     */
    float& max() const;
};

/**
 * @brief Factory function to create a Clip node.
 * @param[in] name Name of the node.
 * @param[in] min Minimum clipping value (default: lowest float).
 * @param[in] max Maximum clipping value (default: maximum float).
 * @return A shared pointer to the created Clip node.
 */
std::shared_ptr<Aidge::Node> Clip(
    const std::string& name = "",
    float min = std::numeric_limits<float>::lowest(),
    float max = std::numeric_limits<float>::max()
);

} // namespace Aidge

#undef LIST_CLIP_ATTR

#endif /* AIDGE_CORE_OPERATOR_CLIP_H_ */
