/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_BITSHIFT_H_
#define AIDGE_CORE_OPERATOR_BITSHIFT_H_

#include <memory>
#include <string>
#include <vector>

#include "aidge/utils/Registrar.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/utils/Types.h"
#include "aidge/utils/StaticAttributes.hpp"

#define LIST_BITSHIFT_ATTR(X) X(BitShiftdirection, "bit_shift_direction", BitShiftDirection), X(Rounding,"rounding",bool)

namespace Aidge {
enum class BitShiftAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_BITSHIFT_ATTR)
};
}  // namespace Aidge

namespace {
/// @brief Specialization of `EnumStrings` for `BitShiftAttr`.
template <>
struct EnumStrings<Aidge::BitShiftAttr> {
    static const char* const data[];
};
constexpr const char* const EnumStrings<Aidge::BitShiftAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_BITSHIFT_ATTR)
};
}

namespace Aidge {
/**
 * @class BitShift_Op
 * @brief A tensor operator to perform element-wise bitwise shift operations on tensors.
 *
 * The operator takes two inputs:
 * - **InputTensor**: The tensor whose elements will be shifted.
 * - **ShiftAmount**: The tensor specifying the shift amount for each element.
 *
 * The shift is applied in the direction specified by the attribute `BitShiftdirection`,
 * which can either be `left` or `right`.
 *
 * @see OperatorTensor
 * @see Registrable
 */
class BitShift_Op : public OperatorTensorWithImpl<BitShift_Op> {
public:
    /**
     * @enum BitShiftDirection
     * @brief Specifies the direction of the bitwise shift.
     */
    enum BitShiftDirection { left, right };

private:
    using Attributes_ = StaticAttributes<BitShiftAttr,
                            GENERATE_LIST_ATTR_TYPE(LIST_BITSHIFT_ATTR)
                        >;

    template <BitShiftAttr e>
    using attr = typename Attributes_::template attr<e>;

    const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "BitShift";
    static constexpr const char* const InputsName[] = {"InputTensor", "ShiftAmount"};
    static constexpr const char* const OutputsName[] = {"OutputTensor"};

    /**
     * @brief Constructor to initialize the `BitShift_Op` with a shift direction.
     * @param[in] direction The direction of the bitwise shift (left or right).
     */
    BitShift_Op(BitShiftDirection direction, bool rounding = false);

    /**
     * @brief Copy-constructor. Copies operator attributes and output tensors but not input tensors.
     * @param[in] op Operator instance to copy.
     */
    BitShift_Op(const BitShift_Op& op);

    bool forwardDims(bool allowDataDependency = false) override final;

    /**
     * @brief Get the attributes of the operator.
     * @return A shared pointer to the attributes.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Retrieve the direction of the bitwise shift (left or right).
     * @return The direction of the bitwise shift.
     */
    inline BitShiftDirection& direction() const noexcept {
        return mAttributes->template getAttr<BitShiftAttr::BitShiftdirection>();
    }

    /**
     * @brief Retrieve the rounding flag.
     * @return A boolean (True: Apply bitshift rounding).
     */
    inline bool rounding() const noexcept {
        return mAttributes->template getAttr<BitShiftAttr::Rounding>();
    }
};

/**
 * @brief Factory function to create a `BitShift` node.
 * @param[in] direction The direction of the bitwise shift (`left` or `right`).
 * @param[in] rounding Apply rounding
 * @param[in] name (Optional) Name of the node.
 * @return A shared pointer to the created node.
 */
std::shared_ptr<Node> BitShift(const BitShift_Op::BitShiftDirection direction, bool rounding = false, const std::string& name = "");

} // namespace Aidge

#undef LIST_BITSHIFT_ATTR

/**
 * @brief EnumStrings specialization for BitShift_Op::BitShiftDirection.
 */
template <>
const char* const EnumStrings<Aidge::BitShift_Op::BitShiftDirection>::data[] = {
    "left", "right"
};

#endif /* AIDGE_CORE_OPERATOR_BITSHIFT_H_ */
