/********************************************************************************
 * Copyright (c) 2024 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_EQUAL_H_
#define AIDGE_CORE_OPERATOR_EQUAL_H_

#include <memory>
#include <string>
#include <vector>

#include "aidge/utils/Registrar.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {

/**
 * @brief Tensor element-wise logical equal operation.
 */
class Equal_Op : public OperatorTensorWithImpl<Equal_Op> {
public:
    static constexpr const char* const Type = "Equal";
    static constexpr const char* const InputsName[] = {"data_input_1", "data_input_2"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    /**
     * @brief Compute element-wise Equal operation on two given inputs.
     * @details supports broadcasting of both operands.
     */
    Equal_Op() : OperatorTensorWithImpl(Type, {InputCategory::Data, InputCategory::Data}, 1) {}

    bool forwardDims(bool allowDataDependency = false) override final;
};

inline std::shared_ptr<Node> Equal(const std::string& name = "") {
    return std::make_shared<Node>(std::make_shared<Equal_Op>(), name);
}
} // namespace Aidge

#endif /* AIDGE_CORE_OPERATOR_EQUAL_H_ */
