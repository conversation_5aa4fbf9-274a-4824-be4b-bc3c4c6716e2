/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_CONCAT_H_
#define AIDGE_CORE_OPERATOR_CONCAT_H_

#include <memory>
#include <stdexcept>
#include <string>
#include <vector>

#include "aidge/utils/Registrar.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/utils/ErrorHandling.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Types.h"
#include "aidge/backend/generic/operator/ConcatImpl.hpp"


#define LIST_CONCAT_ATTR(X)  \
    X(Axis, "axis", std::int32_t)

namespace Aidge {
/**
 * @enum ConcatAttr
 * @brief Attributes for the Concat operation.
 *
 * - Axis: index of dimension along which the input tensors are concatenated.
 */
enum class ConcatAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_CONCAT_ATTR)
};
}  // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::ConcatAttr> {
    static const char* const data[];
};
constexpr const char* const EnumStrings<Aidge::ConcatAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_CONCAT_ATTR)
};
}

namespace Aidge {
/**
 * @class Concat_Op
 * @brief Implements the Concat operation to concatenate multiple tensors along a specified axis.
 *
 * The Concat operation combines multiple input tensors into a single tensor by concatenating them
 * along a specified axis. The tensors must have the same shape except for the concatenating axis.
 *
 * ### Output Shape Calculation
 * - Input shapes: [(N1, D1, D2, ..., DN), (N2, D1, D2, ..., DN), ...]
 * - Axis: 1
 * - Output shape: (N1 + N2, D1, D2, ..., DN)
 *
 * @example:
 * - Input shapes: [(2, 4, 8), (3, 4, 8), (1, 4, 8)]
 * - Axis: 0
 * - Output shape: (6, 4, 8)
 *
 * @see OperatorTensor
 * @see Registrable
 */
class Concat_Op : public OperatorTensorWithImpl<Concat_Op, Concat_OpImpl> {
private:
    using Attributes_ = StaticAttributes<ConcatAttr, GENERATE_LIST_ATTR_TYPE(LIST_CONCAT_ATTR)>;

    template <ConcatAttr e>
    using attr = typename Attributes_::template attr<e>;

    const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "Concat";
    static constexpr const char* const InputsName[] = {"data_input_0", "data_input_n"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    /**
     * @brief Default constructor is deleted to enforce explicit initialization.
     */
    Concat_Op() = delete;

    /**
     * @brief Constructor to initialize the Concat operator.
     * @param[in] nbIn Number of input tensors.
     * @param[in] axis Axis along which concatenation is performed.
     */
    Concat_Op(const IOIndex_t nbIn, const std::int32_t axis = 0);

    /**
     * @brief Copy-constructor. Copies the operator attributes and its output tensors,
     * but not its input tensors (the new operator has no input associated).
     * @param[in] op Operator to copy.
     */
    Concat_Op(const Concat_Op& op);

    /**
     * @brief Forward the dimensions of the operator's inputs and outputs.
     * @param[in] allowDataDependency Allow data dependency during dimension propagation.
     * @return True if dimensions were forwarded successfully.
     */
    bool forwardDims(bool allowDataDependency = false) override final;

    /**
     * @brief Get the attributes of the Concat operator.
     * @return A shared pointer to the attributes.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Get or modify the axis along which tensors are concatenated.
     * @return A reference to the axis attribute.
     */
    inline std::int32_t& axis() const { return mAttributes->template getAttr<ConcatAttr::Axis>(); }
};

/**
 * @brief Factory function to create a Concat node.
 * @param[in] nbIn Number of input tensors.
 * @param[in] axis Axis along which concatenation is performed (default: 0).
 * @param[in] name (Optional) Name of the node.
 * @return A shared pointer to the created node.
 */
std::shared_ptr<Node> Concat(const IOIndex_t nbIn, const std::int32_t axis = 0, const std::string& name = "");

} // namespace Aidge

#undef LIST_CONCAT_ATTR

#endif /* AIDGE_CORE_OPERATOR_CONCAT_H_ */
