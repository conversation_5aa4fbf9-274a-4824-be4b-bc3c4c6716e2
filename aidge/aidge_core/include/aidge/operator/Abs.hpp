/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_ABS_H_
#define AIDGE_CORE_OPERATOR_ABS_H_

#include <memory>
#include <string>
#include <vector>

#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {

/**
* @brief Description of an element-wise Absolute Value (Abs) operation
 * on an input Tensor.
 *
 * For each element x in the input, the function is defined as:
 * `f(x) = |x|`, where |x| denotes the absolute value of x.
 *
 * The input and output Tensors have the same dimensions.
 *
 * @see OperatorTensor
 * @see Registrable
 */
class Abs_Op : public OperatorTensorWithImpl<Abs_Op> {
public:
    static constexpr const char* const Type = "Abs";
    static constexpr const char* const InputsName[] = {"data_input"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    Abs_Op();
};

std::shared_ptr<Node> Abs(const std::string& name = "");

} // namespace Aidge

#endif /* AIDGE_CORE_OPERATOR_ABS_H_ */
