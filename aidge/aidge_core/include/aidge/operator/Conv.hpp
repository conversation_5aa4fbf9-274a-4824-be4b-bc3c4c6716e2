/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_CONV_H_
#define AIDGE_CORE_OPERATOR_CONV_H_

#include <algorithm>
#include <array>
#include <cstddef>  // std::size_t
#include <string>
#include <utility>  // std::pair
#include <vector>

#include "aidge/graph/Node.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/utils/ArrayHelpers.hpp"
#include "aidge/utils/Registrar.hpp" // SET_IMPL_MACRO
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Types.h"


#define LIST_CONV_ATTR(X)                            \
    X(KernelDims, "kernel_dims", sizeArr_t<DIM>),    \
    X(StrideDims, "stride_dims", sizeArr_t<DIM>),    \
    X(DilationDims, "dilation_dims", sizeArr_t<DIM>)

namespace Aidge {
/**
 * @enum Attr
 * @brief Attributes used for the Convolution operation.
 *
 * - StrideDims: The stride dimensions.
 * - DilationDims: The dilation dimensions.
 * - KernelDims: The kernel dimensions.
 */
enum class ConvAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_CONV_ATTR)
};

template <DimIdx_t DIM> struct Conv_Op_Type {};
template <> struct Conv_Op_Type<1> { static constexpr const char* const value = "Conv1D"; };
template <> struct Conv_Op_Type<2> { static constexpr const char* const value = "Conv2D"; };
template <> struct Conv_Op_Type<3> { static constexpr const char* const value = "Conv3D"; };

/**
 * @class Conv_Op
 * @brief Convolution operator for performing a multi-dimensional convolution.
 *
 * The Conv_Op class implements a convolution operator for tensors with customizable
 * kernel dimensions, stride, and dilation values. The operator performs a convolution
 * operation on the input tensor and produces an output tensor.
 *
 * ### Attributes:
 * - `strideDims`: Stride for each dimension of the input.
 * - `dilationDims`: Dilation for each dimension of the input.
 * - `kernelDims`: Kernel size for each dimension.
 *
 * The output shape will depend on the kernel size, stride, and dilation values:
 *    Output Dimension=((Input Dimension−Kernel Dimension+2×Padding​)/Stride)+1
 *
 * @example: Basic 2D Convolution
 *      - Input shape: (1, 3, 32, 32)
 *      - Kernel dimensions: {3, 3} (2D kernel)
 *      - Stride dimensions: {1, 1} (stride of 1 in both height and width)
 *      - Dilation dimensions: {1, 1} (no dilation)
 *      - Padding: None
 *      - Output shape:
 *         (1, 64, (32−3+2×0)/1+1, (32−3+2×0)/1+1) = (1, 64, 30, 30)
 *
 * @see OperatorTensor
 * @see Registrable
 */
template <DimIdx_t DIM>
class Conv_Op : public OperatorTensorWithImpl<Conv_Op<DIM>> {
private:
    // Use the external enum so that Aidge::Conv_Op<DIM>::Attr is valid.
    using Attr = ConvAttr;

    using Attributes_ = StaticAttributes<Attr, GENERATE_LIST_ATTR_TYPE(LIST_CONV_ATTR)>;
    template <Attr e>
    using attr = typename Attributes_::template attr<e>;
    const std::shared_ptr<Attributes_> mAttributes;

public:
    using OperatorTensorWithImpl<Conv_Op<DIM>>::getInput;
    using OperatorTensorWithImpl<Conv_Op<DIM>>::getOutput;
    using OperatorTensorWithImpl<Conv_Op<DIM>>::OperatorTensorWithImpl;
    using OperatorTensorWithImpl<Conv_Op<DIM>>::inputsAssociated;
    using OperatorTensorWithImpl<Conv_Op<DIM>>::mOutputs;
    using OperatorTensorWithImpl<Conv_Op<DIM>>::dimsForwarded;

    static constexpr const char* const Type = Conv_Op_Type<DIM>::value;
    static constexpr const char* const InputsName[] = {"data_input", "weight", "bias"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    Conv_Op() = delete;

    /**
     * @brief Constructor for the Conv_Op operator.
     * @param[in] kernelDims The dimensions of the kernel.
     * @param[in] strideDims The stride dimensions (default is an array of ones).
     * @param[in] dilationDims The dilation dimensions (default is an array of ones).
     */
    constexpr Conv_Op(const std::array<DimSize_t, DIM> &kernelDims,
                      const std::array<DimSize_t, DIM> &strideDims = create_array<DimSize_t,DIM>(1),
                      const std::array<DimSize_t, DIM> &dilationDims = create_array<DimSize_t,DIM>(1))
        : OperatorTensorWithImpl<Conv_Op<DIM>>(Type, {InputCategory::Data, InputCategory::Param, InputCategory::OptionalParam}, 1),
          mAttributes(std::make_shared<Attributes_>(
            attr<Attr::StrideDims>(strideDims),
            attr<Attr::DilationDims>(dilationDims),
            attr<Attr::KernelDims>(kernelDims)))
    {}

    /**
     * @brief Copy-constructor.
     * @param op The Conv_Op to copy.
     * @details This constructor copies the operator attributes and its output tensors, but not the input tensors.
     */
    Conv_Op(const Conv_Op<DIM>& op);

    /**
     * @brief Compute forward dimensions for the operator.
     * @param allowDataDependency Flag to allow data dependency in dimension calculation.
     * @return true if the dimensions are computed successfully.
     */
    bool forwardDims(bool /*allowDataDependency*/ = false) override final;

    /**
     * @brief Calculate the receptive field of the convolution operation.
     * @param firstEltDims The dimensions of the first element.
     * @param outputDims The dimensions of the output tensor.
     * @param outputIdx Index of the output tensor.
     * @return A pair containing the receptive field dimensions.
     */
    std::vector<std::pair<std::vector<DimSize_t>, std::vector<DimSize_t>>>
    computeReceptiveField(const std::vector<DimSize_t>& firstEltDims,
                          const std::vector<DimSize_t>& outputDims,
                          const IOIndex_t outputIdx = 0) const override;

    /**
     * @brief Get the number of input channels.
     * @return The number of input channels.
     * @throws std::runtime_error If the operator has no associated weight tensor.
     */
    DimSize_t inChannels() const;

    /**
     * @brief Get the number of output channels.
     * @return The number of output channels.
     * @throws std::runtime_error If the operator has no associated weight tensor.
     */
    DimSize_t outChannels() const;

    /**
     * @brief Get the attributes of the operator.
     * @return A shared pointer to the operator's attributes.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Get the stride dimensions.
     * @return The stride dimensions as a reference.
     */
    inline std::array<DimSize_t, DIM>& strideDims() const { return mAttributes->template getAttr<Attr::StrideDims>(); }

    /**
     * @brief Get the dilation dimensions.
     * @return The dilation dimensions as a reference.
     */
    inline std::array<DimSize_t, DIM>& dilationDims() const { return mAttributes->template getAttr<Attr::DilationDims>(); }

    /**
     * @brief Get the kernel dimensions.
     * @return The kernel dimensions as a reference.
     */
    inline std::array<DimSize_t, DIM>& kernelDims() const { return mAttributes->template getAttr<Attr::KernelDims>(); }
};

/**
 * @brief Create a Conv_Op operator for performing convolution.
 *
 * This function constructs a Convolution operation by specifying the input and output channels,
 * kernel dimensions, stride, and dilation dimensions.
 *
 * @tparam DIM The number of dimensions of the input tensor.
 * @param[in] inChannels The number of input channels.
 * @param[in] outChannels The number of output channels.
 * @param[in] kernelDims The kernel dimensions.
 * @param[in] name The name of the operator (optional).
 * @param[in] strideDims The stride dimensions (optional).
 * @param[in] dilationDims The dilation dimensions (optional).
 * @param[in] noBias A flag indicating if no bias is used (default is false).
 * @return A shared pointer to the created Node containing the Conv_Op.
 */
template <std::array<DimSize_t, 1>::size_type DIM>
std::shared_ptr<Node> Conv(DimSize_t inChannels,
                           DimSize_t outChannels,
                           const std::array<DimSize_t, DIM> &kernelDims,
                           const std::string& name = "",
                           const std::array<DimSize_t, DIM> &strideDims = create_array<DimSize_t,DIM>(1),
                           const std::array<DimSize_t, DIM> &dilationDims = create_array<DimSize_t,DIM>(1),
                           bool noBias = false);

/**
 * @brief Perform a convolution on the input Tensor.
 *
 * @tparam DIM Number of dimensions for the feature map.
 * @param inChannels Number of input channels.
 * @param outChannels Number of output channels.
 * @param kernelDims Dimensions of the kernel. Must be the same number of dimensions as the feature map.
 * @param name Name of the operator.
 * @param strideDims Dimensions of the stride attribute. Must be the same number of dimensions as the feature map.
 * @param dilationDims Dimensions of the dilation attribute. Must be the same number of dimensions as the feature map.
 * @return std::shared_ptr<Node> A Node containing the operator.
 */
template <DimSize_t DIM>
std::shared_ptr<Node> Conv(
    DimSize_t inChannels,
    DimSize_t outChannels,
    DimSize_t const (&kernelDims)[DIM],
    const std::string& name = "",
    const std::array<DimSize_t, DIM> &strideDims = create_array<DimSize_t,DIM>(1),
    const std::array<DimSize_t, DIM> &dilationDims = create_array<DimSize_t,DIM>(1),
    bool noBias = false);

}  // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::ConvAttr> {
    static const char* const data[];
};
constexpr const char* const EnumStrings<Aidge::ConvAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_CONV_ATTR)
};
}

extern template class Aidge::Conv_Op<1>;
extern template class Aidge::Conv_Op<2>;
extern template class Aidge::Conv_Op<3>;

#undef LIST_CONV_ATTR

#endif /* AIDGE_CORE_OPERATOR_CONV_H_ */
