/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_CONSTANT_OF_SHAPE_H_
#define AIDGE_CORE_OPERATOR_CONSTANT_OF_SHAPE_H_

#include <memory>
#include <functional>
#include <set>
#include <string>

#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/StaticAttributes.hpp"


#define LIST_CONSTANTOFSHAPE_ATTR(X)  \
    X(Value, "value", Tensor)

namespace Aidge {
/**
 * @enum ConstantOfShapeAttr
 * @brief Attributes for the ConstantOfShape operation.
 *
 * - Value: A scalar tensor that holds a fixed datatype value to fill the output tensor.
 */
enum class ConstantOfShapeAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_CONSTANTOFSHAPE_ATTR)
};
}  // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::ConstantOfShapeAttr> {
    static const char* const data[];
};
constexpr const char* const EnumStrings<Aidge::ConstantOfShapeAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_CONSTANTOFSHAPE_ATTR)
};
}

namespace Aidge {
/**
 * @brief This operator's purpose is to generate a tensor of shape given via
 * input and filled with a given value set via attribute.
 */
class ConstantOfShape_Op : public OperatorTensorWithImpl<ConstantOfShape_Op> {
private:
  using Attributes_ = StaticAttributes<ConstantOfShapeAttr, GENERATE_LIST_ATTR_TYPE(LIST_CONSTANTOFSHAPE_ATTR)>;
  template <ConstantOfShapeAttr e>
  using attr = typename Attributes_::template attr<e>;
  const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "ConstantOfShape";
    static constexpr const char* const InputsName[] = {"input"};
    static constexpr const char* const OutputsName[] = {"constant_of_shape"};

  /**
   * @brief constructor for ConstantOfShape_op
   * @param[in] value : a scalar tensor which holds the value that will
   * fill the output tensor
   */
  ConstantOfShape_Op(const Tensor &value = Tensor(0.f));

  /**
   * @brief Copy-constructor. Copy the operator attributes and its output
   * tensor(s), but not its input tensors (the new operator has no input
   * associated).
   * @param op Operator to copy.
   */
  ConstantOfShape_Op(const ConstantOfShape_Op &op);

  /**
   * @brief Compute dimensions for the output Tensor
   * @param allowDataDependency specify if the output shape of this operator
   * depends on its inputs.
   */
  bool forwardDims(bool allowDataDependency = false) override final;

  void setBackend(const std::string &name,
                  DeviceIdx_t device = 0) override final;

  inline std::shared_ptr<Attributes> attributes() const override {
    return mAttributes;
  }
  inline Tensor &value() const noexcept {
    return mAttributes->template getAttr<ConstantOfShapeAttr::Value>();
    }
};

// helper with C-style array instead of std::array for kernel_dims to allow
// automatic template DIM deduction
std::shared_ptr<Node> ConstantOfShape(const Tensor value = Tensor(0.f),
                                             const std::string &name = "");
} // namespace Aidge

#undef LIST_CONSTANTOFSHAPE_ATTR

#endif // AIDGE_CORE_OPERATOR_CONSTANT_OF_SHAPE_H_
