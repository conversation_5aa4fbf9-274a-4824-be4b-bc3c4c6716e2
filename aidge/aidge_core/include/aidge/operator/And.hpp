/********************************************************************************
 * Copyright (c) 2024 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_AND_H_
#define AIDGE_CORE_OPERATOR_AND_H_

#include <memory>
#include <string>
#include <vector>

#include "aidge/utils/Registrar.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {

/**
 * @brief Description of an element-wise And operation on input Tensors,
 * supporting NumPy broadcasting.
 *
 * For each pair of elements x and y from the input Tensors, the function
 * is defined as:
 * `f(x, y) = x && y`
 *
 * Broadcasting adjusts shapes of the input Tensors to make them compatible:
 * - Tensors are aligned from the rightmost dimensions.
 * - Dimensions are compatible if they are equal, one of them is 1, or missing.
 *
 * The output Tensor shape is determined by taking the maximum size along
 * each dimension of the input Tensors after broadcasting.
 *
 * Examples:
 * 1. Input A: (3, 4, 2), Input B: (2), Output: (3, 4, 2)
 * 2. Input A: (1, 5, 3), Input B: (2, 1, 3), Output: (2, 5, 3)
 *
 * @see OperatorTensor
 * @see Registrable
 */
class And_Op : public OperatorTensorWithImpl<And_Op> {
public:
    static constexpr const char* const Type = "And";
    static constexpr const char* const InputsName[] = {"data_input_1", "data_input_2"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    And_Op();

    bool forwardDims(bool allowDataDependency = false) override final;
};

std::shared_ptr<Node> And(const std::string& name = "");

} // namespace Aidge

#endif /* AIDGE_CORE_OPERATOR_AND_H_ */
