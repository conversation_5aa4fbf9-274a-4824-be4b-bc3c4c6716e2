/********************************************************************************
 * Copyright (c) 2024 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_ARGMAX_H_
#define AIDGE_CORE_OPERATOR_ARGMAX_H_

#include <cstdint>    // std::int32_t
#include <memory>
#include <string>
#include <vector>

#include "aidge/graph/Node.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/operator/Producer.hpp"
#include "aidge/utils/ErrorHandling.hpp"
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

#define LIST_ARGMAX_ATTR(X)                       \
    X(Axis, "axis", std::int32_t),                \
    X(KeepDims, "keep_dims", bool),               \
    X(SelectLastIndex, "select_last_index", bool) \

namespace Aidge {
/**
 * @enum ArgMaxAttr
 * @brief Attributes for the ArgMax operation.
 *
 * - Axis: Specifies the dimension along which the ArgMax operation is performed.
 * - KeepDims: Indicates whether reduced dimensions should be kept or removed.
 * - SelectLastIndex: Determines whether to select the first or last index in case of ties.
 */
enum class ArgMaxAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_ARGMAX_ATTR)
};
} // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::ArgMaxAttr> {
    static const char* const data[];
};
/// @brief Provides string representations for the ArgMaxAttr enumeration.
constexpr const char* const EnumStrings<Aidge::ArgMaxAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_ARGMAX_ATTR)
};

}
namespace Aidge {
/**
 * @brief Description of the ArgMax operation on a Tensor.
 *
 * The ArgMax operation identifies the index of the maximum value along a specified axis of a Tensor.
 *
 * The output of the ArgMax operation can retain the dimensionality of the input Tensor or reduce
 * it by removing the specified axis. Additionally, in cases where multiple maximum values exist,
 * the user can specify whether to select the first or the last occurrence of the maximum value.
 *
 * Attributes:
 * - `Axis`: The axis along which the ArgMax operation is performed. For example, if the axis is `0`,
 *   the operation is applied along rows; if it is `1`, it is applied along columns.
 * - `KeepDims`: A boolean indicating whether to retain the reduced axis as a dimension of size `1`
 *   (`true`) or to completely remove it (`false`).
 * - `SelectLastIndex`: A boolean indicating how to handle ties (multiple maximum values along the axis):
 *   - If `true`, the last index of the maximum value is selected.
 *   - If `false`, the first index of the maximum value is selected.
 *
 * @example:
 * - Input Tensor: Shape (2, 3)
 * - ArgMax(axis=1, keep_dims=false, select_last_index=false):
 *   Output Tensor: Shape (2)
 *
 * @see OperatorTensor
 * @see Registrable
 */
class ArgMax_Op : public OperatorTensorWithImpl<ArgMax_Op> {
private:
    using Attributes_ = StaticAttributes<ArgMaxAttr,
                            GENERATE_LIST_ATTR_TYPE(LIST_ARGMAX_ATTR)
                                        >;
    template <ArgMaxAttr e>
    using attr = typename Attributes_::template attr<e>;
    /// Pointer to the attribute storage.
    const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "ArgMax";
    static constexpr const char* const InputsName[] = {"data_input"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    ArgMax_Op() = delete;

    /**
     * @brief Constructs an ArgMax operator.
     * @param[in] axis The axis along which the ArgMax operation is performed.
     * @param[in] keep_dims Whether to retain reduced dimensions with size 1 (`true`) or remove them (`false`).
     * @param[in] select_last_index Whether to select the last occurrence of the maximum value (`true`) or the first (`false`).
     */
    ArgMax_Op(std::int32_t axis = 0, bool keep_dims = true, bool select_last_index = false);

    /**
     * @brief Copy constructor for the ArgMax operator.
     *
     * Creates a new ArgMax_Op instance by copying attributes and output tensors from another
     * instance. The input tensors are not copied, so the new instance has no inputs associated.
     *
     * @param op The ArgMax_Op instance to copy.
     */
    ArgMax_Op(const ArgMax_Op& op);

    /**
     * @brief Performs dimension inference for the ArgMax operation.
     * @param[in] allowDataDependency Whether data dependency is allowed during dimension inference.
     * @return `true` if dimensions were successfully inferred, `false` otherwise.
     */
    bool forwardDims(bool allowDataDependency = false) override final;

    /**
     * @brief Gets the attribute storage for the ArgMax operator.
     * @return A shared pointer to the attribute storage.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Gets a reference to the axis attribute.
     * @return A reference to the axis attribute.
     */
    inline std::int32_t& axis() const noexcept { return mAttributes -> getAttr<ArgMaxAttr::Axis>(); }

    /**
     * @brief Gets a reference to the keepDims attribute.
     * @return A reference to the keepDims attribute.
     */
    inline bool& keepDims() const noexcept { return mAttributes -> getAttr<ArgMaxAttr::KeepDims>(); }

    /**
     * @brief Gets a reference to the selectLastIndex attribute.
     * @return A reference to the selectLastIndex attribute.
     */
    inline bool& selectLastIndex() const noexcept { return mAttributes -> getAttr<ArgMaxAttr::SelectLastIndex>(); }
};

/**
 * @brief Creates an ArgMax operation node.
 *
 * This function constructs a new Node containing an ArgMax_Op operator with the specified
 * attributes.
 *
 * @param[in] axis The axis along which the ArgMax operation is performed.
 * @param[in] keep_dims Whether to retain reduced dimensions with size 1 (`true`) or remove them (`false`).
 * @param[in] select_last_index Whether to select the last occurrence of the maximum value (`true`) or the first (`false`).
 * @param[in] name The name of the Node (optional).
 * @return A shared pointer to the newly created Node.
 */
std::shared_ptr<Node> ArgMax(std::int32_t axis = 0,
                             bool keep_dims = true,
                             bool select_last_index = false,
                             const std::string& name = "");

}  // namespace Aidge

#undef LIST_ARGMAX_ATTR

#endif /* AIDGE_CORE_OPERATOR_ARGMAX_H_ */
