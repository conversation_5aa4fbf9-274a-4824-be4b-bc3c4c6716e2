/***************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ***************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_DEPTHTOSPACE_H_
#define AIDGE_CORE_OPERATOR_DEPTHTOSPACE_H_

#include <array>
#include <memory>
#include <vector>

#include "aidge/graph/Node.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Types.h"
#include "aidge/backend/generic/operator/DepthToSpaceImpl.hpp"


#define LIST_DEPTHTOSPACE_ATTR(X)               \
    X(BlockSize, "block_size", std::uint32_t),    \
    X(Mode, "mode", Aidge::DepthToSpace_Op::Mode)

namespace Aidge {
/**
 * @enum DepthToSpaceAttr
 * @brief Attributes for the DepthToSpace operation.
 *
 * - BlockSize: The block size for rearranging depth to spatial dimensions.
 * - Mode: The mode for depth-to-space transformation.
 */
enum class DepthToSpaceAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_DEPTHTOSPACE_ATTR)
};
}  // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::DepthToSpaceAttr> {
    static const char* const data[];
};
constexpr const char* const EnumStrings<Aidge::DepthToSpaceAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_DEPTHTOSPACE_ATTR)
};
}

namespace Aidge{
/**
 * @class DepthToSpace_Op
 * @brief Represents the DepthToSpace operation to rearrange data from depth to spatial dimensions.
 *
 * This operator splits the depth dimension of a tensor into blocks and distributes them across spatial dimensions.
 *
 * ### Output Shape Calculation
 * Given an input tensor with shape (N, C, H, W):
 * - Block size (B): The depth dimension (C) must be divisible by B^2.
 * - Output shape: (N, C / (B^2), H * B, W * B).
 *
 * ### Modes
 * - `DCR`: Depth-Channel-Row ordering.
 * - `CRD`: Channel-Row-Depth ordering.
 *
 * @see OperatorTensor
 * @see Registrable
 */
class DepthToSpace_Op : public OperatorTensorWithImpl<DepthToSpace_Op, DepthToSpace_OpImpl> {
public:
    /**
     * @enum Mode
     * @brief Defines the modes for depth-to-space transformation.
     */
    enum class Mode { DCR, CRD };

private:
    using Attributes_ = StaticAttributes<DepthToSpaceAttr, GENERATE_LIST_ATTR_TYPE(LIST_DEPTHTOSPACE_ATTR)>;
    template <DepthToSpaceAttr e>
    using attr = typename Attributes_::template attr<e>;
    const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "DepthToSpace";
    static constexpr const char* const InputsName[] = {"data_input"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    DepthToSpace_Op() = delete;

    /**
     * @brief Constructor for DepthToSpace_Op.
     * @param blockSize Size of the blocks to split depth into spatial dimensions.
     * @param mode Depth-to-space transformation mode (DCR or CRD).
     */
    DepthToSpace_Op(const std::uint32_t blockSize, const Mode mode = Mode::CRD);

    /**
     * @brief Copy-constructor.
     * @param op Operator to copy.
     * @details Copies the operator attributes and its output tensor(s), but not
     * its input tensors. The new operator has no associated input.
     */
    DepthToSpace_Op(const DepthToSpace_Op& op);

    bool forwardDims(bool /*allowDataDependency*/ = false) override final;

    /**
     * @brief Get the attributes of the operator.
     * @return Shared pointer to the attributes.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Get the block size for the DepthToSpace operation.
     * @return Block size.
     */
    inline std::uint32_t& blockSize() const { return mAttributes->template getAttr<DepthToSpaceAttr::BlockSize>(); }

    /**
     * @brief Get the mode of the DepthToSpace operation.
     * @return Depth-to-space mode.
     */
    inline Mode& mode() const { return mAttributes->template getAttr<DepthToSpaceAttr::Mode>(); }
};

/**
 * @brief Create a DepthToSpace node.
 * @param blockSize Size of the blocks to split depth into spatial dimensions.
 * @param mode Depth-to-space transformation mode (DCR or CRD).
 * @param name Name of the operator.
 * @return Shared pointer to the created DepthToSpace node.
 */
std::shared_ptr<Node> DepthToSpace(const std::uint32_t blockSize,
                                    const DepthToSpace_Op::Mode mode = DepthToSpace_Op::Mode::CRD,
                                    const std::string& name = "");

}  // namespace Aidge

#undef LIST_DEPTHTOSPACE_ATTR

/**
 * @brief EnumStrings specialization for DepthToSpace_Op::Mode.
 */
template <>
const char* const EnumStrings<Aidge::DepthToSpace_Op::Mode>::data[] = {
    "DCR", "CRD"
};

#endif //AIDGE_CORE_OPERATOR_DEPTHTOSPACE_H_
