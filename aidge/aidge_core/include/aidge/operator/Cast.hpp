/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_CAST_H_
#define AIDGE_CORE_OPERATOR_CAST_H_

#include <cassert>
#include <memory>
#include <vector>

#include "aidge/utils/Registrar.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Types.h"
#include "aidge/backend/generic/operator/CastImpl.hpp"


#define LIST_CAST_ATTR(X)  \
    X(TargetType, "target_type", DataType)

namespace Aidge {
/**
 * @enum CastAttr
 * @brief Enum class defining the attributes for the Cast operator.
 *
 * - TargetType: Specifies the target data type for the cast operation.
 */
enum class CastAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_CAST_ATTR)
};
} // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::CastAttr> {
    static const char* const data[];
};
constexpr const char* const EnumStrings<Aidge::CastAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_CAST_ATTR)
};
}

namespace Aidge {
/**
 * @brief Description of the Cast operation to convert a tensor's data type.
 *
 * The Cast operator changes the data type of input tensor elements to a specified target type.
 *
 * ### Attributes:
 * - `TargetType`: Specifies the data type to which the tensor elements are cast.
 *
 * @see OperatorTensor
 * @see Registrable
 */
class Cast_Op : public OperatorTensorWithImpl<Cast_Op, Cast_OpImpl> {
private:
    using Attributes_ = StaticAttributes<CastAttr,
        GENERATE_LIST_ATTR_TYPE(LIST_CAST_ATTR)
    >;

    template <CastAttr e>
    using attr = typename Attributes_::template attr<e>;

    const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "Cast";
    static constexpr const char* const InputsName[] = {"data_input"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    /**
     * @brief Deleted default constructor.
     */
    Cast_Op() = delete;

    /**
     * @brief Constructor for Cast operator.
     * @param[in] targetType The desired data type to which the tensor will be cast.
     */
    Cast_Op(const DataType targetType);

    /**
     * @brief Copy-constructor. Copy the operator attributes and its output tensor(s), but not its input tensors (the new operator has no input associated).
     * @param op Operator to copy.
     */
    Cast_Op(const Cast_Op& op);

    /**
     * @brief Forward the data type.
     * @return True if successful, false otherwise.
     */
    bool forwardDType() override final;

    /**
     * @brief Access the attributes of the operator.
     * @return A shared pointer to the attributes.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Get the target data type for the cast operation.
     * @return Reference to the target data type.
     */
    inline DataType& targetType() const { return mAttributes->template getAttr<CastAttr::TargetType>(); }

    /**
     * @brief Sets the data type of the operator's tensors.
     * @details This method needs to be overwritten because Cast_Op's output type needs to be set from targetType
     * @param dataType Data type to set.
     */
    virtual void setDataType(const DataType& dataType) const override;
};

/**
 * @brief Factory function to create a Cast node.
 * @param[in] targetType The desired data type to cast to.
 * @param[in] name Name of the operator node.
 * @return A shared pointer to the created Cast node.
 */
std::shared_ptr<Node> Cast(const DataType targetType, const std::string& name = "");

}  // namespace Aidge

#undef LIST_CAST_ATTR

#endif /* AIDGE_CORE_OPERATOR_CAST_H_ */
