/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_AVGPOOLING_H_
#define AIDGE_CORE_OPERATOR_AVGPOOLING_H_

#include <array>
#include <string>
#include <vector>

#include "aidge/graph/Node.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/utils/ArrayHelpers.hpp"
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Rounding.hpp"
#include "aidge/utils/Types.h"

#define LIST_AVGPOOLING_ATTR(X)                     \
    X(KernelDims, "kernel_dims", sizeArr_t<DIM>),   \
    X(StrideDims, "stride_dims", sizeArr_t<DIM>),   \
    X(Dilations,  "dilations",   sizeArr_t<DIM>),   \
    X(CeilMode,   "ceil_mode",   bool),   \
    X(RoundingMode,   "rounding_mode",   Aidge::RoundingMode)

namespace Aidge {

/**
 * @enum Attr
 * @brief Attributes defining the configuration of a MaxPooling Operator.
 *
 * - **KernelDims**: Kernel dimensions specifying the size of the pooling window for each spatial dimension.
 *   Must be an array of positive integers. Common examples include [2,2] or [3,3].
 * - **StrideDims**: Stride dimensions for sliding the pooling window across the input.
 *   The stride specifies how much the window moves after each operation.
 *   Must be an array of positive integers. For example, [1,1] or [2,2].
 * - **Dilations**: Dilation along each spatial axis. Default value is 1 for all axes.
 *   Must be an array of positive integers. For example, [1,1].
 * - **CeilMode**: Flag indicating whether to use ceil or floor when calculating output size.
 *   - `true`: Use `ceil` for output size calculation.
 *   - `false`: Use `floor` for output size calculation.
 */
enum class AvgPoolingAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_AVGPOOLING_ATTR)
};
} // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::AvgPoolingAttr> {
    static const char* const data[];
};
/// @brief String representation of the AvgPooling attributes.
constexpr const char* const EnumStrings<Aidge::AvgPoolingAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_AVGPOOLING_ATTR)
};
}

namespace Aidge {
template <DimIdx_t DIM> struct AvgPooling_Op_Type {};
template <> struct AvgPooling_Op_Type<1> { static constexpr const char* const value = "AvgPooling1D"; };
template <> struct AvgPooling_Op_Type<2> { static constexpr const char* const value = "AvgPooling2D"; };
template <> struct AvgPooling_Op_Type<3> { static constexpr const char* const value = "AvgPooling3D"; };
template <> struct AvgPooling_Op_Type<4> { static constexpr const char* const value = "AvgPooling4D"; };

/**
 * @brief Class representing an Average Pooling operation.
 *
 * The AvgPooling operation computes the average value within sliding windows of specified size
 * (kernel dimensions) over the input tensor. The stride dimensions determine how the window
 * moves across the input. The dilation parameter allows spacing between kernel elements, and
 * `ceil_mode` determines whether to use ceiling instead of floor when computing the output shape.
 * This operation is commonly used in neural networks to reduce spatial dimensions while preserving features.
 *
 * @tparam DIM Number of dimensions for the pooling operation.
 *
 * ### Output Shape Calculation
 * - If `ceil_mode` is false:
 *   `output_size = floor((input_size - dilation * (kernel_size - 1) - 1) / stride + 1)`
 * - If `ceil_mode` is true:
 *   `output_size = ceil((input_size - dilation * (kernel_size - 1) - 1) / stride + 1)`
 *
 * @example Example usage:
 * - Input shape: (1, 3, 32, 32) // Batch size 1, 3 channels, 32x32 spatial dimensions
 * - KernelDims: (2, 2)
 * - StrideDims: (2, 2)
 * - Dilation: (1, 1)
 * - CeilMode: false
 * - Output shape: (1, 3, 16, 16)
 *
 * @see OperatorTensor
 * @see Registrable
 */

template <DimIdx_t DIM>
class AvgPooling_Op : public OperatorTensorWithImpl<AvgPooling_Op<DIM>> {
private:
    /**
     * @brief Static attributes representing kernel and stride dimensions.
     */
    using Attributes_ = StaticAttributes<AvgPoolingAttr,
                            GENERATE_LIST_ATTR_TYPE(LIST_AVGPOOLING_ATTR)
                                             >;
    template <AvgPoolingAttr e>
    using attr = typename Attributes_::template attr<e>;

    /**
     * @brief Shared pointer to the attributes of the operation.
     */
    const std::shared_ptr<Attributes_> mAttributes;

public:
    using OperatorTensorWithImpl<AvgPooling_Op<DIM>>::getInput;
    using OperatorTensorWithImpl<AvgPooling_Op<DIM>>::getOutput;
    using OperatorTensorWithImpl<AvgPooling_Op<DIM>>::OperatorTensorWithImpl;
    using OperatorTensorWithImpl<AvgPooling_Op<DIM>>::inputsAssociated;
    using OperatorTensorWithImpl<AvgPooling_Op<DIM>>::mOutputs;
    using OperatorTensorWithImpl<AvgPooling_Op<DIM>>::dimsForwarded;

    static constexpr const char* const Type = AvgPooling_Op_Type<DIM>::value;
    static constexpr const char* const InputsName[] = {"data_input"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    /**
     * @brief Default constructor is deleted.
     */
    AvgPooling_Op() = delete;

    /**
     * @brief Constructs an AvgPooling operation with specified kernel and stride dimensions.
     * @param[in] kernel_dims Size of the pooling window for each spatial dimension.
     * @param[in] stride_dims Step size (stride) for sliding the pooling window across the input dimensions.
     * Defaults to 1 for each dimension.
     * @param[in] dilations Spatial dilations for the pooling operation.
     * @param[in] ceil_mode Indicates whether to use ceil mode for output size calculation.
     */
    AvgPooling_Op(const std::array<DimSize_t, DIM> &kernel_dims,
                            const std::array<DimSize_t, DIM> &stride_dims = create_array<DimSize_t, DIM>(1),
                            const std::array<DimSize_t, DIM> &dilations = create_array<DimSize_t, DIM>(1),
                            bool ceil_mode = false,
                            RoundingMode roundingMode = RoundingMode::HalfAwayFromZero);

    /**
     * @brief Copy-constructor.
     * @param[in] op AvgPooling_Op to copy.
     * @details Copies the operator attributes and its output tensor(s), but not
     * its input tensors. The new operator has no associated input.
     */
    AvgPooling_Op(const AvgPooling_Op<DIM>& op);

    /**
     * @brief Calculates the output dimensions based on the input dimensions and operator attributes.
     * @param[in] allowDataDependency If true, considers data-dependent operations. Defaults to false.
     * @return True if the dimensions are successfully calculated.
     */
    bool forwardDims(bool /*allowDataDependency*/ = false) override final;

    /**
     * @brief Computes the receptive field of the operator.
     * @param[in] firstEltDims Dimensions of the first element.
     * @param[in] outputDims Dimensions of the output tensor.
     * @param[in] outputIdx Index of the output tensor. Defaults to 0.
     * @return A vector of pairs representing the receptive fields.
     */
    std::vector<std::pair<std::vector<DimSize_t>, std::vector<DimSize_t>>>
    computeReceptiveField(const std::vector<DimSize_t>& firstEltDims,
                          const std::vector<DimSize_t>& outputDims,
                          const IOIndex_t outputIdx = 0) const override final;

    /**
     * @brief Get the number of input channels.
     * @return The number of input channels.
     * @throws std::runtime_error If the operator has no associated weight tensor.
     */
    DimSize_t nbChannels() const;

    /**
     * @brief Accessor for the operation attributes.
     * @return Shared pointer to the attributes.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Accessor for the stride dimensions.
     * @return An array representing the stride dimensions.
     */
    inline std::array<DimSize_t, DIM>& strideDims() const { return mAttributes->template getAttr<AvgPoolingAttr::StrideDims>(); }

    /**
     * @brief Accessor for dilations.
     * @return An array representing spatial dilations.
     */
    inline std::array<DimSize_t, DIM>& dilations() const { return mAttributes->template getAttr<AvgPoolingAttr::Dilations>(); }

    /**
     * @brief Accessor for kernel dimensions.
     * @return An array representing kernel dimensions.
     */
    inline std::array<DimSize_t, DIM>& kernelDims() const { return mAttributes->template getAttr<AvgPoolingAttr::KernelDims>(); }

    /**
     * @brief Accessor for ceil mode flag.
     * @return Boolean value indicating whether ceil mode is enabled.
     */
    inline bool& ceilMode() const { return mAttributes->template getAttr<AvgPoolingAttr::CeilMode>(); }

    /**
     * @brief Accessor for rounding mode mode flag.
     * @return Rounding mode (used only for integer outputs).
     */
    inline RoundingMode& roundingMode() const { return mAttributes->template getAttr<AvgPoolingAttr::RoundingMode>(); }
};

/**
 * @brief Creates an AvgPooling operator node.
 * @tparam DIM Number of dimensions for the pooling operation.
 * @param[in] kernel_dims Size of the pooling window for each spatial dimension.
 * @param[in] name Name of the operator node. Defaults to an empty string.
 * @param[in] stride_dims Step size (stride) for sliding the pooling window across the input dimensions. Defaults to 1 for each dimension.
 * @param[in] dilations Spatial dilations for the pooling operation.
 * @param[in] ceil_mode Indicates whether to use ceil mode for output size calculation.
 * @return A shared pointer to the created operator node.
 */
template <std::array<DimSize_t, 1>::size_type DIM>
std::shared_ptr<Node> AvgPooling(const std::array<DimSize_t, DIM> &kernel_dims,
                                 const std::string& name = "",
                                 const std::array<DimSize_t, DIM> &stride_dims = create_array<DimSize_t,DIM>(1),
                                 const std::array<DimSize_t, DIM> &dilations = create_array<DimSize_t,DIM>(1),
                                 bool ceil_mode=false,
                                 RoundingMode roundingMode = RoundingMode::HalfAwayFromZero);

/**
 * @brief Overload of AvgPooling for C-style arrays.
 * @tparam DIM Number of dimensions for the pooling operation.
 * @param[in] kernel_dims C-style array specifying the kernel dimensions.
 * @param[in] name Name of the operator node. Defaults to an empty string.
 * @param[in] stride_dims Step size (stride) for sliding the pooling window across the input dimensions. Defaults to 1 for each dimension.
 * @param[in] dilations Spatial dilations for the pooling operation.
 * @param[in] ceil_mode Indicates whether to use ceil mode for output size calculation.
 * @return A shared pointer to the created operator node.
 */
template <DimSize_t DIM>
inline std::shared_ptr<Node> AvgPooling(
    DimSize_t const (&kernel_dims)[DIM],
    const std::string& name = "",
    const std::array<DimSize_t, DIM> &stride_dims = create_array<DimSize_t,DIM>(1),
    const std::array<DimSize_t, DIM> &dilations = create_array<DimSize_t,DIM>(1),
    bool ceil_mode=false,
    RoundingMode roundingMode = RoundingMode::HalfAwayFromZero) {
    static_assert(DIM<=MaxDim,"Too many kernel dimensions required by AvgPooling, not supported");
    return AvgPooling(to_array(kernel_dims), name, stride_dims, dilations, ceil_mode, roundingMode);
}
}  // namespace Aidge

/**
 * @brief Explicit template instantiations for AvgPooling_Op with 1 to 4 dimensions.
 */
extern template class Aidge::AvgPooling_Op<1>;
extern template class Aidge::AvgPooling_Op<2>;
extern template class Aidge::AvgPooling_Op<3>;
extern template class Aidge::AvgPooling_Op<4>;

#undef LIST_AVGPOOLING_ATTR

#endif /* AIDGE_CORE_OPERATOR_AVGPOOLING_H_ */
