/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_CRYPTOHASH_H_
#define AIDGE_CORE_OPERATOR_CRYPTOHASH_H_

#include <memory>
#include <string>
#include <vector>

#include "aidge/utils/Registrar.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {
/**
 * @enum CryptoHashAttr
 * @brief Attributes for the CryptoHash operator.
 */
enum class CryptoHashAttr {
    CryptoHashFunction ///< Cryptographic hash function to use.
};

/**
 * @enum CryptoHashFunction
 * @brief Cryptographic hash function.
 */
enum class CryptoHashFunction {
    SHA256  ///< SHA256
};

/**
 * @brief Produce a cryptographic hash from the input.
 * 
 * @see OperatorTensor
 * @see Registrable
 */
class CryptoHash_Op : public OperatorTensorWithImpl<CryptoHash_Op> {
private:
    using Attributes_ = StaticAttributes<CryptoHashAttr, CryptoHashFunction>;
    template <CryptoHashAttr e> using attr = typename Attributes_::template attr<e>;
    const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "CryptoHash";
    static constexpr const char* const InputsName[] = {"data_input"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    CryptoHash_Op();

    /**
     * @brief Copy-constructor.
     * @param op CryptoHash_Op to copy.
     * @details Copies the operator attributes and its output tensor(s), but not
     * its input tensors. The new operator has no associated input.
     */
    CryptoHash_Op(const CryptoHash_Op& op);

    bool forwardDims(bool allowDataDependency = false) override final;

    /**
     * @brief Get the attributes of the operator.
     * @return A shared pointer to the attributes.
     */
    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    /**
     * @brief Get or modify the `crypto_hash_function` attribute.
     * @return Reference to the `crypto_hash_function` attribute.
     */
    inline CryptoHashFunction& cryptoHashFunction() const noexcept { return mAttributes->getAttr<CryptoHashAttr::CryptoHashFunction>(); }
};

std::shared_ptr<Node> CryptoHash(const std::string& name = "");

} // namespace Aidge

namespace {

/**
 * @brief EnumStrings specialization for CryptoHashAttr.
 */
template <>
const char* const EnumStrings<Aidge::CryptoHashAttr>::data[] = {
    "crypto_hash_function"
};

/**
 * @brief EnumStrings specialization for CryptoHashFunction.
 */
template <>
const char* const EnumStrings<Aidge::CryptoHashFunction>::data[] = {
    "sha256"
};

}  // namespace

#endif /* AIDGE_CORE_OPERATOR_CRYPTOHASH_H_ */
