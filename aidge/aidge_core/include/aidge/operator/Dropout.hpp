/********************************************************************************
 * Copyright (c) 2025 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 ********************************************************************************/

#ifndef AIDGE_CORE_OPERATOR_DROPOUT_H_
#define AIDGE_CORE_OPERATOR_DROPOUT_H_

#include <memory>
#include <vector>

#include "aidge/graph/Node.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/StaticAttributes.hpp"
#include "aidge/utils/Types.h"

#define LIST_DROPOUT_ATTR(X) \
    X(Probability, "probability", float)

namespace Aidge {

// First, declare an Enum for the attributes. Dropout has one attribute: Probability
enum class DropoutAttr {
    GENERATE_LIST_ATTR_ENUM(LIST_DROPOUT_ATTR)
};

// Define the Dropout_Op class, inheriting from OperatorTensor and Registrable
class Dropout_Op : public OperatorTensorWithImpl<Dropout_Op> {
private:
    using Attributes_ = StaticAttributes<DropoutAttr,  GENERATE_LIST_ATTR_TYPE(LIST_DROPOUT_ATTR)>;
    template <DropoutAttr e> using attr = typename Attributes_::template attr<e>;
    const std::shared_ptr<Attributes_> mAttributes;

public:
    static constexpr const char* const Type = "Dropout";
    static constexpr const char* const InputsName[] = {"data_input", "probability"};
    static constexpr const char* const OutputsName[] = {"data_output"};

    Dropout_Op(float probability = 0.5f);

    Dropout_Op(const Dropout_Op& op);

    /**
     * @brief Propagates dimensions through the Dropout operation.
     * This function updates the output Tensors' dimensions based on the input Tensors.
     * If an optional input is provided, it may override an attribute,
     * provided data dependency is allowed.
     *
     * If the 'Probability' optional input is provided but undefined and `allowDataDependency` is true, it defaults to 0.0.
     *
     * @param allowDataDependency If true, the optional input can modify attributes.
     * @return true if dimension propagation was successful, false otherwise.
     *
     * @warning This function does not check the optional input type before coping it.
     */
    bool forwardDims(bool allowDataDependency = true) override final;

    inline std::shared_ptr<Attributes> attributes() const override { return mAttributes; }

    inline float& probability() const noexcept { return mAttributes -> getAttr<DropoutAttr::Probability>(); }

private:
    void checkProbability() const;
};

// Function to create a Dropout node
std::shared_ptr<Node> Dropout(float probability = 0.5f, const std::string& name = "");

} // namespace Aidge

namespace {
template <>
struct EnumStrings<Aidge::DropoutAttr> {
    static const char* const data[];
};
constexpr const char* const EnumStrings<Aidge::DropoutAttr>::data[] = {
    GENERATE_LIST_ATTR_STR(LIST_DROPOUT_ATTR)
};
}

#endif /* AIDGE_CORE_OPERATOR_DROPOUT_H_ */