/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_SCHEDULER_SEQUENTIALSCHEDULER_H_
#define AIDGE_CORE_SCHEDULER_SEQUENTIALSCHEDULER_H_

#include <memory>
#include <vector>

#include "aidge/data/Tensor.hpp"
#include "aidge/graph/GraphView.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/scheduler/Scheduler.hpp"

namespace Aidge {
/**
 * Multi-threaded parallel scheduler with dynamic scheduling.
*/
class SequentialScheduler : public Scheduler {
public:
    SequentialScheduler(std::shared_ptr<GraphView> graphView, bool resetCPModel = true, std::shared_ptr<Node> upperNode = nullptr)
        : Scheduler(graphView, resetCPModel, upperNode),
          mSchedulingPolicy(SchedulingPolicy::Default)
    {
        // ctor
    };

    ~SequentialScheduler() = default;

public:
    inline void setSchedulingPolicy(SchedulingPolicy policy) {
        mSchedulingPolicy = policy;
    }

    /**
     * @brief Get the static scheduling sequential order of nodes following the
     * current scheduling policy.
     * @param step The step of the static schedule to retrieve (default is 0).
     * @return Vector of shared pointers to Nodes in their scheduled order.
     */
    std::vector<std::shared_ptr<Node>> getSequentialStaticScheduling(std::size_t step = 0) const;

    /**
     * Generate the memory layout for the static scheduling following the
     * current scheduling policy.
     * @param incProducers If true, include the producers in the memory layout.
     * @param wrapAroundBuffer If true, allow wrapping in memory planes.
    */
    MemoryManager generateMemory(bool incProducers = false, bool wrapAroundBuffer = false) const;

    /**
     * Generate the memory layout for the static scheduling following the
     * current scheduling policy, with auto-concatenation: the Concat operator
     * is replaced by direct allocation when possible.
     * @param incProducers If true, include the producers in the memory layout.
     * @param wrapAroundBuffer If true, allow wrapping in memory planes.
    */
    MemoryManager generateMemoryAutoConcat(bool incProducers = false, bool wrapAroundBuffer = false) const;

    /**
     * @brief Run the provided Computational Graph with a batch of data
     */
    virtual void forward(bool forwardDims = true, const std::vector<std::shared_ptr<Aidge::Tensor>>& data = {});

    /**
     * @brief Run backpropagation on the Computational Graph
     *
     * Executes the backward pass through the nodes in reverse order of the forward pass.
     * Nodes will be skipped during the backward pass in two cases:
     * 1. If they are not conditionally required (as determined by isConditionalNodeRequired)
     * 2. If they have the 'skipBackward' attribute set to true
     *
     */
    void backward();

private:
    SchedulingPolicy mSchedulingPolicy;
};
} // namespace Aidge

#endif /* AIDGE_CORE_SCHEDULER_SEQUENTIALSCHEDULER_H_ */
