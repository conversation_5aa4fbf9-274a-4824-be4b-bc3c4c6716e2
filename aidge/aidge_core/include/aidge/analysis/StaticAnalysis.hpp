
/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CORE_ANALYSIS_STATICANALYSIS_H_
#define AIDGE_CORE_ANALYSIS_STATICANALYSIS_H_

#include <cstddef>  // std::size_t
#include <memory>
#include <string>

#include "aidge/analysis/OperatorStats.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/graph/GraphView.hpp"
#include "aidge/operator/Operator.hpp"
#include "aidge/utils/Registrar.hpp"

namespace Aidge {
/**
 * @brief Base class to compute statistics from a GraphView
 *
 */
class StaticAnalysis : public std::enable_shared_from_this<StaticAnalysis> {
public:
    StaticAnalysis() = delete;
    StaticAnalysis(std::shared_ptr<GraphView> graph);

    virtual ~StaticAnalysis();

    inline const std::shared_ptr<GraphView> getGraph() const noexcept { return mGraph; }

    /**
     * @brief Get the number of parameters associated to a node. This includes
     * all Producers directly connected to the node's inputs as well as all
     * internal Producers (in case of a meta operator).
     *
     * Note: this function does not check if parameters are shared between
     * several nodes or not. This means that simply adding parameters count from
     * several nodes may lead to a higher number of parameters than in reality
     * if some of them are shared.
     *
     * @param node Node
     * @return std::size_t Number of parameters
     */
    virtual std::size_t getNbParams(std::shared_ptr<Node> node) const;

    /**
     * @brief Get the total parameters memory size, in bits, associated to a node.
     * This includes all Producers directly connected to the node's inputs as
     * well as all internal Producers (in case of a meta operator).
     *
     * Note: this function does not check if parameters are shared between
     * several nodes or not. This means that simply adding parameters size from
     * several nodes may lead to a higher parameter size than in reality
     * if some of them are shared.
     *
     * @param node Node
     * @return std::size_t Total parameters memory, in bits
     */
    virtual std::size_t getParamsSize(std::shared_ptr<Node> node) const;

    std::size_t getNbArithmOps() const;
    std::size_t getNbLogicOps() const;
    std::size_t getNbCompOps() const;
    std::size_t getNbNLOps() const;
    std::size_t getNbOps() const;
    std::size_t getNbArithmIntOps() const;
    std::size_t getNbArithmFpOps() const;
    std::size_t getNbMACOps() const;
    virtual void summary(bool incProducers = false) const;

protected:
    const std::shared_ptr<GraphView> mGraph;

    std::size_t accumulate(std::size_t (OperatorStats::*func)() const) const;
};
}

#endif /* AIDGE_CORE_ANALYSIS_STATICANALYSIS_H_ */
