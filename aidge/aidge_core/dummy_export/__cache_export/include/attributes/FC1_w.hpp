#ifndef EXPORT_PARAMETERS_FC1_W_H
#define EXPORT_PARAMETERS_FC1_W_H

#include <aidge/data/Tensor.hpp>
#include <memory>

std::shared_ptr<Aidge::Tensor> FC1_1 = std::make_shared<Aidge::Tensor>(Aidge::Array2D<float, 32, 64> {
{{  2.21221,  1.16308,  0.77400,  0.48380,  1.04344,  0.29956,  1.18393,  0.15303,  1.89171, -1.16881, -1.23474,  1.55807, -1.77103, -0.54594, -0.45138, -2.35563,  0.57938,  0.54144, -1.85608,  2.67851, -1.97688,  1.25463, -0.20802, -0.54877,  0.24442, -0.68106, -0.03716, -0.13532, -0.48775,  0.37723, -0.02262,  0.41016,  0.57461,  0.57127,  1.46613, -2.75796,  0.68629,  1.07628,  0.35496, -0.61413,  1.07317,  1.83076,  0.12017, -1.14681, -0.97111,  0.05384, -0.77570, -2.50748, -0.78822, -0.59165,  0.74177,  0.85860, -1.47344, -0.22794, -1.07309,  0.20131, -1.04248,  0.35005, -1.32788,  0.53605, -1.47497,  1.51944, -0.52414,  1.90409},
 {  1.26626, -1.57344,  0.89506, -0.14008, -0.60159,  0.29670,  1.20406,  1.31120, -0.97122,  0.50359, -0.58256, -1.18945,  0.37171, -0.55021,  0.93001, -1.59188, -1.42258, -1.10819, -0.51762,  0.07872,  2.00883, -0.91856,  0.28631, -0.74571,  0.56046, -1.20809,  0.96976,  1.81402, -0.52854, -1.52274, -1.88909, -2.51524,  0.65479, -1.35493, -0.45481, -0.95748,  0.32511, -0.72486, -1.30023,  1.11196,  0.36793, -0.47827,  1.45343, -1.17395,  0.24154, -0.79218,  0.47898,  0.93210,  0.96885, -3.15577, -1.02182,  2.19353, -0.06813, -0.53859, -0.31868, -0.86113, -0.17634, -1.88152,  0.35655, -0.72057,  0.74419, -0.35602,  0.77874, -0.15964},
 {  0.60878,  1.79745,  1.07418,  0.19595,  0.06643, -1.73764,  0.84870,  0.04735, -0.80038,  0.14581, -0.16882,  0.32605,  0.93633,  0.45788,  0.35744, -0.89426,  0.77933,  0.49384, -1.01031, -0.90434, -0.39157, -1.21408,  1.31662,  2.15641, -0.43293,  1.09382,  0.71536,  1.82714,  0.92542, -1.04467, -0.90495,  1.00622, -0.07468,  0.51742,  0.82254, -0.80693, -1.87851,  1.37690,  0.88588,  0.20589,  1.91187,  0.99435,  0.33342, -0.23581,  0.11884,  0.29882, -1.91989, -1.99821, -0.67558,  0.35114,  1.00775, -0.76447, -0.35391,  1.25944,  1.63239, -0.96297, -0.33355,  0.50805, -1.73784,  0.75617,  0.77374,  1.68393,  0.89127,  1.25736},
 {  3.29045,  0.13123, -1.19762,  1.64030,  1.89389, -1.21380, -0.56693,  2.39997, -0.02392, -0.38509,  1.07567, -0.98780, -1.26881,  0.95859, -0.83141, -1.49765,  1.39457,  0.66052,  1.00435, -0.18904, -0.55172, -0.27303, -1.91536,  0.91815,  1.23910,  2.17306, -0.47186,  0.07520,  0.21996,  0.07186, -1.32469,  1.12562, -1.44675, -0.35596, -0.77169, -0.25377,  0.30545, -0.04047, -0.30367,  0.49660,  0.16687,  1.38010, -0.46042,  1.80271, -0.22734,  0.64572, -0.26001,  1.81153,  0.03087, -1.25296, -1.11660,  0.38731,  0.30455, -0.41055, -0.37035,  1.28454, -1.09419,  0.21001, -0.43584, -0.08666, -0.93973,  1.46422, -2.24651, -1.30581},
 { -0.34305,  0.93444, -0.40720,  0.53809,  0.50870, -0.16038,  0.91512,  0.84188, -0.49178, -1.00554, -1.55609,  3.13222, -0.15749, -0.43590, -0.52665, -0.49295, -0.17885,  1.43719,  0.43165, -0.31814,  0.46760, -0.16282,  0.17287,  0.68361,  0.76159,  1.61067,  0.00010, -1.26422, -0.51431,  0.13895,  1.71985,  0.66712, -0.11494,  0.21445,  1.56177,  0.99385,  1.11169,  0.10874,  1.49358,  0.53534,  0.53680, -1.86105, -1.32519, -0.46896, -0.64744, -0.67683, -0.55203, -0.61593,  0.10064,  0.64282, -0.33182,  1.65978,  0.49453, -0.01003,  0.71980, -1.08675, -0.32953, -0.19519, -0.51758, -2.79099,  0.72313, -0.35720,  1.44181, -1.49806},
 {  0.17372, -1.30963,  0.07968, -0.14142, -0.23906,  2.00367,  1.93971, -0.50719,  2.30716,  0.78840,  0.30559,  0.56460, -0.01646,  0.26467, -0.22913, -1.47478,  0.67586,  0.27119, -0.63455, -0.41398,  0.47299,  0.32027,  0.63768, -0.28379,  1.55080, -1.23966, -1.72708,  0.87003,  0.07617,  1.27926, -0.38289,  0.75839, -0.53819, -1.63486,  0.25525,  0.19743,  0.56403, -0.98571, -0.05929,  0.61197, -0.17426,  1.96916,  0.65265, -0.64305,  0.58428,  0.35468, -0.83858, -1.10752, -2.76255,  1.77993, -0.47830, -0.40403,  1.61741, -0.52861,  1.47949, -1.27279, -1.47610, -0.18857, -0.83653, -0.59329,  1.28774,  0.36334,  0.40237,  0.59253},
 {  0.34593, -0.75671, -1.78783,  0.13927, -0.17871, -0.20226,  0.98685,  1.04663, -0.82165, -2.31760,  1.18866,  0.22843, -0.51752, -0.55537, -0.21166, -0.22437, -1.07040, -0.03408,  1.33647, -0.86891,  0.77280,  0.83015, -0.36801,  0.22462, -0.13839,  0.19773,  0.17645, -0.29490,  1.68344,  0.37922, -0.55152,  0.10254, -1.03103, -1.00571,  0.77013, -1.12598,  0.03745, -0.32547,  0.96050,  0.49005,  0.97980, -1.05094, -1.24471,  0.33783,  0.02099, -0.73851,  2.64973, -0.47339,  0.26951,  0.95299, -1.13895, -0.65459, -0.05305,  0.54617,  0.44457, -0.31482, -0.01124,  0.65329,  0.11693,  0.23943, -0.06616,  1.43014,  1.42808,  0.89327},
 { -0.00535,  1.07622,  0.80104, -0.48121, -0.36084,  0.41194,  1.32260,  0.57039,  0.90337, -0.38090,  0.40487, -0.50801,  1.63764, -0.13969, -0.18017, -0.00095, -1.34553,  0.81409, -1.20510, -1.76932, -0.21270, -1.07602, -0.55615,  0.88617, -0.39793, -1.24831, -1.04972,  0.03132, -0.19883, -0.85172,  0.20714,  1.08402, -0.03866,  0.11690, -0.18257,  0.83748, -0.63579,  1.23811, -1.00193, -1.66343, -0.23578, -0.49840,  1.39689, -0.47080, -0.38969, -0.81984,  0.01970, -0.28763, -0.74443,  1.03075, -0.15310, -0.25814,  0.26538, -0.61389,  1.02410,  0.56759, -0.41213, -1.54664,  1.52279, -0.47991,  1.24897,  0.21739, -0.33278,  0.99188},
 { -0.48363,  0.27216, -0.92140, -0.86779,  0.25384, -1.01749, -0.03598, -0.68016, -0.11660, -1.91736, -0.29314, -1.68092,  1.48246, -2.64387, -0.58981,  1.86432,  0.81097,  0.44989, -0.52307, -1.70122,  1.12915, -0.41478,  0.03927, -1.34026,  0.16557, -0.45846, -0.97811,  0.65672,  0.53105,  0.42618,  0.70178,  1.07344, -0.74952, -0.56063, -0.63423, -0.18606, -0.12226,  0.02184,  0.54300, -0.31465, -0.94694, -0.33365,  0.59643, -0.64718,  0.79070, -0.07466, -0.04377,  1.42907, -0.05834, -0.80917,  1.03049,  0.05072, -0.11668, -1.94498, -1.79335,  1.62774, -0.36966,  2.17769,  1.65158, -2.03699, -1.24017,  0.03652, -1.15005,  0.11681},
 {  1.10500,  1.34106,  0.30968, -1.75593, -0.37868, -0.80625, -1.05498, -0.32076, -0.26674, -0.22519,  0.16912,  0.86569, -0.88539,  0.09512,  0.87076, -0.09139,  1.40119, -0.19310,  0.62163,  0.36950, -0.81660, -0.20282, -0.15917,  0.87267, -0.27311,  1.93801,  0.68470,  1.70431,  1.20404, -0.95925,  1.35970,  0.79950,  0.77691, -0.75658, -0.64094,  0.99089,  0.25686, -3.13714,  0.54560,  0.10941,  0.18105,  1.12029, -0.91403, -0.09713,  0.13380,  0.49690,  1.07026,  0.22333, -0.36828,  0.34505,  1.43268,  1.38218,  0.55863, -0.12628, -1.47976, -2.13627,  0.87901, -0.93159,  0.46016,  0.66305, -0.78269, -2.01707, -1.21978, -1.61502},
 {  0.02315, -0.35160,  0.38455, -0.35049,  0.86108, -2.34388,  0.20672,  0.49228,  0.22711, -0.83289, -0.66996, -0.06916, -0.60928, -0.25537, -0.75273, -0.76541,  1.09408,  0.36886,  0.02483, -0.51822,  0.00955, -0.35396,  1.89739, -0.90169,  0.07714, -0.74717,  0.40762, -1.41808,  0.82382, -0.75003, -0.31846, -0.94928, -0.00572, -0.31106, -1.37509,  0.63803, -0.07048,  0.26597, -1.67743, -0.80321,  0.33193, -0.33746,  1.14506,  0.51752,  2.24886, -0.15570, -0.18532, -0.43022,  0.63395,  0.59488, -0.37305, -0.18377,  0.78124, -1.10046, -1.12790,  1.53675,  0.69496, -1.31767,  0.55242,  0.21464,  1.17212,  0.05732, -0.08395, -2.74432},
 { -2.08367,  1.57902,  0.84687, -0.55909,  1.02597,  0.88966, -1.33733, -2.15423,  0.28327, -0.08425, -0.44803,  1.07606, -0.17183,  0.71263, -0.29177, -1.53215,  0.52563, -0.18208, -0.58426,  0.75803, -0.01193,  0.04897, -0.68381, -0.36712, -0.80811, -0.74519, -0.02595, -0.97467, -0.66727, -1.25363, -0.93317,  1.54306, -0.55819,  0.33347,  0.30068,  0.87532, -0.65006, -0.40287, -1.47911, -1.42251, -1.49008, -1.06671,  0.81872,  1.13087,  0.72607, -0.64415,  0.01556, -1.29771, -1.80779, -0.44990,  0.67853, -0.34375, -0.43898,  1.00518,  0.78918, -0.86819,  2.96358,  0.12791,  0.76286,  2.10684,  0.05918,  1.10662, -0.17385,  0.50608},
 { -0.80323,  0.45649,  1.79265, -0.41310,  0.17462, -0.67058,  1.00474, -1.21105, -0.17717, -1.17356,  1.70412, -0.71713, -0.31520, -0.95514, -0.84464,  0.21987,  0.43947, -0.11190,  0.38265,  0.03741, -0.71534, -0.45823, -1.51817,  0.58029, -0.18007,  0.25114,  1.54185, -1.79951,  0.41588,  0.04552, -0.93543, -0.94102,  0.47638, -0.04515, -0.34668, -0.07181,  0.46880, -0.30394,  1.86876,  0.05255, -1.11533, -0.95167,  0.82308, -0.26465, -0.46029,  0.02428,  0.68186,  1.36004, -1.23176, -1.20311, -0.18156,  0.19849, -0.81377, -1.17884,  0.97245, -0.13457,  0.03047, -0.96758,  0.15276, -2.17768, -0.67858,  1.43962,  0.32967,  1.97780},
 {  0.54775,  0.15074,  0.59761,  0.32025,  0.62907, -1.04804,  1.22692, -1.39702,  0.53095,  1.93491, -0.36058,  0.60155, -1.16424,  0.14458,  0.72586, -0.44268,  0.08320, -0.28939, -1.01653,  1.06716, -0.01812, -0.22410, -0.24187, -0.06477,  2.52264,  1.32669, -0.21465,  0.65254,  0.00586, -0.88812, -1.11131, -0.30178, -0.06011, -0.38383,  0.69249, -0.26833,  0.66317, -1.00834, -1.68279,  0.61749,  0.37652,  1.88165,  1.19898,  0.91981, -0.13437,  0.54271, -0.63640,  2.07523,  0.36775,  0.07821,  1.17551,  0.31249, -0.54557,  1.80664,  0.57213,  0.96959,  2.19630,  0.14495, -0.06989, -0.02464,  0.09625, -0.91309,  0.20793, -0.02454},
 { -2.36493,  0.40365,  0.95708,  0.60531, -0.08403, -0.88165,  0.61790, -0.51470,  1.02107, -2.34133,  0.86753,  0.27620, -1.53678,  1.06356, -0.26588, -1.01969,  0.03477,  0.37425,  0.30821,  0.94160, -2.38394,  0.05376,  0.08035,  0.80552, -1.03488,  0.11297,  0.22596,  0.52934, -0.53320,  0.82629, -0.53099, -0.94104, -1.40969, -0.61660,  0.08301,  0.59779,  0.56448, -0.27779,  0.27554, -1.16786,  0.23409,  0.92333, -1.55018, -0.31722,  0.16705,  0.84234, -0.19704, -0.18905, -1.25802,  1.23314,  2.04320, -0.97232, -0.21116, -0.64479,  0.55067,  0.26323,  0.51062, -0.34078, -0.07620, -0.53280,  0.02849, -0.71191, -0.04133, -0.71609},
 { -0.43875,  1.61326,  1.20324,  0.96492,  2.00856,  2.52659, -1.07612, -1.17279,  1.20241, -1.70298,  2.14166, -0.42166,  0.50629, -1.00520, -1.21064, -1.00672, -1.02450,  0.61664, -1.58471,  0.89101, -2.47735, -0.57219,  0.86405, -1.40261,  0.41234, -0.27611, -2.75215,  0.59266,  0.63253, -0.19509,  2.28139, -1.07726, -0.79037, -1.88307,  0.07906,  0.40094, -0.35942, -0.90554,  0.27597, -0.70864,  1.26883, -1.30465,  1.22081,  0.34542, -1.03528,  1.69481, -0.45374, -0.10081, -0.71149, -2.03204,  0.24777,  0.12535,  1.41413,  1.13089, -0.59440,  0.79759,  0.05316,  0.39518, -0.42179,  0.28871, -1.07132,  0.93246, -0.60947, -0.23989},
 {  0.42623, -0.56608, -0.55524, -0.39505, -1.60794, -0.45489,  1.08700,  0.06443, -0.63081,  0.53733, -0.07918,  0.54438, -0.34534, -0.75568,  0.19421, -0.91510,  0.66496,  0.13665,  0.14528,  0.65813,  1.35399, -0.48292, -1.26734, -0.52573,  0.42705, -0.56758, -0.97780,  0.58156,  0.73138,  0.28752,  2.39439, -0.04466, -1.84299,  1.27113, -0.34784, -0.22283, -0.06808, -0.13130, -2.14495, -2.35050, -0.05389, -1.02259,  0.59861, -3.06363, -0.88989, -0.16971, -0.04392, -1.03152, -0.31414, -1.72156, -1.42395, -0.51747, -0.61712, -0.14016, -0.79393,  0.36173,  0.91840, -0.39974, -0.73601,  1.56812, -1.30096,  1.52674, -0.29790, -0.06284},
 { -1.96695, -1.07168,  1.57022,  1.11278,  0.18578, -0.63718,  1.31332,  1.17286, -0.91540,  1.17779, -1.94218,  0.39020,  1.38821, -0.47799, -1.57974, -0.68306,  1.17202, -0.98043,  0.05995, -0.45766, -0.63443,  1.50010,  0.07801, -0.98645, -0.76294,  0.19555,  1.01537,  1.24543, -0.28283,  0.08105,  0.30750, -0.66981,  0.72157,  0.05763,  0.50182, -0.10485,  1.43723,  0.11730, -0.52308, -1.11801,  1.56040,  0.10463,  0.48917,  0.76034,  1.44935, -1.37732, -0.71561, -1.02958,  0.07336, -0.82545, -0.22931, -0.42183, -0.66637, -0.44016,  0.02384, -0.75170, -0.36137,  0.98650,  0.85157,  0.12360,  0.39003,  0.27706,  0.94091, -0.10324},
 { -0.44528, -0.91978,  0.63971, -0.90253, -1.87478, -1.71976, -1.89376,  0.26962, -0.57822, -0.20265, -0.39226,  1.41273, -0.62908,  1.41398, -0.06495, -0.20425,  0.97560, -0.28243,  0.35227,  0.79532, -0.96689,  0.60032,  0.45945,  1.49505, -0.69380, -2.13096,  2.24161,  0.01674, -1.38053, -0.69376,  0.78827, -2.08473, -0.60535, -0.39320, -1.43159, -0.01926,  0.15490,  2.00305, -0.50543, -0.27959,  1.17869,  0.08167, -0.25350, -0.92857, -0.47064, -0.59976,  0.10533, -0.55877,  0.30664, -1.24742, -1.13613,  0.38870,  0.24022, -0.53960,  0.74871,  1.51420, -0.05390,  0.14648,  1.41599,  0.03572,  0.85235, -0.50578, -2.20361,  1.28753},
 { -0.13867,  0.70792,  0.73245,  0.03483,  0.91665, -0.96042, -1.10470, -1.91065,  0.73858, -1.78492,  0.37732,  0.94221, -2.14712, -0.65737,  1.93714, -0.64003,  0.81390, -1.85675,  0.29393,  0.40736,  0.82890, -0.25843,  0.65130,  1.02494, -1.11373,  0.72759,  0.24500, -1.60802,  0.67848, -0.70459,  0.51240, -0.69395,  0.20031,  0.38244, -0.15566, -1.87361, -1.36890, -0.20985, -1.45481, -0.02985,  0.19237,  1.50871, -0.21250,  1.17633, -1.73838, -0.63797,  0.57697,  0.80879,  0.43498, -0.52986, -1.05699, -0.81817, -1.85852, -1.50428, -1.81205,  1.81769, -0.82706, -0.97082,  0.66106,  0.14645,  0.29106, -0.40888,  0.06882,  0.29508},
 {  0.66310, -0.16628,  0.77057, -1.14285, -0.80685, -0.73091, -0.44767, -0.01564,  0.61929,  0.20243,  1.66524, -0.79056,  1.47506, -0.88148,  0.39076,  1.25839,  0.32460,  1.82225, -0.05871,  0.60052,  1.13671, -1.67981,  0.28850,  0.74436, -0.61164,  1.30058,  1.38047, -1.16992, -0.77740,  2.19371, -0.47508, -0.24208, -0.33626,  0.47182, -0.65817, -1.06340,  0.41793, -1.18993, -1.70300, -1.24758,  0.86252,  0.93498,  1.99840,  0.30199, -1.16724, -0.04048, -0.69279, -0.11865, -0.08507, -0.15755, -0.59717, -1.14890,  0.47944, -1.33473,  0.43582,  0.72849, -0.44708,  0.39506, -0.44949, -0.97053,  1.50699,  0.70102,  0.33193, -0.78114},
 {  0.69574,  0.44730,  0.81485,  2.05135, -0.69403, -0.03232,  0.02441, -0.64201,  1.00601, -0.81675, -1.21412, -2.29554,  0.40201, -0.40479,  0.31236, -0.72769,  0.19090,  1.31934,  2.48452, -0.75133,  0.51738,  1.60765, -1.56678, -1.80913,  1.45768, -0.90850, -2.22033,  0.24317,  0.59501,  0.37922,  1.08816,  1.14745,  0.63337,  0.34604,  0.34724, -0.32523,  1.88831, -3.07295,  1.29768, -1.50633,  0.20538,  0.66431,  0.97949, -1.09788, -0.74590, -0.30380, -0.56389,  2.45115,  0.08877,  0.48040, -0.44584, -0.63734, -0.79203, -0.64393,  0.33041,  0.89533,  1.61240, -0.05014, -0.27214,  0.77063,  0.58654, -0.88300,  2.42739,  0.90757},
 {  1.02069, -0.57277, -1.02258, -0.63394,  1.40188, -0.25126, -1.63322, -0.46791, -1.37103, -0.96949, -0.82552,  0.60547,  0.18362, -1.84797, -1.87852,  1.17719, -0.71071,  0.76935,  0.63645,  1.01937,  1.29859, -1.19295,  0.57343,  0.35728, -0.34835,  0.92974, -0.58362, -0.07530, -2.04176, -0.15882, -0.17969, -3.00708, -1.24054,  0.25676, -0.73254,  0.21392, -3.06886, -0.11818,  1.31733,  0.63941, -1.63841, -0.85671,  0.93695, -0.20085,  0.59293,  0.93427, -0.50639, -2.09719, -0.68631, -0.84132,  0.11307, -0.65956, -0.22557,  0.27801,  0.18526,  0.32143,  1.22298, -1.27104,  0.55321, -0.79818,  0.25945,  0.41795, -0.00415, -0.69495},
 { -0.22740, -0.80816,  0.41515,  0.65764,  1.91706,  1.63017, -1.43812, -0.98106,  0.43687,  1.51875,  0.35309, -0.14714,  0.74127,  1.95094,  2.73936,  0.73396, -0.65508, -0.02194,  2.11391,  1.00941,  0.54437,  0.76817, -0.92764,  0.81749,  0.74472,  0.84312, -0.69186, -0.74769,  0.26549,  0.74111, -0.11799, -0.33655,  1.21966, -0.89500,  0.18523, -0.87647,  1.62042, -0.15412, -0.56248,  0.72426,  0.39255, -0.18326, -0.95290, -0.48213,  0.05601,  1.36230, -0.66916, -1.19787,  1.03502,  0.92144,  0.29303, -0.06429,  0.03906, -1.97123, -0.74861,  1.52150, -1.06361, -1.61434,  0.85911,  0.66443,  0.58268,  0.89129, -0.68223, -1.06371},
 {  1.02250, -1.06439, -0.23819, -1.28786, -1.13609, -0.59965,  2.08400, -0.25189,  1.63561,  0.14286,  0.31181,  1.05939, -0.01125,  0.99957, -0.37621, -0.27569,  1.26908,  0.54775, -0.02173, -1.00390,  1.15557, -0.13389, -0.82020, -0.39904,  0.01174, -0.00463, -1.62429, -3.21495, -0.30228,  0.16245, -0.16559, -0.32443, -0.85864,  1.21471, -0.08807,  0.69216, -2.29826,  0.10911, -0.15207, -1.40012, -0.79851,  0.06567,  0.84031, -0.53600,  0.98916, -0.66688,  0.42233,  2.56520, -0.34844,  0.29250,  0.95587,  1.00975,  2.07198,  1.34944,  0.22273,  0.47019, -2.25771, -0.12401,  0.31657,  0.91473, -0.23763,  1.21773, -0.41381, -1.37376},
 { -0.44935,  1.43439, -0.43358, -0.17498,  0.55650, -0.43083,  1.93613,  0.06796, -0.86532,  0.76025, -0.58073,  0.01887, -0.03339,  0.72139,  0.46204, -1.42527,  0.67847,  0.30201,  0.75600,  0.16708,  0.76639, -0.04300,  0.39492,  1.80100,  0.09017, -0.55498, -0.09503,  0.11222, -1.02279,  0.05347, -0.42830, -0.31416,  1.13500,  2.34678,  0.91625, -0.65990, -0.49840, -0.04614, -0.55381, -0.10800,  0.12217, -0.00259,  0.76801, -0.69289, -1.43346,  0.47302, -0.06434,  1.64739,  0.77901, -0.84305,  1.07792,  0.38225, -1.25020,  0.64378,  0.44344,  0.90460,  1.06537, -0.02712, -0.56629, -0.66582, -0.35431, -0.87738, -1.65918, -0.19150},
 {  0.16233,  0.49870, -0.39547,  1.74306, -1.32171, -0.93655,  0.27405, -0.15981, -0.35567, -1.67652, -0.60088,  0.03401,  0.11403, -1.14078, -0.49232, -1.90877, -1.48432,  1.08161, -0.93451,  1.06422, -1.35371, -0.31485, -0.10548,  2.17803, -0.40910,  0.93318, -2.04898, -0.29846, -2.59259, -0.48842,  0.83598, -1.15160, -0.18947,  1.28851, -0.69973, -0.62462,  0.46010, -0.20287, -0.34623,  0.26722, -0.15805,  1.82573, -0.86913,  0.12358, -0.99197,  0.75096,  1.74126, -0.76410,  0.01026, -0.19212,  0.02715, -0.26509,  1.27706,  0.04313,  0.09018,  0.27758,  0.37234,  0.94864, -0.83655, -0.58954, -0.74540,  0.38243, -0.23283, -0.89262},
 {  0.88300, -0.79913,  0.05409,  0.40822,  1.34566,  0.81426, -1.38969,  0.09355, -1.49484, -1.73507, -0.00909,  1.54870, -0.41116, -0.57581,  0.40543,  0.85562,  0.89612,  0.27473, -0.11448, -0.48889,  0.45032,  0.21290, -0.19628,  0.32661, -1.22466,  0.08798,  1.18371, -0.60153, -0.29835, -0.65494,  0.16112,  1.60792, -0.90909,  0.27369, -2.04474,  0.74625, -1.04076, -0.90865, -0.73193, -0.50927,  0.03767,  0.03280,  0.34832,  0.25719,  0.50090,  0.02027,  0.11923, -0.70444, -2.53008, -1.67828, -0.67685, -2.79179,  0.16454, -0.12364, -1.37738, -0.36129, -1.29966, -1.48309,  0.43467, -1.03580,  1.73035, -0.46957,  0.41723,  0.01925},
 { -0.07200, -0.19858, -0.56990,  0.93502,  0.31489,  0.03415,  0.43054,  0.89851, -0.16452, -0.21438, -0.65391,  0.20125, -1.52491,  0.70159, -0.32446, -0.91775,  1.47648,  0.46984, -0.38152, -1.31182,  0.72037, -0.31019,  1.02505,  1.88987, -0.96692, -0.87114, -0.38355,  1.69843, -0.95672, -1.68272,  1.30997, -0.49157, -0.01892, -1.52841, -1.13385,  1.20348, -1.14483, -0.35326, -0.93280, -0.80282, -1.52466,  0.03460,  1.32331, -0.94722,  0.98785,  0.75622, -0.05733, -0.19965, -1.03950,  0.40891,  0.66356, -1.23525, -0.87733, -1.21386, -0.55512,  0.16917,  0.41563,  0.26203,  1.88441, -1.45885, -0.29217,  0.79032,  1.43778,  0.48994},
 {  1.18125, -0.22241, -0.53966,  1.17850, -0.11837,  0.19794, -0.06091, -1.90854, -1.06745, -0.34259,  2.38867, -0.28041,  1.53918,  0.56650,  0.41196,  0.18814,  1.48597, -1.61362,  0.61397, -1.52356,  0.41307, -0.66676, -0.97275,  0.97028,  0.68165,  0.27753,  1.42399, -0.39215,  0.43083, -0.37118,  1.01178, -0.38451, -1.01038,  0.82819,  1.04420,  0.47743,  0.18053,  0.35283, -0.39320,  0.81665,  0.55156,  0.99824,  0.26124,  0.62063,  0.85804, -0.63472,  1.52438, -1.24371,  1.89197,  0.66152,  0.83330,  1.81075,  0.94727, -0.72838, -1.60372, -0.81342,  1.07132, -0.80169, -0.16865,  0.47476,  0.24113,  0.90325,  0.33418,  0.99190},
 { -1.31474,  0.99885, -0.51905, -2.06755,  1.89607,  0.50120,  0.55699,  0.64159,  0.27250, -0.74298, -0.47070,  1.24029,  1.25937,  0.56533, -0.72068,  0.00403,  0.60023,  0.09010,  0.01106,  0.36940,  0.01107,  0.23341, -0.28285, -0.90343, -0.81654, -0.87117, -2.22683, -1.74085, -0.33994,  0.21590,  0.32339, -0.25447, -0.51572,  0.07395,  0.56391, -0.65319, -0.30675, -0.54697,  1.37291, -0.97025, -0.48722, -0.07884, -1.08162,  1.58811,  1.20202, -2.12240, -0.23753, -0.68533,  0.17356, -0.57624, -0.29717, -1.76222,  0.46068, -1.04109,  0.09766,  0.41468, -0.37873, -0.44170, -0.56938,  1.07255, -1.68430,  0.73715,  0.34100,  0.04918},
 { -1.63274,  0.07702, -0.53002, -0.16307, -1.15813,  2.66859, -0.43385,  0.09158,  0.23298, -0.30041,  0.83545, -0.16855,  0.71322,  0.85191, -1.08731,  0.90001, -0.78671, -0.61874,  0.00169, -3.17655,  0.12642,  0.36269,  0.36946,  0.93149, -1.29027, -0.71739, -0.33558,  0.70335,  0.75606,  0.26006,  0.77883, -0.12917,  0.10172, -0.34259, -0.29544,  0.19847, -1.71776,  0.01513, -0.57679,  0.51253,  0.67262,  1.38958, -0.47644,  0.40470, -0.53291, -1.79595,  0.24123,  1.38369, -1.51523,  0.27198, -1.54307, -0.15085, -1.17440,  0.88249,  0.14907, -0.91731, -0.68929, -0.38283, -1.11170, -1.48413, -0.14836, -0.44517, -0.36235, -2.03756}}
});

#endif /* EXPORT_PARAMETERS_FC1_W_H */
