#ifndef EXPORT_PARAMETERS_FC2_B_H
#define EXPORT_PARAMETERS_FC2_B_H

#include <aidge/data/Tensor.hpp>
#include <memory>

std::shared_ptr<Aidge::Tensor> FC2_2 = std::make_shared<Aidge::Tensor>(Aidge::Array1D<float, 16> {
{ 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000}
});

#endif /* EXPORT_PARAMETERS_FC2_B_H */
