/********************************************************************************
 * This file has been generated by the Aidge export.
 ********************************************************************************/

/*** STD INCLUDES ***/
#include <memory>  // std::shared_ptr

/*** AIDGE INCLUDES ***/
#include <aidge/graph/GraphView.hpp>  // Aidge::GraphView
#include <aidge/graph/Node.hpp>       // Aidge::Node
#include <aidge/graph/OpArgs.hpp>     // Aidge::Sequential

/*** AIDGE OPERATORS ***/

/*** OPERATOR ATTRIBUTES & PARAMETERS ***/
#include "aidge/operator/Producer.hpp"
#include "attributes/InputNode_b.hpp"
#include "aidge/operator/Producer.hpp"
#include "attributes/InputNode_w.hpp"
#include "aidge/operator/FC.hpp"
#include "attributes/InputNode.hpp"
#include "aidge/operator/ReLU.hpp"
#include "aidge/operator/Producer.hpp"
#include "attributes/FC1_b.hpp"
#include "aidge/operator/Producer.hpp"
#include "attributes/FC1_w.hpp"
#include "aidge/operator/FC.hpp"
#include "attributes/FC1.hpp"
#include "aidge/operator/ReLU.hpp"
#include "aidge/operator/Producer.hpp"
#include "attributes/FC2_b.hpp"
#include "aidge/operator/Producer.hpp"
#include "attributes/FC2_w.hpp"
#include "aidge/operator/FC.hpp"
#include "attributes/FC2.hpp"
#include "aidge/operator/ReLU.hpp"
#include "aidge/operator/Producer.hpp"
#include "attributes/OutputNode_b.hpp"
#include "aidge/operator/Producer.hpp"
#include "attributes/OutputNode_w.hpp"
#include "aidge/operator/FC.hpp"
#include "attributes/OutputNode.hpp"

/*** HEADER ***/
#include "dnn.hpp"


std::shared_ptr<Aidge::GraphView> generateModel() {
    /*** BUILDING GRAPH ***/
    std::shared_ptr<Aidge::GraphView> graph = std::make_shared<Aidge::GraphView>();
    
    /*** INPUTNODE_B ***/
    std::shared_ptr<Aidge::Node> InputNode_b =
            Aidge::Producer(
                InputNode_2,
                "InputNode_b"
            );
    graph->add(InputNode_b);


    
    /*** INPUTNODE_W ***/
    std::shared_ptr<Aidge::Node> InputNode_w =
            Aidge::Producer(
                InputNode_1,
                "InputNode_w"
            );
    graph->add(InputNode_w);


    
    /*** INPUTNODE ***/
    std::shared_ptr<Aidge::Node> InputNode =
            Aidge::FC(
                _INPUTNODE_IN_CHANNELS,
                _INPUTNODE_OUT_CHANNELS,
                "InputNode"
            );

    InputNode_w->addChild(InputNode, 0, 1); 
    InputNode_b->addChild(InputNode, 0, 2); 

    graph->add(InputNode);


    
    /*** RELU0 ***/
    std::shared_ptr<Aidge::Node> Relu0 =
            Aidge::ReLU(
                "Relu0"
            );

    InputNode->addChild(Relu0, 0, 0); 

    graph->add(Relu0);


    
    /*** FC1_B ***/
    std::shared_ptr<Aidge::Node> FC1_b =
            Aidge::Producer(
                FC1_2,
                "FC1_b"
            );
    graph->add(FC1_b);


    
    /*** FC1_W ***/
    std::shared_ptr<Aidge::Node> FC1_w =
            Aidge::Producer(
                FC1_1,
                "FC1_w"
            );
    graph->add(FC1_w);


    
    /*** FC1 ***/
    std::shared_ptr<Aidge::Node> FC1 =
            Aidge::FC(
                _FC1_IN_CHANNELS,
                _FC1_OUT_CHANNELS,
                "FC1"
            );

    Relu0->addChild(FC1, 0, 0); 
    FC1_w->addChild(FC1, 0, 1); 
    FC1_b->addChild(FC1, 0, 2); 

    graph->add(FC1);


    
    /*** RELU1 ***/
    std::shared_ptr<Aidge::Node> Relu1 =
            Aidge::ReLU(
                "Relu1"
            );

    FC1->addChild(Relu1, 0, 0); 

    graph->add(Relu1);


    
    /*** FC2_B ***/
    std::shared_ptr<Aidge::Node> FC2_b =
            Aidge::Producer(
                FC2_2,
                "FC2_b"
            );
    graph->add(FC2_b);


    
    /*** FC2_W ***/
    std::shared_ptr<Aidge::Node> FC2_w =
            Aidge::Producer(
                FC2_1,
                "FC2_w"
            );
    graph->add(FC2_w);


    
    /*** FC2 ***/
    std::shared_ptr<Aidge::Node> FC2 =
            Aidge::FC(
                _FC2_IN_CHANNELS,
                _FC2_OUT_CHANNELS,
                "FC2"
            );

    Relu1->addChild(FC2, 0, 0); 
    FC2_w->addChild(FC2, 0, 1); 
    FC2_b->addChild(FC2, 0, 2); 

    graph->add(FC2);


    
    /*** RELU2 ***/
    std::shared_ptr<Aidge::Node> Relu2 =
            Aidge::ReLU(
                "Relu2"
            );

    FC2->addChild(Relu2, 0, 0); 

    graph->add(Relu2);


    
    /*** OUTPUTNODE_B ***/
    std::shared_ptr<Aidge::Node> OutputNode_b =
            Aidge::Producer(
                OutputNode_2,
                "OutputNode_b"
            );
    graph->add(OutputNode_b);


    
    /*** OUTPUTNODE_W ***/
    std::shared_ptr<Aidge::Node> OutputNode_w =
            Aidge::Producer(
                OutputNode_1,
                "OutputNode_w"
            );
    graph->add(OutputNode_w);


    
    /*** OUTPUTNODE ***/
    std::shared_ptr<Aidge::Node> OutputNode =
            Aidge::FC(
                _OUTPUTNODE_IN_CHANNELS,
                _OUTPUTNODE_OUT_CHANNELS,
                "OutputNode"
            );

    Relu2->addChild(OutputNode, 0, 0); 
    OutputNode_w->addChild(OutputNode, 0, 1); 
    OutputNode_b->addChild(OutputNode, 0, 2); 

    graph->add(OutputNode);



    return graph;
}
