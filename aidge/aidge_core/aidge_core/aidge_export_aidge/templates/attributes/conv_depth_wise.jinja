#ifndef EXPORT_ATTRIBUTES_{{name|upper}}_H
#define EXPORT_ATTRIBUTES_{{name|upper}}_H

#define _{{name|upper}}_CHANNELS {{out_chan[0]}}

{% for i in range(kernel_dims|length) %}
#define _{{name|upper}}_KERNEL_{{i}} {{kernel_dims[i]}}
{%- endfor %}
{% for i in range(stride_dims|length) %}
#define _{{name|upper}}_STRIDE_{{i}} {{stride_dims[i]}}
{%- endfor %}
{% for i in range(dilation_dims|length) %}
#define _{{name|upper}}_DILATION_{{i}} {{dilation_dims[i]}}
{%- endfor %}

#endif /* EXPORT_ATTRIBUTES_{{name|upper}}_H */
