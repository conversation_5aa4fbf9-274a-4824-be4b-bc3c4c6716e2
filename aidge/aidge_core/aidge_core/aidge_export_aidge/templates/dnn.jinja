/********************************************************************************
 * This file has been generated by the Aidge export.
 ********************************************************************************/

/*** STD INCLUDES ***/
#include <memory>  // std::shared_ptr

/*** AIDGE INCLUDES ***/
#include <aidge/graph/GraphView.hpp>  // Aidge::GraphView
#include <aidge/graph/Node.hpp>       // Aidge::Node
#include <aidge/graph/OpArgs.hpp>     // Aidge::Sequential

/*** AIDGE OPERATORS ***/
{%- for operator in operators %}
#include <aidge/operator/{{operator}}.hpp>
{%- endfor %}

/*** OPERATOR ATTRIBUTES & PARAMETERS ***/
{%- for header in headers %}
#include "{{ header | replace('include/', '') }}"
{%- endfor %}

/*** HEADER ***/
#include "dnn.hpp"


std::shared_ptr<Aidge::GraphView> generateModel() {
    /*** BUILDING GRAPH ***/
    std::shared_ptr<Aidge::GraphView> graph = std::make_shared<Aidge::GraphView>();

    {%- for action in actions %}
    {{ action }}
    {%- endfor %}

    return graph;
}
