{% filter indent(width=4, first=False) %}
/*** {{name|upper}} ***/
std::shared_ptr<Aidge::Node> {{name}} =
        Aidge::MaxPooling(
            {
            {%- for i in range(kernel_dims|length) -%}
                _{{name|upper}}_KERNEL_{{i}}{%- if not loop.last %}, {% endif -%}
            {%- endfor -%}
            },
            "{{name}}",
            {
            {%- for i in range(stride_dims|length) -%}
                _{{name|upper}}_STRIDE_{{i}} {%- if not loop.last %}, {% endif -%}
            {%- endfor -%}
            },
            _{{name|upper}}_CEIL_MODE
        );
{% include "./_set_input.jinja" %}
graph->add({{name}});
{% endfilter %}
