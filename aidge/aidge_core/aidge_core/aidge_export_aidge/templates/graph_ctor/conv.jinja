{% filter indent(width=4, first=False) %}
/*** {{name|upper}} ***/
std::shared_ptr<Aidge::Node> {{name}} =
        Aidge::Conv(
            _{{name|upper}}_IN_CHANNELS,
            _{{name|upper}}_OUT_CHANNELS,
            {
            {%- for i in range(kernel_dims|length) -%}
                _{{name|upper}}_KERNEL_{{i}}{%- if not loop.last %}, {% endif -%}
            {%- endfor -%}
            },
            "{{name}}",
            {
            {%- for i in range(stride_dims|length) -%}
                _{{name|upper}}_STRIDE_{{i}} {%- if not loop.last %}, {% endif -%}
            {%- endfor -%}
            },
            {
            {%- for i in range(dilation_dims|length) -%}
                _{{name|upper}}_DILATION_{{i}} {%- if not loop.last %}, {% endif -%}
            {%- endfor -%}
            }
        );
{% include "./_set_input.jinja" %}
graph->add({{name}});
{% endfilter %}
