from aidge_core.aidge_export_aidge.registry import ExportSerialize
from aidge_core.aidge_export_aidge import ROOT_EXPORT
from aidge_core.export_utils import ExportNodeCpp
from aidge_core import ImplSpec, IOSpec, dtype

@ExportSerialize.register(["Pad1D", "Pad2D"], ImplSpec(IOSpec(dtype.any)))
class Pad(ExportNodeCpp):
    def __init__(self, node, mem_info):
        super().__init__(node, mem_info)
        self.config_template = str(
            ROOT_EXPORT / "templates/attributes/pad.jinja")
        self.forward_template = str(
            ROOT_EXPORT /"templates/graph_ctor/pad.jinja")
        self.include_list = ["aidge/operator/Pad.hpp"]
        self.kernels_to_copy = []
        self.config_path = "include/attributes"
        self.config_extension = "hpp"
