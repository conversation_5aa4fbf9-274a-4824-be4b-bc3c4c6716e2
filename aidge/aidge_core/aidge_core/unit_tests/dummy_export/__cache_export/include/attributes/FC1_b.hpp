#ifndef EXPORT_PARAMETERS_FC1_B_H
#define EXPORT_PARAMETERS_FC1_B_H

#include <aidge/data/Tensor.hpp>
#include <memory>

std::shared_ptr<Aidge::Tensor> FC1_2 = std::make_shared<Aidge::Tensor>(Aidge::Array1D<float, 32> {
{ 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000, 0.01000}
});

#endif /* EXPORT_PARAMETERS_FC1_B_H */
