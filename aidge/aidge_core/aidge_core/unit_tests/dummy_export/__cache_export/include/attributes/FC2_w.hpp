#ifndef EXPORT_PARAMETERS_FC2_W_H
#define EXPORT_PARAMETERS_FC2_W_H

#include <aidge/data/Tensor.hpp>
#include <memory>

std::shared_ptr<Aidge::Tensor> FC2_1 = std::make_shared<Aidge::Tensor>(Aidge::Array2D<float, 16, 32> {
{{  2.21221,  1.16308,  0.77400,  0.48380,  1.04344,  0.29956,  1.18393,  0.15303,  1.89171, -1.16881, -1.23474,  1.55807, -1.77103, -0.54594, -0.45138, -2.35563,  0.57938,  0.54144, -1.85608,  2.67851, -1.97688,  1.25463, -0.20802, -0.54877,  0.24442, -0.68106, -0.03716, -0.13532, -0.48775,  0.37723, -0.02262,  0.41016},
 {  0.57461,  0.57127,  1.46613, -2.75796,  0.68629,  1.07628,  0.35496, -0.61413,  1.07317,  1.83076,  0.12017, -1.14681, -0.97111,  0.05384, -0.77570, -2.50748, -0.78822, -0.59165,  0.74177,  0.85860, -1.47344, -0.22794, -1.07309,  0.20131, -1.04248,  0.35005, -1.32788,  0.53605, -1.47497,  1.51944, -0.52414,  1.90409},
 {  1.26626, -1.57344,  0.89506, -0.14008, -0.60159,  0.29670,  1.20406,  1.31120, -0.97122,  0.50359, -0.58256, -1.18945,  0.37171, -0.55021,  0.93001, -1.59188, -1.42258, -1.10819, -0.51762,  0.07872,  2.00883, -0.91856,  0.28631, -0.74571,  0.56046, -1.20809,  0.96976,  1.81402, -0.52854, -1.52274, -1.88909, -2.51524},
 {  0.65479, -1.35493, -0.45481, -0.95748,  0.32511, -0.72486, -1.30023,  1.11196,  0.36793, -0.47827,  1.45343, -1.17395,  0.24154, -0.79218,  0.47898,  0.93210,  0.96885, -3.15577, -1.02182,  2.19353, -0.06813, -0.53859, -0.31868, -0.86113, -0.17634, -1.88152,  0.35655, -0.72057,  0.74419, -0.35602,  0.77874, -0.15964},
 {  0.60878,  1.79745,  1.07418,  0.19595,  0.06643, -1.73764,  0.84870,  0.04735, -0.80038,  0.14581, -0.16882,  0.32605,  0.93633,  0.45788,  0.35744, -0.89426,  0.77933,  0.49384, -1.01031, -0.90434, -0.39157, -1.21408,  1.31662,  2.15641, -0.43293,  1.09382,  0.71536,  1.82714,  0.92542, -1.04467, -0.90495,  1.00622},
 { -0.07468,  0.51742,  0.82254, -0.80693, -1.87851,  1.37690,  0.88588,  0.20589,  1.91187,  0.99435,  0.33342, -0.23581,  0.11884,  0.29882, -1.91989, -1.99821, -0.67558,  0.35114,  1.00775, -0.76447, -0.35391,  1.25944,  1.63239, -0.96297, -0.33355,  0.50805, -1.73784,  0.75617,  0.77374,  1.68393,  0.89127,  1.25736},
 {  3.29045,  0.13123, -1.19762,  1.64030,  1.89389, -1.21380, -0.56693,  2.39997, -0.02392, -0.38509,  1.07567, -0.98780, -1.26881,  0.95859, -0.83141, -1.49765,  1.39457,  0.66052,  1.00435, -0.18904, -0.55172, -0.27303, -1.91536,  0.91815,  1.23910,  2.17306, -0.47186,  0.07520,  0.21996,  0.07186, -1.32469,  1.12562},
 { -1.44675, -0.35596, -0.77169, -0.25377,  0.30545, -0.04047, -0.30367,  0.49660,  0.16687,  1.38010, -0.46042,  1.80271, -0.22734,  0.64572, -0.26001,  1.81153,  0.03087, -1.25296, -1.11660,  0.38731,  0.30455, -0.41055, -0.37035,  1.28454, -1.09419,  0.21001, -0.43584, -0.08666, -0.93973,  1.46422, -2.24651, -1.30581},
 { -0.34305,  0.93444, -0.40720,  0.53809,  0.50870, -0.16038,  0.91512,  0.84188, -0.49178, -1.00554, -1.55609,  3.13222, -0.15749, -0.43590, -0.52665, -0.49295, -0.17885,  1.43719,  0.43165, -0.31814,  0.46760, -0.16282,  0.17287,  0.68361,  0.76159,  1.61067,  0.00010, -1.26422, -0.51431,  0.13895,  1.71985,  0.66712},
 { -0.11494,  0.21445,  1.56177,  0.99385,  1.11169,  0.10874,  1.49358,  0.53534,  0.53680, -1.86105, -1.32519, -0.46896, -0.64744, -0.67683, -0.55203, -0.61593,  0.10064,  0.64282, -0.33182,  1.65978,  0.49453, -0.01003,  0.71980, -1.08675, -0.32953, -0.19519, -0.51758, -2.79099,  0.72313, -0.35720,  1.44181, -1.49806},
 {  0.17372, -1.30963,  0.07968, -0.14142, -0.23906,  2.00367,  1.93971, -0.50719,  2.30716,  0.78840,  0.30559,  0.56460, -0.01646,  0.26467, -0.22913, -1.47478,  0.67586,  0.27119, -0.63455, -0.41398,  0.47299,  0.32027,  0.63768, -0.28379,  1.55080, -1.23966, -1.72708,  0.87003,  0.07617,  1.27926, -0.38289,  0.75839},
 { -0.53819, -1.63486,  0.25525,  0.19743,  0.56403, -0.98571, -0.05929,  0.61197, -0.17426,  1.96916,  0.65265, -0.64305,  0.58428,  0.35468, -0.83858, -1.10752, -2.76255,  1.77993, -0.47830, -0.40403,  1.61741, -0.52861,  1.47949, -1.27279, -1.47610, -0.18857, -0.83653, -0.59329,  1.28774,  0.36334,  0.40237,  0.59253},
 {  0.34593, -0.75671, -1.78783,  0.13927, -0.17871, -0.20226,  0.98685,  1.04663, -0.82165, -2.31760,  1.18866,  0.22843, -0.51752, -0.55537, -0.21166, -0.22437, -1.07040, -0.03408,  1.33647, -0.86891,  0.77280,  0.83015, -0.36801,  0.22462, -0.13839,  0.19773,  0.17645, -0.29490,  1.68344,  0.37922, -0.55152,  0.10254},
 { -1.03103, -1.00571,  0.77013, -1.12598,  0.03745, -0.32547,  0.96050,  0.49005,  0.97980, -1.05094, -1.24471,  0.33783,  0.02099, -0.73851,  2.64973, -0.47339,  0.26951,  0.95299, -1.13895, -0.65459, -0.05305,  0.54617,  0.44457, -0.31482, -0.01124,  0.65329,  0.11693,  0.23943, -0.06616,  1.43014,  1.42808,  0.89327},
 { -0.00535,  1.07622,  0.80104, -0.48121, -0.36084,  0.41194,  1.32260,  0.57039,  0.90337, -0.38090,  0.40487, -0.50801,  1.63764, -0.13969, -0.18017, -0.00095, -1.34553,  0.81409, -1.20510, -1.76932, -0.21270, -1.07602, -0.55615,  0.88617, -0.39793, -1.24831, -1.04972,  0.03132, -0.19883, -0.85172,  0.20714,  1.08402},
 { -0.03866,  0.11690, -0.18257,  0.83748, -0.63579,  1.23811, -1.00193, -1.66343, -0.23578, -0.49840,  1.39689, -0.47080, -0.38969, -0.81984,  0.01970, -0.28763, -0.74443,  1.03075, -0.15310, -0.25814,  0.26538, -0.61389,  1.02410,  0.56759, -0.41213, -1.54664,  1.52279, -0.47991,  1.24897,  0.21739, -0.33278,  0.99188}}
});

#endif /* EXPORT_PARAMETERS_FC2_W_H */
