#ifndef EXPORT_PARAMETERS_OUTPUTNODE_W_H
#define EXPORT_PARAMETERS_OUTPUTNODE_W_H

#include <aidge/data/Tensor.hpp>
#include <memory>

std::shared_ptr<Aidge::Tensor> OutputNode_1 = std::make_shared<Aidge::Tensor>(Aidge::Array2D<float, 10, 16> {
{{  2.21221,  1.16308,  0.77400,  0.48380,  1.04344,  0.29956,  1.18393,  0.15303,  1.89171, -1.16881, -1.23474,  1.55807, -1.77103, -0.54594, -0.45138, -2.35563},
 {  0.57938,  0.54144, -1.85608,  2.67851, -1.97688,  1.25463, -0.20802, -0.54877,  0.24442, -0.68106, -0.03716, -0.13532, -0.48775,  0.37723, -0.02262,  0.41016},
 {  0.57461,  0.57127,  1.46613, -2.75796,  0.68629,  1.07628,  0.35496, -0.61413,  1.07317,  1.83076,  0.12017, -1.14681, -0.97111,  0.05384, -0.77570, -2.50748},
 { -0.78822, -0.59165,  0.74177,  0.85860, -1.47344, -0.22794, -1.07309,  0.20131, -1.04248,  0.35005, -1.32788,  0.53605, -1.47497,  1.51944, -0.52414,  1.90409},
 {  1.26626, -1.57344,  0.89506, -0.14008, -0.60159,  0.29670,  1.20406,  1.31120, -0.97122,  0.50359, -0.58256, -1.18945,  0.37171, -0.55021,  0.93001, -1.59188},
 { -1.42258, -1.10819, -0.51762,  0.07872,  2.00883, -0.91856,  0.28631, -0.74571,  0.56046, -1.20809,  0.96976,  1.81402, -0.52854, -1.52274, -1.88909, -2.51524},
 {  0.65479, -1.35493, -0.45481, -0.95748,  0.32511, -0.72486, -1.30023,  1.11196,  0.36793, -0.47827,  1.45343, -1.17395,  0.24154, -0.79218,  0.47898,  0.93210},
 {  0.96885, -3.15577, -1.02182,  2.19353, -0.06813, -0.53859, -0.31868, -0.86113, -0.17634, -1.88152,  0.35655, -0.72057,  0.74419, -0.35602,  0.77874, -0.15964},
 {  0.60878,  1.79745,  1.07418,  0.19595,  0.06643, -1.73764,  0.84870,  0.04735, -0.80038,  0.14581, -0.16882,  0.32605,  0.93633,  0.45788,  0.35744, -0.89426},
 {  0.77933,  0.49384, -1.01031, -0.90434, -0.39157, -1.21408,  1.31662,  2.15641, -0.43293,  1.09382,  0.71536,  1.82714,  0.92542, -1.04467, -0.90495,  1.00622}}
});

#endif /* EXPORT_PARAMETERS_OUTPUTNODE_W_H */
