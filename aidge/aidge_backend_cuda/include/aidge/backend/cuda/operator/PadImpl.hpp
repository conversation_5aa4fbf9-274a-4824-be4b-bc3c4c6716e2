/********************************************************************************
 * Copyright (c) 2024 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_BACKEND_CUDA_OPERATOR_PADIMPL_H_
#define AIDGE_BACKEND_CUDA_OPERATOR_PADIMPL_H_

#include <array>
#include <memory>
#include <tuple>
#include <vector>

#include <cudnn.h>

#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/operator/Pad.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

#include "aidge/backend/cuda/utils/CudaUtils.hpp"

namespace Aidge {
template <DimIdx_t DIM>
class PadImpl_cuda : public OperatorImpl {
private:
    // CuDNN specific variables
    std::shared_ptr<Tensor> mInputFallback, mOutputGradFallback;
    int mLeftPad, mTopPad;
    double mPadVal;
    unsigned int mPadType;

public:
    PadImpl_cuda(const Pad_Op<DIM> &op) : OperatorImpl(op, "cuda") {}

    static std::unique_ptr<PadImpl_cuda> create(const Pad_Op<2> &op) {
        return std::make_unique<PadImpl_cuda>(op);
    }

public:
    void forward();
    void backward();

private:
    template <class T> void forward_(const Tensor& input);
    template <class T> void backward_(const Tensor& outGrad);
};

namespace {
// add cuda backend to Pad_Op<2> implementation registry
static Registrar<Pad_Op<2>> registrarPadImpl_cuda("cuda", Aidge::PadImpl_cuda<2>::create);
}  // namespace
}  // namespace Aidge

#endif /* AIDGE_BACKEND_CUDA_OPERATOR_PADIMPL_H_ */
