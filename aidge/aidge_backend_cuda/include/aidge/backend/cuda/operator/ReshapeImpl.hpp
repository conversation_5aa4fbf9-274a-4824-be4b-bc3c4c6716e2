/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_BACKEND_CUDA_OPERATOR_RESHAPEIMPL_H_
#define AIDGE_BACKEND_CUDA_OPERATOR_RESHAPEIMPL_H_

#include <array>
#include <memory>
#include <tuple>
#include <vector>

#include <cudnn.h>

#include "aidge/backend/OperatorImpl.hpp"
#include "aidge/operator/Reshape.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

#include "aidge/backend/cuda/utils/CudaUtils.hpp"

namespace Aidge {
class ReshapeImpl_cuda : public OperatorImpl {
private:
    std::shared_ptr<Tensor> mInputFallback, mOutputGradFallback;

public:
    ReshapeImpl_cuda(const Reshape_Op &op) : OperatorImpl(op, "cuda") {}

    static std::unique_ptr<ReshapeImpl_cuda> create(const Reshape_Op &op) {
        return std::make_unique<ReshapeImpl_cuda>(op);
    }

public:
    void forward();
    void backward();
    ~ReshapeImpl_cuda();
};

namespace {
// add cuda backend to Reshape_Op implementation registry
static Registrar<Reshape_Op> registrarReshapeImpl_cuda("cuda", Aidge::ReshapeImpl_cuda::create);
}  // namespace
}  // namespace Aidge

#endif /* AIDGE_BACKEND_CUDA_OPERATOR_RESHAPEIMPL_H_ */
