include:
  #- remote: 'https://gitlab.eclipse.org/eclipse/aidge/gitlab_shared_files/-/raw/main/.gitlab/ci/shared_script.gitlab-ci.yml'
  - remote: 'https://gitlab.eclipse.org/hrouis/gitlab_shared_files/-/raw/test_hro/.gitlab/ci/shared_script.gitlab-ci.yml'

build:ubuntu_cpp:
  stage: build
  needs: []
  tags:
    - docker

  script:
    # Download dependencies
    - DEPENDENCY_JOB="build:ubuntu_cpp"
    # aidge_core
    - DEPENDENCY_NAME="aidge_core"
    - !reference [.download_dependency, script]
    # aidge_backend_cpu
    - DEPENDENCY_NAME="aidge_backend_cpu"
    - !reference [.download_dependency, script]

    # Build current module
    - export CMAKE_PREFIX_PATH=../install_cpp
    - mkdir -p build_cpp
    - cd build_cpp
    - cmake -DCMAKE_INSTALL_PREFIX:PATH=../install_cpp -DCMAKE_BUILD_TYPE=Debug -DWERROR=ON -DCOVERAGE=ON ..
    - make -j4 all install

  artifacts:
    expire_in: 1 week
    paths:
      - build_cpp/
      - install_cpp/

build:ubuntu_cpp_g++10:
  stage: build
  needs: []
  tags:
    - docker

  script:
        # Download dependencies
    - DEPENDENCY_JOB="build:ubuntu_cpp"
    # aidge_core
    - DEPENDENCY_NAME="aidge_core"
    - !reference [.download_dependency, script]
    # aidge_backend_cpu
    - DEPENDENCY_NAME="aidge_backend_cpu"
    - !reference [.download_dependency, script]

    # Build current module
    - export CMAKE_PREFIX_PATH=../install_cpp
    - apt install -y g++-10
    - mkdir -p build_cpp
    - mkdir -p install_cpp
    - cd build_cpp
    - export CXX=/usr/bin/g++-10
    - cmake -DCMAKE_INSTALL_PREFIX:PATH=../install_cpp -DCMAKE_BUILD_TYPE=Debug -DWERROR=ON -DCOVERAGE=ON ..
    - make -j4 all install

build:ubuntu_cpp_g++12:
  stage: build
  needs: []
  tags:
    - docker

  script:
    # Download dependencies
    - DEPENDENCY_JOB="build:ubuntu_cpp"
    # aidge_core
    - DEPENDENCY_NAME="aidge_core"
    - !reference [.download_dependency, script]
    # aidge_backend_cpu
    - DEPENDENCY_NAME="aidge_backend_cpu"
    - !reference [.download_dependency, script]


    # Build current module
    - export CMAKE_PREFIX_PATH=../install_cpp
    - apt install -y g++-12
    - mkdir -p build_cpp
    - mkdir -p install_cpp
    - cd build_cpp
    - export CXX=/usr/bin/g++-12
    - cmake -DCMAKE_INSTALL_PREFIX:PATH=../install_cpp -DCMAKE_BUILD_TYPE=Debug -DWERROR=ON -DCOVERAGE=ON ..
    - make -j4 all install

build:ubuntu_cpp_clang12:
  stage: build
  needs: []
  tags:
    - docker

  script:
    # Download dependencies
    - DEPENDENCY_JOB="build:ubuntu_cpp"
    # aidge_core
    - DEPENDENCY_NAME="aidge_core"
    - !reference [.download_dependency, script]
    # aidge_backend_cpu
    - DEPENDENCY_NAME="aidge_backend_cpu"
    - !reference [.download_dependency, script]


    # Build current module
    - export CMAKE_PREFIX_PATH=../install_cpp
    - apt install -y clang-12
    - mkdir -p build_cpp
    - mkdir -p install_cpp
    - cd build_cpp
    - export CXX=/usr/bin/clang++-12
    - cmake -DCMAKE_INSTALL_PREFIX:PATH=../install_cpp -DCMAKE_BUILD_TYPE=Debug -DWERROR=ON -DCOVERAGE=ON ..
    - make -j4 all install

build:ubuntu_cpp_clang15:
  stage: build
  needs: []
  tags:
    - docker

  script:
    # Download dependencies
    - DEPENDENCY_JOB="build:ubuntu_cpp"
    # aidge_core
    - DEPENDENCY_NAME="aidge_core"
    - !reference [.download_dependency, script]
    # aidge_backend_cpu
    - DEPENDENCY_NAME="aidge_backend_cpu"
    - !reference [.download_dependency, script]

    # Build current module
    - export CMAKE_PREFIX_PATH=../install_cpp
    - apt install -y clang-15
    - mkdir -p build_cpp
    - mkdir -p install_cpp
    - cd build_cpp
    - export CXX=/usr/bin/clang++-15
    - cmake -DCMAKE_INSTALL_PREFIX:PATH=../install_cpp -DCMAKE_BUILD_TYPE=Debug -DWERROR=ON -DCOVERAGE=ON ..
    - make -j4 all install

build:ubuntu_python:
  stage: build
  needs: []
  tags:
    - docker

  script:
    # Download dependencies
    - DEPENDENCY_JOB="build:ubuntu_python"
    # aidge_core (python)
    - DEPENDENCY_NAME="aidge_core"
    - !reference [.download_dependency, script]
    # aidge_backend_cpu (python)
    - DEPENDENCY_NAME="aidge_backend_cpu"
    - !reference [.download_dependency, script]

    - python3 -m pip install virtualenv
    - virtualenv venv
    - source venv/bin/activate
    - python3 -m pip install -r requirements.txt
    - python3 -m pip install .

  artifacts:
    expire_in: 1 week
    paths:
      - venv/

# build:windows_cpp:
#   stage: build
#   needs: []
#   tags:
#     - windows

#   image: buildtools
#   before_script:
#     # Install Chocolatey
#     - Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
#     # Install dependencies
#     - choco install cmake.install --installargs '"ADD_CMAKE_TO_PATH=System"' -Y
#     - choco install git -Y
#     - choco install python -Y
#     - choco install cuda -Y
#     # Update PATH
#     - $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
#   script:
#     # Download dependencies
#     # aidge_core
#     - 'curl "https://gitlab.eclipse.org/api/v4/projects/5139/jobs/artifacts/main/download?job=build:windows_cpp" -o build_artifacts.zip'
#     - Expand-Archive -Path .\build_artifacts.zip -DestinationPath . -Force
#     - Remove-Item .\build_cpp\ -Recurse
#     # aidge_backend_cpu
#     - 'curl "https://gitlab.eclipse.org/api/v4/projects/5140/jobs/artifacts/main/download?job=build:windows_cpp" -o build_artifacts.zip'
#     - Expand-Archive -Path .\build_artifacts.zip -DestinationPath . -Force
#     - Remove-Item .\build_cpp\ -Recurse

#     - $env:CMAKE_PREFIX_PATH = '../install_cpp'
#     - mkdir -p build_cpp
#     - cd build_cpp
#     - cmake -DCMAKE_INSTALL_PREFIX:PATH=../install_cpp -DCMAKE_BUILD_TYPE=Debug ..
#     - cmake --build . -j2
#     - cmake --install . --config Debug

#   artifacts:
#     expire_in: 1 week
#     paths:
#       - build_cpp/
#       - install_cpp/

# build:windows_python:
#   stage: build
#   needs: []
#   tags:
#     - windows

#   image: buildtools
#   before_script:
#     # Install Chocolatey
#     - Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
#     # Install dependencies
#     - choco install cmake.install --installargs '"ADD_CMAKE_TO_PATH=System"' -Y
#     - choco install git -Y
#     - choco install python -Y
#     - choco install cuda -Y
#     # Update PATH
#     - $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
#   script:
#     # Download dependencies
#     # aidge_core (Python)
#     - 'curl "https://gitlab.eclipse.org/api/v4/projects/5139/jobs/artifacts/main/download?job=build:windows_python" -o build_artifacts.zip'
#     - Expand-Archive -Path .\build_artifacts.zip -DestinationPath . -Force
#     # aidge_backend_cpu (Python)
#     - 'curl "https://gitlab.eclipse.org/api/v4/projects/5140/jobs/artifacts/main/download?job=build:windows_python" -o build_artifacts.zip'
#     - Expand-Archive -Path .\build_artifacts.zip -DestinationPath . -Force

#     - python -m pip install virtualenv
#     - virtualenv venv
#     - venv\Scripts\Activate.ps1
#     - python -m pip install -r requirements.txt
#     - python -m pip install .
#   artifacts:
#     expire_in: 1 week
#     paths:
#       - venv/
