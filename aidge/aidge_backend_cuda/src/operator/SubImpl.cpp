/********************************************************************************
 * Copyright (c) 2024 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <algorithm>
#include <cassert>
#include <numeric>
#include <vector>

#include "aidge/backend/cuda/data/TensorImpl.hpp"
#include "aidge/backend/cuda/operator/SubImpl.hpp"
#include "aidge/backend/cuda/utils/CudaContext.hpp"
#include "aidge/backend/cuda/utils/CudaUtils.hpp"
#include "aidge/operator/Sub.hpp"
#include "aidge/utils/Types.h"

void Aidge::SubImpl_cuda::forward() {
    const Sub_Op& op = static_cast<const Sub_Op&>(mOp);
    // Check inputs
    AIDGE_ASSERT(op.getInput(0), "missing input in Sub operator");
    AIDGE_ASSERT(op.getInput(0)->hasImpl(), "cannot run Sub forward because the 0-th input has no implementation.");
    DataType datatypeFirstInput = op.getInput(0)->dataType();
    for (IOIndex_t i = 1; i < op.nbInputs(); ++i) {
        AIDGE_ASSERT(op.getInput(i), "missing input in Sub operator");
        AIDGE_ASSERT(op.getInput(i)->hasImpl(), "cannot run Sub forward because the {}-th input has no implementation.", i);
        AIDGE_ASSERT(op.getInput(i)->dataType() == datatypeFirstInput, "Cannot add inputs with two differents data type.");
    }

    std::vector<std::shared_ptr<Tensor>> inputFallbacks(op.nbInputs());
    std::vector<Tensor> inputs(op.nbInputs());
    std::vector<std::vector<int>> dims(op.nbInputs()); // For broadcasted dims
    std::vector<std::vector<int>> strides(op.nbInputs()); // For the cooresponding strides
    for (IOIndex_t i = 0; i < op.nbInputs(); ++i) {
        inputs[i] = op.getInput(i)->refCastFrom(inputFallbacks[i], *op.getOutput(0));

        // Get tensor dims and broadcast them
        std::copy(inputs[i].dims().begin(), inputs[i].dims().end(), std::back_inserter(dims[i]));
        dims[i].insert(dims[i].cbegin(), op.getOutput(0)->nbDims() - dims[i].size(), int(1));

        // Compute the corresponding strides
        std::vector<int> tensorStrides(dims[i].size());
        int product = 1;
        for (size_t j = dims[i].size(); j > 0; --j) {
            tensorStrides[j - 1] = product;
            product *= dims[i][j - 1];
        }
        strides[i] = tensorStrides;
    }

    switch(std::static_pointer_cast<Tensor>(mOp.getRawOutput(0))->dataType()) {
        case DataType::Float64:
            forward_<double>(inputs, dims, strides);
            break;
        case DataType::Float32:
            forward_<float>(inputs, dims, strides);
            break;
        case DataType::Float16:
            forward_<half>(inputs, dims, strides);
            break;
        default:
            AIDGE_THROW_OR_ABORT(std::runtime_error, "Data type is not supported by Backend Cuda");
    }
}

template <class T>
void Aidge::SubImpl_cuda::forward_(const std::vector<Tensor>& inputs, const std::vector<std::vector<int>>& inputsDims, const std::vector<std::vector<int>>& inputsStrides) {
    const OperatorTensor& op = static_cast<const OperatorTensor&>(mOp);
    const typename Cuda::cudnn_scaling_type<T>::type alpha = 1.0f;
    const typename Cuda::cudnn_scaling_type<T>::type beta = 0.0f;
    const typename Cuda::cudnn_scaling_type<T>::type gamma = -1.0f;
    // Create a Tensor descriptor with the broadcasted dims and strides
    cudnnTensorDescriptor_t tensorDesc;
    CHECK_CUDNN_STATUS(cudnnCreateTensorDescriptor(&tensorDesc));
    CHECK_CUDNN_STATUS(cudnnSetTensorNdDescriptor(tensorDesc, CudaContext::data_type<T>::value, inputsDims[0].size(), inputsDims[0].data(), inputsStrides[0].data()));
    // Add first input to the output
    CHECK_CUDNN_STATUS(
        cudnnAddTensor(CudaContext::cudnnHandle(),
                       &alpha,
                       tensorDesc,
                       inputs[0].getImpl()->rawPtr(),
                       &beta,
                       std::dynamic_pointer_cast<TensorImpl_cuda_>(op.getOutput(0)->getImpl())->getCudnnTensorDesc(*op.getOutput(0)),
                       std::static_pointer_cast<Tensor>(op.getRawOutput(0))->getImpl()->rawPtr())
    );
    // Substract other inputs if there are any
    for (size_t i = 1; i < op.nbInputs(); ++i)
    {
        CHECK_CUDNN_STATUS(cudnnSetTensorNdDescriptor(tensorDesc, CudaContext::data_type<T>::value, inputsDims[i].size(), inputsDims[i].data(), inputsStrides[i].data()));
        CHECK_CUDNN_STATUS(
            cudnnAddTensor(CudaContext::cudnnHandle(),
                        &gamma,
                        tensorDesc,
                        inputs[i].getImpl()->rawPtr(),
                        &alpha,
                        std::dynamic_pointer_cast<TensorImpl_cuda_>(op.getOutput(0)->getImpl())->getCudnnTensorDesc(*op.getOutput(0)),
                        std::static_pointer_cast<Tensor>(op.getRawOutput(0))->getImpl()->rawPtr())
        );
    }
    CHECK_CUDNN_STATUS(cudnnDestroyTensorDescriptor(tensorDesc));
}

void Aidge::SubImpl_cuda::backward() {
    const Sub_Op& op = static_cast<const Sub_Op&>(mOp);
    // Check output
    AIDGE_ASSERT(op.getOutput(0)->grad(), "missing output gradient in Sub operator");
    AIDGE_ASSERT(op.getOutput(0)->grad()->hasImpl(), "cannot run Sub backward because the output gradient has no implementation.");

    std::shared_ptr<Tensor> outputGradFallback;
    const auto& outputGrad = op.getOutput(0)->grad()->refCastFrom(outputGradFallback, *op.getOutput(0)->grad());

    std::vector<std::vector<int>> dims(op.nbInputs()); // For broadcasted dims
    std::vector<std::vector<int>> strides(op.nbInputs()); // For the cooresponding strides
    for (IOIndex_t i = 0; i < op.nbInputs(); ++i) {
        std::shared_ptr<Tensor> inputFallback;
        const Tensor input = op.getInput(i)->refCastFrom(inputFallback, *op.getOutput(0));

        // Get tensor dims and broadcast them
        std::copy(input.dims().begin(), input.dims().end(), std::back_inserter(dims[i]));
        dims[i].insert(dims[i].cbegin(), op.getOutput(0)->nbDims() - dims[i].size(), int(1));

        // Compute the corresponding strides
        std::vector<int> tensorStrides(dims[i].size());
        int product = 1;
        for (size_t j = dims[i].size(); j > 0; --j) {
            tensorStrides[j - 1] = product;
            product *= dims[i][j - 1];
        }
        strides[i] = tensorStrides;
    }

    switch(std::static_pointer_cast<Tensor>(mOp.getRawOutput(0))->dataType()) {
        case DataType::Float64:
            backward_<double>(outputGrad, dims, strides);
            break;
        case DataType::Float32:
            backward_<float>(outputGrad, dims, strides);
            break;
        case DataType::Float16:
            backward_<half>(outputGrad, dims, strides);
            break;
        default:
            AIDGE_THROW_OR_ABORT(std::runtime_error, "Data type is not supported by Backend Cuda");
    }
}

template <class T>
void Aidge::SubImpl_cuda::backward_(const Tensor& outputGrad, const std::vector<std::vector<int>>& inputsDims, const std::vector<std::vector<int>>& inputsStrides) {
    const OperatorTensor& op = static_cast<const OperatorTensor&>(mOp);
    const typename Cuda::cudnn_scaling_type<T>::type alpha = 1.0f;
    const typename Cuda::cudnn_scaling_type<T>::type beta = 0.0f;
    const typename Cuda::cudnn_scaling_type<T>::type gamma = -1.0f;
    for (std::size_t i = 0; i < inputsDims.size(); i++)
    {
        if (op.getInput(i)->size() == op.getOutput(0)->size())
        {
            CHECK_CUDNN_STATUS(
            cudnnAddTensor(CudaContext::cudnnHandle(),
                        i==0 ? &alpha: &gamma,
                        std::dynamic_pointer_cast<TensorImpl_cuda_>(op.getOutput(0)->getImpl())->getCudnnTensorDesc(*op.getOutput(0)),
                        outputGrad.getImpl()->rawPtr(),
                        &beta,
                        std::dynamic_pointer_cast<TensorImpl_cuda_>(op.getInput(i)->getImpl())->getCudnnTensorDesc(*op.getInput(i)),
                        op.getInput(i)->grad()->getImpl()->rawPtr()));
        }
        else // In case of broadcasting
        {
            // Gradient with respect to input_i: sum outputGrad over the broadcasted dimensions using cudnnReduceTensor
            cudnnReduceTensorDescriptor_t reduceDesc;
            CHECK_CUDNN_STATUS(cudnnCreateReduceTensorDescriptor(&reduceDesc));
            CHECK_CUDNN_STATUS(cudnnSetReduceTensorDescriptor(reduceDesc,
                                                              CUDNN_REDUCE_TENSOR_ADD,
                                                              CudaContext::data_type<T>::value,
                                                              CUDNN_PROPAGATE_NAN,
                                                              CUDNN_REDUCE_TENSOR_NO_INDICES,
                                                              CUDNN_32BIT_INDICES));

            cudnnTensorDescriptor_t outputDesc = std::dynamic_pointer_cast<TensorImpl_cuda_>(outputGrad.getImpl())->getCudnnTensorDesc(*op.getOutput(0));
            // Create a Tensor descriptor with the broadcasted dims and strides
            cudnnTensorDescriptor_t tensorDesc;
            CHECK_CUDNN_STATUS(cudnnCreateTensorDescriptor(&tensorDesc));
            CHECK_CUDNN_STATUS(cudnnSetTensorNdDescriptor(tensorDesc,
                                                          CudaContext::data_type<T>::value,
                                                          inputsDims[i].size(),
                                                          inputsDims[i].data(),
                                                          inputsStrides[i].data()));
            size_t workspaceSize;
            CHECK_CUDNN_STATUS(cudnnGetReductionWorkspaceSize(CudaContext::cudnnHandle(),
                               reduceDesc,
                               outputDesc,
                               tensorDesc,
                               &workspaceSize));

            float *d_workspace;
            CHECK_CUDA_STATUS(cudaMalloc(&d_workspace, workspaceSize));

            CHECK_CUDNN_STATUS(cudnnReduceTensor(CudaContext::cudnnHandle(),
                               reduceDesc,
                               NULL,
                               0,
                               d_workspace,
                               workspaceSize,
                               i==0 ? &alpha: &gamma,
                               outputDesc,
                               outputGrad.getImpl()->rawPtr(),
                               &beta,
                               tensorDesc,
                               op.getInput(i)->grad()->getImpl()->rawPtr()));

            CHECK_CUDNN_STATUS(cudnnDestroyTensorDescriptor(tensorDesc));
        }
    }
}