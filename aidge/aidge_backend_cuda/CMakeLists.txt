# CMake >= 3.18 is required for good support of FindCUDAToolkit
cmake_minimum_required(VERSION 3.18)

file(READ "${CMAKE_SOURCE_DIR}/version.txt" version)
add_definitions(-DPROJECT_VERSION="${version}")
file(READ "${CMAKE_SOURCE_DIR}/project_name.txt" project)

message(STATUS "Project name: ${project}")
message(STATUS "Project version: ${version}")

execute_process(
    COMMAND git rev-parse --short HEAD
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_COMMIT_HASH
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
message(STATUS "Latest git commit: ${GIT_COMMIT_HASH}")

# Define a preprocessor macro with the Git commit version
add_definitions(-DGIT_COMMIT_HASH="${GIT_COMMIT_HASH}")


# Note : project name is {project} and python module name is also {project}
set(module_name _${project}) # target name


project(${project})
set(CXX_STANDARD 14)

##############################################
# Define options
option(PYBIND "python binding" ON)
option(WERROR "Warning as error" OFF)
option(TEST "Enable tests" ON)
option(COVERAGE "Enable coverage" OFF)
option(ENABLE_ASAN "Enable ASan (AddressSanitizer) for runtime analysis of memory use (over/underflow, memory leak, ...)" OFF)

##############################################
# Import utils CMakeLists
set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${CMAKE_SOURCE_DIR}/cmake")
include(PybindModuleCreation)

if(CMAKE_COMPILER_IS_GNUCXX AND COVERAGE)
    Include(CodeCoverage)
endif()

enable_language(CUDA)

message(STATUS "Cuda compiler version = ${CMAKE_CUDA_COMPILER_VERSION}")
# Define a preprocessor macro with the Cuda compiler version
add_definitions(-DCUDA_COMPILER_VERSION="${CMAKE_CUDA_COMPILER_VERSION}")


##############################################
# Find system dependencies
find_package(CUDAToolkit REQUIRED)

find_package(aidge_core REQUIRED)
if(TEST)
    find_package(aidge_backend_cpu REQUIRED)
endif()
##############################################
# Create target and set properties

file(GLOB_RECURSE src_files "src/*.cpp" "src/*.cu")
file(GLOB_RECURSE inc_files "include/*.hpp")

add_library(${module_name} ${src_files} ${inc_files})
target_link_libraries(${module_name}
    PUBLIC
        _aidge_core # _ is added because we link the target not the project
        CUDA::cudart
        CUDA::cublas
        cudnn
)

if( ${ENABLE_ASAN} )
    message("Building ${module_name} with ASAN.")
    set(SANITIZE_FLAGS -fsanitize=address -fno-omit-frame-pointer)
    target_link_libraries(${module_name}
        PUBLIC
            -fsanitize=address
    )
    target_compile_options(${module_name}
        PRIVATE
            ${SANITIZE_FLAGS}
    )
endif()

if(TEST)
    target_link_libraries(${module_name}
    PUBLIC
        _aidge_backend_cpu # _ is added because we link the target not the project
    )
endif()

#Set target properties
target_include_directories(${module_name}
    PUBLIC
        $<INSTALL_INTERFACE:include>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

if(NOT DEFINED CMAKE_CUDA_STANDARD)
    set(CMAKE_CUDA_STANDARD 14)
    set(CMAKE_CUDA_STANDARD_REQUIRED ON)
endif()

set_property(TARGET ${module_name} PROPERTY POSITION_INDEPENDENT_CODE ON)
set_target_properties(${module_name} PROPERTIES CUDA_SEPARABLE_COMPILATION ON)

# PYTHON BINDING
if (PYBIND)
    generate_python_binding(${project} ${module_name})

    # Handles Python + pybind11 headers dependencies
    target_link_libraries(${module_name}
        PUBLIC
            pybind11::pybind11
        PRIVATE
            Python::Python
        )
endif()

target_compile_features(${module_name} PRIVATE cxx_std_14)

target_compile_options(${module_name} PRIVATE
    $<$<COMPILE_LANGUAGE:CPP>:$<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>,$<CXX_COMPILER_ID:GNU>>:
    -Wall -Wextra -Wold-style-cast -Winline -pedantic -Werror=narrowing -Wshadow $<$<BOOL:${WERROR}>:-Werror>>>)
target_compile_options(${module_name} PRIVATE
    $<$<COMPILE_LANGUAGE:CUDA>:
    -Wall>)
target_compile_options(${module_name} PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:
    /W4>)

if(CMAKE_COMPILER_IS_GNUCXX AND COVERAGE)
    append_coverage_compiler_flags()
endif()

##############################################
# Installation instructions

include(GNUInstallDirs)
set(INSTALL_CONFIGDIR ${CMAKE_INSTALL_LIBDIR}/cmake/${project})

install(TARGETS ${module_name} EXPORT ${project}-targets
  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
  ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
  RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
  INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

install(DIRECTORY include/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})

#Export the targets to a script

install(EXPORT ${project}-targets
 FILE "${project}-targets.cmake"
 DESTINATION ${INSTALL_CONFIGDIR}
#  COMPONENT ${module_name}
)

#Create a ConfigVersion.cmake file
include(CMakePackageConfigHelpers)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/${project}-config-version.cmake"
    VERSION ${version}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file("${project}-config.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/${project}-config.cmake"
    INSTALL_DESTINATION ${INSTALL_CONFIGDIR}
)

#Install the config, configversion and custom find modules
install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/${project}-config.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/${project}-config-version.cmake"
    DESTINATION ${INSTALL_CONFIGDIR}
)

##############################################
## Exporting from the build tree
export(EXPORT ${project}-targets
    FILE "${CMAKE_CURRENT_BINARY_DIR}/${project}-targets.cmake")


##############################################
## Add test
if(TEST)
    enable_testing()
    add_subdirectory(unit_tests)
endif()
