################################################################################
# Pre-configured CI/CD for your Aidge module.
#
# Three stages are already pre-configured to run on Eclipse Aidge CI:
# - build: ubuntu_cpp, ubuntu_python and windows_cpp;
# - test: ubuntu_cpp, ubuntu_python and windows_cpp;
# - coverage: ubuntu_cpp and ubuntu_python.
#
# If your project is pure C++ or pure Python, you can remove the "_python" or 
# "_cpp" jobs respectively.
# "ubuntu" jobs require an Ubuntu runner with a docker executor with tag 
# "docker".
# "windows" jobs require a Windows runner with a docker-windows executor with 
# tag "windows".
#
# You can change the docker images in the YML scripts directly. The default 
# images are:
# - nvidia/cuda:12.2.0-devel-ubuntu22.04 for Ubuntu jobs;
# - buildtools for Windows jobs, built on top of 
#   mcr.microsoft.com/windows/servercore:ltsc2022 with Microsoft Visual Studio 
#   2022 BuildTools installed.
#
# See Aidge project wiki for more details on how to setup your own docker images
# and Gitlab runners.
################################################################################

stages:
  # Build
  - build
  # Unit test stage
  - test
  # Code coverage
  - coverage

include:
  - local: '/.gitlab/ci/_global.gitlab-ci.yml'
  - local: '/.gitlab/ci/build.gitlab-ci.yml'
  - local: '/.gitlab/ci/test.gitlab-ci.yml'
  - local: '/.gitlab/ci/coverage.gitlab-ci.yml'
