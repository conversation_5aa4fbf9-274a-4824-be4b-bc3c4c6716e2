# Aidge CPU library

You can find in this folder the library that implements :
- Computer vision databases 
- The OpenCV tensor, operators and stimulis

So far be sure to have the correct requirements to use this library
- GCC
- OpenCV >= 3.0
- Make
- CMake
- aidge_core
- aidge_backend_cpu
- Python (optional, if you have no intend to use this library in python with pybind)

## Pip installation

You will need to install first the aidge_core and aidge_backend_cpu libraries before installing aidge_backend_opencv.
Then run in your python environnement : 
``` bash
pip install . -v
```