cmake_minimum_required(VERSION 3.15)

file(READ "${CMAKE_SOURCE_DIR}/version.txt" version)
file(READ "${CMAKE_SOURCE_DIR}/project_name.txt" project)

message(STATUS "Project name: ${project}")
message(STATUS "Project version: ${version}")

# Note : project name is {project} and python module name is also {project} 
set(module_name _${project}) # target name


project(${project})

##############################################
# Define options
option(PYBIND "python binding" ON)
option(WERROR "Warning as error" OFF)
option(TEST "Enable tests" ON)
option(COVERAGE "Enable coverage" OFF)

##############################################
# Import utils CMakeLists
set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${CMAKE_SOURCE_DIR}/cmake")
include(PybindModuleCreation)

if(CMAKE_COMPILER_IS_GNUCXX AND COVERAGE)
    Include(CodeCoverage)
endif()

##############################################
# Find system dependencies
find_package(aidge_core REQUIRED)
find_package(OpenCV REQUIRED)
if (${OpenCV_VERSION_MAJOR} LESS 3)
    MESSAGE(FATAL_ERROR "Unsupported OpenCV version. OpenCV 3.0.0+ is required")
endif()

##############################################
# Create target and set properties

file(GLOB_RECURSE src_files "src/*.cpp")
file(GLOB_RECURSE inc_files "include/*.hpp")

add_library(${module_name} ${src_files} ${inc_files})
target_link_libraries(${module_name}
    PUBLIC
        _aidge_core # _ is added because we link the target not the project
)

target_link_libraries(${module_name} PUBLIC ${OpenCV_LIBS})
target_include_directories(${module_name} PUBLIC ${OpenCV_INCLUDE_DIRS})

#Set target properties
set_property(TARGET ${module_name} PROPERTY POSITION_INDEPENDENT_CODE ON)
target_include_directories(${module_name}
    PUBLIC
        $<INSTALL_INTERFACE:include>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# PYTHON BINDING
if (PYBIND)
    generate_python_binding(${project} ${module_name})

    # Handles Python + pybind11 headers dependencies
    target_link_libraries(${module_name}
        PUBLIC 
            pybind11::pybind11
        PRIVATE
            Python::Python
        )
endif()

target_compile_features(${module_name} PRIVATE cxx_std_14)

target_compile_options(${module_name} PRIVATE
    $<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>,$<CXX_COMPILER_ID:GNU>>:
    -Wall -Wextra -Wold-style-cast -Winline -pedantic -Werror=narrowing -Wshadow $<$<BOOL:${WERROR}>:-Werror>>)
    # -O0 -g -Wall -Wextra -Wold-style-cast -Winline -pedantic -Werror=narrowing -Wshadow $<$<BOOL:${WERROR}>:-Werror>>)
target_compile_options(${module_name} PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:
    /W4>)

if(CMAKE_COMPILER_IS_GNUCXX AND COVERAGE)
    append_coverage_compiler_flags()
endif()

##############################################
# Installation instructions

include(GNUInstallDirs)
set(INSTALL_CONFIGDIR ${CMAKE_INSTALL_LIBDIR}/cmake/${project})

install(TARGETS ${module_name} EXPORT ${project}-targets
  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
  ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
  RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
#   INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

install(DIRECTORY include/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})

#Export the targets to a script

install(EXPORT ${project}-targets
 FILE "${project}-targets.cmake"
 DESTINATION ${INSTALL_CONFIGDIR}
 COMPONENT ${module_name} 
)  

#Create a ConfigVersion.cmake file
include(CMakePackageConfigHelpers)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/${project}-config-version.cmake"
    VERSION ${version}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file("${project}-config.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/${project}-config.cmake"
    INSTALL_DESTINATION ${INSTALL_CONFIGDIR}
)

#Install the config, configversion and custom find modules
install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/${project}-config.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/${project}-config-version.cmake"
    DESTINATION ${INSTALL_CONFIGDIR}
)

##############################################
## Exporting from the build tree
export(EXPORT ${project}-targets
    FILE "${CMAKE_CURRENT_BINARY_DIR}/${project}-targets.cmake")


##############################################
## Add test
if(TEST)
    enable_testing()
    add_subdirectory(unit_tests)
endif()
