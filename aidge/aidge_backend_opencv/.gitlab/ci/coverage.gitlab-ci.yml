coverage:ubuntu_cpp:
  stage: coverage
  needs: ["build:ubuntu_cpp"]
  tags:
    - docker
  script:
    - cd build_cpp
    - ctest --output-on-failure
    # HTML report for visualization
    - gcovr --html-details --exclude-unreachable-branches -o coverage.html --root ${CI_PROJECT_DIR} --filter '\.\./include/' --filter '\.\./src/'
    # Coberta XML report for Gitlab integration
    - gcovr --xml-pretty --exclude-unreachable-branches --print-summary -o coverage.xml --root ${CI_PROJECT_DIR} --filter '\.\./include/' --filter '\.\./src/'
  coverage: /^\s*lines:\s*\d+.\d+\%/
  artifacts:
    name: ${CI_JOB_NAME}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}
    expire_in: 2 days
    reports:
      coverage_report:
        coverage_format: cobertura
        path: build_cpp/coverage.xml

coverage:ubuntu_python:
  stage: coverage
  needs: ["build:ubuntu_python"]
  tags:
    - docker
  script:
    - source venv/bin/activate
    - python3 -m pip install numpy coverage
    - cd ${CI_PROJECT_NAME}
    # Retrieve the installation path of the module, since it is installed with pip.
    - export MODULE_LOCATION=`python -c "import ${CI_PROJECT_NAME} as _; print(_.__path__[0])"`
    - python3 -m coverage run --source=$MODULE_LOCATION -m unittest discover -s unit_tests/ -v -b
    - python3 -m coverage report
    - python3 -m coverage xml
  coverage: '/(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: ${CI_PROJECT_NAME}/coverage.xml
