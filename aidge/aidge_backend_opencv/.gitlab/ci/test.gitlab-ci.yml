test:ubuntu_cpp:
  stage: test
  needs: ["build:ubuntu_cpp"]
  tags:
    - docker
  script:
    - cd build_cpp
    - ctest --output-junit ctest-results.xml --output-on-failure
  artifacts:
    reports:
      junit: build_cpp/ctest-results.xml

test:ubuntu_python:
  stage: test
  needs: ["build:ubuntu_python"]
  tags:
    - docker
  script:
    - source venv/bin/activate
    - cd ${CI_PROJECT_NAME}
    - python3 -m pip install numpy unittest-xml-reporting
    - python3 -m pip list
    # Run on discovery all tests located in core/unit_tests/python
    - python3 -m xmlrunner discover -s unit_tests/ -v -b --output-file xmlrunner-results.xml
  artifacts:
    reports:
      junit: ${CI_PROJECT_NAME}/xmlrunner-results.xml

# test:windows_cpp:
#   stage: test
#   needs: ["build:windows_cpp"]
#   tags:
#     - windows
#   image: buildtools
#   before_script:
#     # Install Chocolatey
#     - Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
#     # Install dependencies
#     - choco install cmake.install --installargs '"ADD_CMAKE_TO_PATH=System"' -Y
#     - choco install python -Y
#     # Update PATH
#     - $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
#   script:
#     - cd build_cpp
#     - ctest --output-junit ctest-results.xml --output-on-failure
#   artifacts:
#     reports:
#       junit: build_cpp/ctest-results.xml

