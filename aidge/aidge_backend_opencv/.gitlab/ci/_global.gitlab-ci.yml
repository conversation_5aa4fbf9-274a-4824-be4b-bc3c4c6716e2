################################################################################
# Centralized definitions of common job parameter values.                      #
# Parameters with many optional configurations may be in separate files.       #
#                                                                              #
################################################################################
variables:
  GIT_SUBMODULE_STRATEGY: recursive
  OMP_NUM_THREADS: 4
  GIT_SSL_NO_VERIFY: 1
  DEBIAN_FRONTEND: noninteractive

# See https://docs.gitlab.com/ee/ci/yaml/workflow.html#switch-between-branch-pipelines-and-merge-request-pipelines
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH

default:
  image: nvidia/cuda:12.2.0-devel-ubuntu22.04
  before_script:
    - apt update
    - apt install -y cmake cppcheck python-is-python3 pip git gcovr unzip curl libopencv-dev
