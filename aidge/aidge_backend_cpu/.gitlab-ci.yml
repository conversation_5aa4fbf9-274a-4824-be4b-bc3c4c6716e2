###############################################################################
#            Aidge Continious Integration and Continious Deployment            #
#                                                                             #
###############################################################################

stages:
  - static_analysis
  - build
  - test
  - coverage
  - release
  - deploy

include:
  - project: 'eclipse/aidge/gitlab_shared_files'
    ref: 'main'
    file: # choose which jobs to run by including the corresponding files.
      - '.gitlab/ci/ubuntu_cpp.gitlab-ci.yml'

      - '.gitlab/ci/ubuntu_python.gitlab-ci.yml'
      - '.gitlab/ci/release/cibuildwheel_ubuntu.gitlab-ci.yml'

      - '.gitlab/ci/windows_cpp.gitlab-ci.yml'
      - '.gitlab/ci/windows_python.gitlab-ci.yml'
      - '.gitlab/ci/release/cibuildwheel_windows.gitlab-ci.yml'
