@PACKAGE_INIT@

include(CMakeFindDependencyMacro)
find_dependency(aidge_core)
set(AIDGE_REQUIRES_OPENMP @AIDGE_REQUIRES_OPENMP@)
if (<PERSON><PERSON>GE_REQUIRES_OPENMP)
    find_dependency(OpenMP)
endif()
set(AIDGE_REQUIRES_OPENSSL @AIDGE_REQUIRES_OPENSSL@)
if (AIDGE_REQUIRES_OPENSSL)
    find_dependency(OpenSSL)
endif()

include(CMakeFindDependencyMacro)

include(${CMAKE_CURRENT_LIST_DIR}/aidge_backend_cpu-config-version.cmake)

include(${CMAKE_CURRENT_LIST_DIR}/aidge_backend_cpu-targets.cmake)
