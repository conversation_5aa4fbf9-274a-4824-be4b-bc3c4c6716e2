/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <array>
#include <memory>

#include <catch2/catch_test_macros.hpp>

#include "aidge/backend/cpu/data/TensorImpl.hpp"
#include "aidge/backend/cpu/operator/MaxPoolingImpl.hpp"
#include "aidge/data/DataType.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/operator/MaxPooling.hpp"

using namespace Aidge;


TEST_CASE("[cpu/operator] MaxPooling(forward)", "[MaxPooling][CPU]") {
    std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,2,5,5> { //NCHW
        {
            {
                {{-0.3848,  0.2166, -0.4373,  0.6142,  0.5277},
                 {0.7995,  0.3638, -1.4589, -1.0843,  1.0918},
            	 {0.7147,  0.0936, -1.2902,  1.2037,  0.4874},
                 {-0.5981,  2.1184, -0.9175,  1.3859,  0.3305},
                 {-1.7700,  0.0563, -0.3914,  0.0538, -0.3955}},

                {{-3.1409, -0.4554,  0.0524,  2.2291,  0.4859},
                 {-0.7465, -0.6567, -2.3703, -0.6386, -1.4152},
                 { 2.2329, -0.5850,  0.0700,  1.2838, -1.7363},
                 { 0.2139,  0.0624, -1.0689, -0.8221, -0.8038},
                 { 0.1886, -0.7840, -0.2313,  0.2651, -1.6244}}
            },
            {
                {{ 0.4371,  1.6417,  0.9129,  0.6325,  0.5438},
                 {-2.3552, -0.8850, -0.0232, -0.5462, -1.2011},
                 {1.7653, -1.6668, -1.0814,  0.6182,  1.2071},
                 {0.9541, -0.5133,  0.8664, -0.8892,  1.4585},
                 {1.0220, -0.5107,  0.1829, -0.2301, -0.4268}},

                {{ 1.0429,  0.6279, -0.2875,  0.7187, -0.1500},
                 {1.6041,  2.9635,  1.4172, -0.7517,  0.5441},
                 {-0.2276,  0.0857,  0.6776, -0.1389, -0.0614},
                 {-0.1547, -0.3435,  0.0650, -0.5095, -1.8073},
                 {1.7217,  0.3999, -0.5953,  1.0604, -0.4126}}
            }
        }
    });
    SECTION("Stride") {
        std::shared_ptr<MaxPooling_Op<2>> op =
		std::make_shared<MaxPooling_Op<2>>(
			std::array<std::size_t, 2>({2, 2}),
			std::array<std::size_t, 2>({2, 2})
		);

        Tensor myOutput = Array4D<float,2,2,2,2> {
            {
                {
                    {{  0.7995,  0.6142},
                     { 2.1184,  1.3859}},
                    {{ -0.4554,  2.2291},
                     {  2.2329,  1.2838}}
                },
                {
                    {{1.6417,  0.9129},
                     {1.7653,  0.8664}},
                    {{2.9635,  1.4172},
                     {0.0857,  0.6776}}
                }
            }
        };
        op->associateInput(0,myInput);
        op->setDataType(DataType::Float32);
        op->setBackend("cpu");
        op->forward();
        op->getOutput(0)->print();
        REQUIRE(*(op->getOutput(0)) == myOutput);
    }
    SECTION("Dilation") {
        std::shared_ptr<Node> myMaxPool = MaxPooling({2,2}, "mycdw", {2,2}, {2,2}); // Dilation 2x2
        auto op = std::static_pointer_cast<OperatorTensor>(myMaxPool -> getOperator());

        std::shared_ptr<Tensor> myOutput = std::make_shared<Tensor>(Array4D<float,2,2,2,2> {
            {
                {
                    {
                        {0.71470, 0.52770},
                        {0.71470, 0.48740}
                    },
                    {
                        {2.23290, 0.48590},
                        {2.23290, 0.07000}
                    }
                },
                {
                    {
                        {1.76530, 1.20710},
                        {1.76530, 1.20710}
                    },
                    {
                        {1.04290, 0.67760},
                        {1.72170, 0.67760}
                    }
                }
            }
        });
        myMaxPool->getOperator()->associateInput(0,myInput);
        myMaxPool->getOperator()->setDataType(DataType::Float32);
        myMaxPool->getOperator()->setBackend("cpu");
        myMaxPool->forward();
        op->getOutput(0)->print();
        REQUIRE(*(op->getOutput(0)) == *myOutput);
    }
    SECTION("Ceil Mode") {
        std::shared_ptr<Tensor> myInput4 = std::make_shared<Tensor>(Array4D<float,1,1,5,5> { // NCHW
        {
            {
                {
                    { 1,  2,  3,  4,  5},
                    { 6,  7,  8,  9, 10},
                    {11, 12, 13, 14, 15},
                    {16, 17, 18, 19, 20},
                    {21, 22, 23, 24, 25}
                }
            }
        }
        });

        // MaxPool with ceil_mode = true
        std::shared_ptr<Node> myMaxPool1 = MaxPooling({2,2}, "mycdw", {2,2}, {1,1}, true);
        auto op1 = std::static_pointer_cast<OperatorTensor>(myMaxPool1 -> getOperator());

        std::shared_ptr<Tensor> myOutput4 = std::make_shared<Tensor>(Array4D<float,1,1,3,3> {
            {
                {
                    {
                        {  7.0,  9.0, 10.0 },
                        { 17.0, 19.0, 20.0 },
                        { 22.0, 24.0, 25.0 }
                    }
                }
            }
        });
        op1->associateInput(0, myInput4);
        op1->setDataType(DataType::Float32);
        op1->setBackend("cpu");
        myMaxPool1->forward();
        op1->getOutput(0)->print();
        REQUIRE(*(op1->getOutput(0)) == *myOutput4);

        // MaxPool with ceil_mode = false
        std::shared_ptr<Node> myMaxPool2 = MaxPooling({2,2}, "mycdw", {2,2}, {1,1}, false);
        auto op2 = std::static_pointer_cast<OperatorTensor>(myMaxPool2 -> getOperator());
        std::shared_ptr<Tensor> myOutput5 = std::make_shared<Tensor>(Array4D<float,1,1,2,2> {
            {
                {
                    {
                        {  7.0,  9.0 },
                        { 17.0, 19.0 }
                    }
                }
            }
        });
        op2->associateInput(0, myInput4);
        op2->setDataType(DataType::Float32);
        op2->setBackend("cpu");
        myMaxPool2->forward();
        op2->getOutput(0)->print();
        REQUIRE(*(op2->getOutput(0)) == *myOutput5);
    }
}



TEST_CASE("[cpu/operator] MaxPooling(backward)", "[MaxPooling][CPU]") {
    std::shared_ptr<Tensor> myInput =
	std::make_shared<Tensor>(Array4D<float,2,2,5,5> { //NCHW
		{
		    {
			{{-0.3848,  0.2166, -0.4373,  0.6142,  0.5277},
			 {0.7995,  0.3638, -1.4589, -1.0843,  1.0918},
			 {0.7147,  0.0936, -1.2902,  1.2037,  0.4874},
			 {-0.5981,  2.1184, -0.9175,  1.3859,  0.3305},
			 {-1.7700,  0.0563, -0.3914,  0.0538, -0.3955}},

			{{-3.1409, -0.4554,  0.0524,  2.2291,  0.4859},
			 {-0.7465, -0.6567, -2.3703, -0.6386, -1.4152},
			 { 2.2329, -0.5850,  0.0700,  1.2838, -1.7363},
			 { 0.2139,  0.0624, -1.0689, -0.8221, -0.8038},
			 { 0.1886, -0.7840, -0.2313,  0.2651, -1.6244}}
		    },
		    {
			{{ 0.4371,  1.6417,  0.9129,  0.6325,  0.5438},
			 {-2.3552, -0.8850, -0.0232, -0.5462, -1.2011},
			 {1.7653, -1.6668, -1.0814,  0.6182,  1.2071},
			 {0.9541, -0.5133,  0.8664, -0.8892,  1.4585},
			 {1.0220, -0.5107,  0.1829, -0.2301, -0.4268}},

			{{ 1.0429,  0.6279, -0.2875,  0.7187, -0.1500},
			 {1.6041,  2.9635,  1.4172, -0.7517,  0.5441},
			 {-0.2276,  0.0857,  0.6776, -0.1389, -0.0614},
			 {-0.1547, -0.3435,  0.0650, -0.5095, -1.8073},
			 {1.7217,  0.3999, -0.5953,  1.0604, -0.4126}}
		    }
		}
	});
    SECTION("Stride") {
        std::shared_ptr<MaxPooling_Op<2>> op =
		std::make_shared<MaxPooling_Op<2>>(
			std::array<std::size_t, 2>({2,2}),
			std::array<std::size_t, 2>({2,2})
		);

		Tensor grad = Array4D<float,2,2,5,5> {
			{
				{
					{{0, 0, 0, 1, 0},
					{1, 0, 0, 0, 0},
					{0, 0, 0, 0, 0},
					{0, 1, 0, 1, 0},
					{0, 0, 0, 0, 0}},

					{{0, 1, 0, 1, 0},
					{0, 0, 0, 0, 0},
					{1, 0, 0, 1, 0},
					{0, 0, 0, 0, 0},
					{0, 0, 0, 0, 0}}
				},
				{
					{{0, 1, 1, 0, 0},
					{0, 0, 0, 0, 0},
					{1, 0, 0, 0, 0},
					{0, 0, 1, 0, 0},
					{0, 0, 0, 0, 0}},

					{{0, 0, 0, 0, 0},
					{0, 1, 1, 0, 0},
					{0, 1, 1, 0, 0},
					{0, 0, 0, 0, 0},
					{0, 0, 0, 0, 0}}
				}
			}
		};
        op->associateInput(0,myInput);
        op->setDataType(DataType::Float32);
        op->setBackend("cpu");
	op->backward();
	//op->getInput(0)->grad()->print();
        REQUIRE(*(op->getInput(0)->grad()) == grad);
    }
    SECTION("Dilation"){
        std::shared_ptr<Node> myMaxPool = MaxPooling({2,2}, "mycdw", {2,2}, {2,2}); // Dilation 2x2
        auto op = std::static_pointer_cast<OperatorTensor>(myMaxPool -> getOperator());

	Tensor grad = Array4D<float,2,2,5,5> {
		{{{{0., 0., 0., 0., 1.},
		  {0., 0., 0., 0., 0.},
		  {2., 0., 0., 0., 1.},
		  {0., 0., 0., 0., 0.},
		  {0., 0., 0., 0., 0.}},

		 {{0., 0., 0., 0., 1.},
		  {0., 0., 0., 0., 0.},
		  {2., 0., 1., 0., 0.},
		  {0., 0., 0., 0., 0.},
		  {0., 0., 0., 0., 0.}}},


		{{{0., 0., 0., 0., 0.},
		  {0., 0., 0., 0., 0.},
		  {2., 0., 0., 0., 2.},
		  {0., 0., 0., 0., 0.},
		  {0., 0., 0., 0., 0.}},

		 {{1., 0., 0., 0., 0.},
		  {0., 0., 0., 0., 0.},
		  {0., 0., 2., 0., 0.},
		  {0., 0., 0., 0., 0.},
		  {1., 0., 0., 0., 0.}}}}
	};
        myMaxPool->getOperator()->associateInput(0,myInput);
        myMaxPool->getOperator()->setDataType(DataType::Float32);
        myMaxPool->getOperator()->setBackend("cpu");
	op->backward();
	//op->getInput(0)->grad()->print();
        REQUIRE(*(op->getInput(0)->grad()) == grad);
    }
    SECTION("Ceil mode"){
		std::shared_ptr<Tensor> myInput4 =
			std::make_shared<Tensor>(Array4D<float,1,1,5,5> { // NCHW
				{{{
					{ 1,  2,  3,  4,  5},
					{ 6,  7,  8,  9, 10},
					{11, 12, 13, 14, 15},
					{16, 17, 18, 19, 20},
					{21, 22, 23, 24, 25}
				}}}
			});

		// MaxPool with ceil_mode = true
		std::shared_ptr<Node> myMaxPool1 =
			MaxPooling({2,2}, "mycdw", {2,2}, {1,1}, true);
		auto op1 = std::static_pointer_cast<OperatorTensor>(
			myMaxPool1 -> getOperator()
		);
		Tensor grad = Array4D<float,1,1,5,5> {
			{{{
				{0, 0, 0, 0, 0},
				{0, 1, 0, 1, 1},
				{0, 0, 0, 0, 0},
				{0, 1, 0, 1, 1},
				{0, 1, 0, 1, 1}
			}}}
		};

		op1->associateInput(0, myInput4);
		op1->setDataType(DataType::Float32);
		op1->setBackend("cpu");
		op1->backward();
		//op1->getInput(0)->grad()->print();
		REQUIRE(*(op1->getInput(0)->grad()) == grad);

		// MaxPool with ceil_mode = false
		std::shared_ptr<Node> myMaxPool2 =
			MaxPooling({2,2}, "mycdw", {2,2}, {1,1}, false);
		auto op2 = std::static_pointer_cast<OperatorTensor>(
				myMaxPool2 -> getOperator()
			);

		Tensor grad2 = Array4D<float,1,1,5,5> {
			{{{
				{0, 0, 0, 0, 0},
				{0, 1, 0, 1, 0},
				{0, 0, 0, 0, 0},
				{0, 1, 0, 1, 0},
				{0, 0, 0, 0, 0}
			}}}
		};

		myInput4->setGrad(nullptr);
		op2->associateInput(0, myInput4);
		op2->setDataType(DataType::Float32);
		op2->setBackend("cpu");
		myMaxPool2->backward();
		op2->getInput(0)->grad()->print();
		REQUIRE(*(op2->getInput(0)->grad()) == grad2);
	}
}
