/********************************************************************************
 * Copyright (c) 2025 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include "aidge/backend/cpu/operator/HeavisideImpl_kernels.hpp"

#include <memory>
#include <cmath>
#include <cstdlib>
#include <random>

#include <catch2/catch_test_macros.hpp>

#include "aidge/backend/cpu/operator/HeavisideImpl.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/operator/Atan.hpp"
#include "aidge/operator/Mul.hpp"
#include "aidge/operator/Producer.hpp"
#include "aidge/utils/TensorUtils.hpp"
#include "aidge/utils/Types.h"


namespace Aidge
{

TEST_CASE("[cpu/operator] Heaviside(forward)", "[Heaviside][CPU]") {

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> valueDist(-1.0f, 1.0f);
    std::uniform_int_distribution<std::size_t> dimSizeDist(std::size_t(2), std::size_t(10));
    std::uniform_int_distribution<std::size_t> nbDimsDist(std::size_t(1), std::size_t(5));

    SECTION("1D Tensor") {

        std::shared_ptr<Tensor> input0 = std::make_shared<Tensor>(Array1D<float,10> {
            {0, 1, 2,-3, 4,-5,-6, 7, 8, 9}
        });
        std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array1D<float,10> {
            {0.5, 1, 1, 0, 1, 0, 0, 1, 1, 1}
        });

        std::shared_ptr<Node> heaviside = Heaviside(0.5);
        auto op = std::static_pointer_cast<OperatorTensor>(heaviside->getOperator());
        op->associateInput(0, input0);
        op->setBackend("cpu");
        op->setDataType(DataType::Float32);

        op->forward();
        REQUIRE(approxEq<float>(*op->getOutput(0),*expectedOutput));
    }

    SECTION("+1-D Tensor")
    {
        auto dims = std::vector<std::size_t>();
        auto nbDims = nbDimsDist(gen);

        for (auto i = 0u; i < nbDims; ++i) {
            dims.push_back(dimSizeDist(gen));
        }

        auto numberOfElements = std::accumulate(dims.cbegin(), dims.cend(), std::size_t(1), std::multiplies<std::size_t>());
        float* inputArray = new float[numberOfElements];
        float* resultArray = new float[numberOfElements];

        for(auto i = 0u; i < numberOfElements; ++i)
        {
            inputArray[i] = valueDist(gen);
            resultArray[i] = inputArray[i] > 0 ? 1 : (inputArray[i] == 0 ? 0.5 : 0);
        }

        auto T0 = std::make_shared<Tensor>();
        T0->setDataType(DataType::Float32);
        T0->setBackend("cpu");

        auto T1 = std::make_shared<Tensor>();
        T1->setDataType(DataType::Float32);
        T1->setBackend("cpu");

        T0->resize(dims);
        T0->getImpl()->setRawPtr(inputArray, numberOfElements);
        T1->resize(dims);
        T1->getImpl()->setRawPtr(resultArray, numberOfElements);

        std::shared_ptr<Node> heaviside = Heaviside(0.5);
        auto op = std::static_pointer_cast<OperatorTensor>(heaviside->getOperator());
        op->associateInput(0, T0);
        op->setBackend("cpu");
        op->setDataType(DataType::Float32);

        op->forward();

        REQUIRE(approxEq<float>(*(op->getOutput(0)), *T1));
    }
}

TEST_CASE("[cpu/operator] Heaviside(backward)", "[Heaviside][CPU]") {

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> valueDist(-2.0f, 2.0f);
    std::uniform_int_distribution<std::size_t> sizeDist(5, 100);

    const std::size_t tensorSize = sizeDist(gen);

    auto hs = Heaviside(1.0f);
    auto op = std::static_pointer_cast<OperatorTensor>(hs->getOperator());
    op->setDataType(DataType::Float32);
    op->setBackend("cpu");



    auto inputTensor = std::make_shared<Tensor>(std::vector<std::size_t>{tensorSize});
    inputTensor->setDataType(DataType::Float32);
    inputTensor->setBackend("cpu");
    auto* inputData = static_cast<float*>(inputTensor->getImpl()->rawPtr());

    for(std::size_t i = 0; i < tensorSize; ++i) {
        inputData[i] = valueDist(gen);
    }

    // Compare it to the real Atan implementation
    auto mul = Mul();
    auto pi = std::make_shared<Tensor>(Array1D<float,1>{M_PI});
    auto producer = Producer(pi);
    auto atan = Atan();
    auto mulOp = std::static_pointer_cast<OperatorTensor>(mul->getOperator());
    auto piOp = std::static_pointer_cast<OperatorTensor>(producer->getOperator());
    auto atanOp = std::static_pointer_cast<OperatorTensor>(atan->getOperator());
    mulOp->setBackend("cpu");
    piOp->setBackend("cpu");
    atanOp->setBackend("cpu");
    mulOp->setDataType(DataType::Float32);
    piOp->setDataType(DataType::Float32);
    atanOp->setDataType(DataType::Float32);


    producer->addChild(mul,0,0);
    mulOp->setInput(IOIndex_t(1),  inputTensor);
    mulOp->forward();
    auto outmul = mulOp->getOutput(0);
    atanOp->setInput(0, inputTensor);
    atanOp->forward();

    auto gradTensor = std::make_shared<Tensor>(std::vector<std::size_t>{tensorSize});
    gradTensor->setDataType(DataType::Float32);
    gradTensor->setBackend("cpu");
    auto* gradData = static_cast<float*>(gradTensor->getImpl()->rawPtr());

    for (std::size_t i = 0; i < tensorSize; ++i) {
        gradData[i] = valueDist(gen);
    }

    op->setInput(IOIndex_t(0), inputTensor);
    op->forward();

    auto output = op->getOutput(0);
    output->setGrad(gradTensor);

    // Backward pass
    op->backward();

    atanOp->setOutput(0, outmul);
    atanOp->getOutput(0)->setGrad(gradTensor);
    atanOp->backward();

    // Compute expected gradient manually
    auto expectedGrad = std::make_shared<Tensor>(std::vector<std::size_t>{tensorSize});
    expectedGrad->setDataType(DataType::Float32);
    expectedGrad->setBackend("cpu");
    auto* expectedGradData = static_cast<float*>(expectedGrad->getImpl()->rawPtr());

    for (std::size_t i = 0; i < tensorSize; ++i) {
        expectedGradData[i] = gradData[i] * (1.0f / (1.0f + (inputData[i] * M_PI) * (inputData[i] * M_PI)));
    }

    // Compare actual gradient with expected gradient
    REQUIRE(approxEq<float>(*(op->getInput(0)->grad()), *expectedGrad));

    // Compare Atan(pi*input) to expected Gradient
    REQUIRE(approxEq<float>(*(atanOp->getInput(0)->grad()), *expectedGrad));
}

}
