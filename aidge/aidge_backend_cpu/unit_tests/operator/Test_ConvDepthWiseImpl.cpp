/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <memory>

#include <catch2/catch_test_macros.hpp>
#include <fmt/core.h>

#include "aidge/backend/cpu/operator/ConvDepthWiseImpl.hpp"
#include "aidge/data/Data.hpp"  // DataType
#include "aidge/data/Tensor.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/operator/ConvDepthWise.hpp"
#include "aidge/utils/TensorUtils.hpp"

using namespace Aidge;

/**
 * @brief ConvDepthWise reference cpp backend forward implmentation tests.
 *
 * Summary
 * =======
 * kernel [3, 3]
 *  no stride, no dilation
 *  stride [2,2], no dilation
 *  stride [2,2], dilation [2,2]
 * kernel [1,1]
 *  no stride, no dilation
 *  stride [3,3], no dilation
 *  stride [3,3], dilation [2,2]
 * kernel [5,5]
 *  no stride, no dilation
 *  stride [2,2], no dilation
 *  stride [2,2], dilation [2,2]
 */
TEST_CASE("[cpu/operator] ConvDepthWise(forward)", "[ConvDepthWise][CPU]") {
    SECTION("ConvDepthWise kernel [3,3]") {
        SECTION("no stride, no dilation") {
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({3,3});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,1,4,5,5> {
                {{{{-0.1008466408, -1.5454049110, -0.9166140556, -0.6960951090,
                    0.4341324568},
                { 0.0774235576, -0.5880309343, -0.7458236814,  1.1315208673,
                -0.1549620479},
                { 0.0743396878,  2.1142954826, -0.1723070294, -0.1795921773,
                    0.5806804895},
                { 1.3079929352,  1.0224009752, -0.1107744649, -0.9886689186,
                    0.2421970069},
                {-0.5798103809,  0.3528926671,  0.0050931070, -0.7710842490,
                    0.3619758785}},

                {{ 0.0081272032,  0.9984526038,  0.0044101765, -1.6572961807,
                    2.0608859062},
                {-1.1862419844, -0.4931973815,  0.7710964084,  0.8817673326,
                    0.8965246081},
                { 1.8537108898, -0.0401010700, -0.4194879532,  0.3477281332,
                    0.6765057445},
                {-0.1150730550, -0.1088671982,  0.1020692363, -1.0760768652,
                    0.5623582602},
                {-0.8432090282, -1.9785683155, -1.0973212719, -0.4528564811,
                    0.5299630761}},

                {{-1.4599646330, -1.2320238352,  0.2687234879,  0.4537659883,
                -0.5159105062},
                {-0.1159662902, -0.0771213397,  0.1781232059,  0.4988347590,
                    1.6487812996},
                { 0.4867084324, -1.0319201946, -1.0943733454, -1.9665944576,
                    1.4405336380},
                {-0.0458223820,  1.9759161472,  1.0542000532, -0.8943792582,
                    0.0833332092},
                { 0.6894319654,  0.1574374884, -0.0074822172,  0.1254266798,
                -1.0130254030}},

                {{ 0.7434654236,  0.5090847015,  0.9238219261, -1.6246345043,
                -1.7051482201},
                {-0.2401624918,  0.1479524970,  0.1278737187,  0.3838838637,
                    0.5377982855},
                { 0.5112501979, -2.1439685822, -0.4956556559, -0.3001609743,
                -2.1275873184},
                {-2.0808370113,  0.0635774806, -0.2659386396, -0.0834266022,
                -1.3333587646},
                { 2.3835628033,  0.3105354607, -0.3466446698, -1.3037562370,
                -0.2132562548}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {{ 0.1543500125,  0.2259403467, -0.2096073627,  0.0794987679}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,1,3,3> {
                {{{{-0.0184964351,  0.0452591591, -0.0370532684},
                { 0.1729838848,  0.0589027032, -0.2055611610},
                { 0.0022869906, -0.0960286856,  0.1693624258}}},


                {{{ 0.3332056701, -0.3317362666,  0.2975485921},
                { 0.0031725965,  0.3205705583, -0.2838594615},
                { 0.3050023913, -0.2213795185,  0.2740720510}}},


                {{{-0.2431102246, -0.0334809646,  0.2784897089},
                {-0.2372554243, -0.0052002668,  0.2773198783},
                {-0.2523153722,  0.2629073262, -0.2890377939}}},


                {{{ 0.0322953090,  0.0774075240,  0.2625359297},
                { 0.0187648144, -0.2478408515,  0.2355882376},
                { 0.2588306367, -0.0054992437,  0.0906284302}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,1,4,3,3> {
                {{{{ 0.0202585980, -0.2200401127,  0.2083991319},
                { 0.2128068209,  0.3275838494,  0.2010547519},
                { 0.5299543738,  0.3573902845,  0.1360719502}},

                {{-0.0227194652,  0.2353070378,  1.4015233517},
                { 0.3528071046, -0.5158598423,  0.7987069488},
                { 0.5479428768,  0.3083744943, -0.4213459492}},

                {{ 0.2609346807,  0.9040609002, -0.6786935925},
                {-0.3164857328, -0.3156455159,  0.3337014914},
                {-0.4356064498, -1.2700247765,  0.6287755966}},

                {{ 0.4735998511, -0.7769540548, -0.7495914698},
                {-0.0220829751,  0.2172142118, -0.3708224297},
                { 0.2662829161, -0.0953377336, -0.9186285734}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));;
        }
        SECTION("stride [2,2] | no dilation") {
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({3,3}, {2,2});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,3,5,5> {
                {{{{-0.2681647539, -0.1401816458, -1.3439471722,  0.7686232328,
                    -0.2347259074},
                    {-0.9053395391, -0.1154953390,  0.2734031677,  0.6977564096,
                    -0.2161262333},
                    {-0.5152124763,  0.1068634093,  0.8755886555,  0.3106011450,
                    -0.2565480769},
                    {-0.1076742634, -1.2246952057, -1.3814687729, -0.5840652585,
                        0.4304563105},
                    {-0.8992680907,  0.4047995806,  1.4492179155, -1.4900603294,
                    -0.7605531812}},

                    {{-2.3480441570,  0.6959431767, -0.1289412379, -0.0847483501,
                        1.2968308926},
                    { 0.9658490419, -0.2208128721, -0.8502574563, -1.5217782259,
                        0.3917841315},
                    {-0.0676943064, -0.8175349236,  0.7544731498,  0.0412569866,
                        1.2587231398},
                    {-0.7003766298, -0.8936796188, -0.0226393063, -1.2184852362,
                        0.3866785169},
                    {-1.7608956099, -0.1914640963,  0.2436290830, -0.9511274099,
                        1.5242536068}},

                    {{-1.6968810558, -0.7415107489, -0.3093018830, -0.5676894784,
                        0.2574917674},
                    { 0.2789881527, -1.1715706587,  0.3031383455,  0.2484192103,
                        0.1954772770},
                    { 0.9325433969, -2.1942939758, -0.9328040481,  0.9583657384,
                        1.5130572319},
                    {-1.2234312296,  0.7099080086,  0.9838530421, -0.3570700288,
                    -1.9504652023},
                    {-0.1599121839,  1.4158203602, -0.6402221918, -1.1810790300,
                    -0.4780270755}}},


                    {{{ 0.2103048563, -1.6611757278,  0.4508873522,  0.8979755640,
                    -0.6757122874},
                    { 0.7067258954,  0.3836486340, -1.5270982981, -0.2568639815,
                    -0.1403182000},
                    { 0.3186497986, -0.0742176697,  0.2034454942, -1.4518156052,
                        0.5708613396},
                    {-0.9756730199, -0.1207360327,  0.5579432845,  0.2221798450,
                        0.7631093264},
                    {-0.6217514277,  0.0976419225, -0.3045219481,  1.8516135216,
                    -0.2196053267}},

                    {{ 1.2789859772, -0.6263879538, -0.2063939124, -0.2311875522,
                    -0.0393278264},
                    {-0.8454674482, -1.0055953264,  1.1767570972, -1.2289278507,
                        0.1877539605},
                    { 0.8406858444,  1.5269470215,  0.9868479967, -0.3241306841,
                        0.9222514033},
                    { 1.3278032541,  1.0738047361,  0.2232345939, -1.9111207724,
                        2.5548310280},
                    { 1.1046593189, -0.6609010696, -0.1762587577, -0.4457865655,
                        0.8877998590}},

                    {{-0.3908021450,  1.6496912241,  0.1737804860,  0.1961778849,
                    -0.4031102061},
                    {-0.8883095384,  1.4801807404, -0.6092755198, -0.5375859141,
                    -0.3113131523},
                    { 2.0676779747,  0.3772547543, -0.3045346141,  1.6700518131,
                    -1.8084005117},
                    { 1.4025870562,  0.8708391786,  1.3200664520, -1.1006357670,
                        0.3649013042},
                    {-0.5854687095, -1.2669901848, -2.2839319706, -0.1692169160,
                    -0.2855746150}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,3> {{0.0749854296, 0.2219027281, 0.1885577142}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,3,1,3,3> {
                {{{{ 0.3290626705,  0.3244876564, -0.3134182394},
                    { 0.2142836750,  0.2250442505,  0.1981022060},
                    {-0.1862878054,  0.2690648437,  0.0310798883}}},


                    {{{ 0.0116933584, -0.3271239698,  0.1202279776},
                    { 0.0051873922, -0.0847972259, -0.3323501348},
                    {-0.2489729375,  0.2729874253,  0.0016002655}}},


                    {{{-0.3015822172, -0.0256469250, -0.2859892547},
                    { 0.1704760790,  0.1709747761,  0.1115431413},
                    {-0.3066976070,  0.2390796840, -0.0861117467}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,2,3,2,2> {
                {{{{ 0.3485876918,  0.0410024002},
                    {-0.5851752758, -0.4924227297}},

                    {{ 0.0524865389,  0.2238895148},
                    { 1.0454657078,  0.0253930446}},

                    {{-0.0414484441,  0.7236352563},
                    { 0.6955899596, -0.1431636363}}},


                    {{{-0.6739091277, -0.0971068442},
                    { 0.0989151821,  0.3607567251}},

                    {{ 0.3158496320,  0.0055956692},
                    {-0.7632108927, -0.3119188249}},

                    {{-0.2696485221,  0.6642971039},
                    { 0.2509680390,  1.5169239044}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);

            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();

            conv_op.forward();

            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [2,2] | dilation [2,2]") {
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({3,3}, {2,2}, {2,2});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,1,4,7,7> {
                {{{{-1.2564380169e+00,  1.7001671791e+00, -6.0228500515e-02,
                -7.6883906126e-01,  1.0394480228e+00, -2.1278746128e+00,
                -4.3723234534e-01},
                { 1.1784723997e+00, -8.7596154213e-01, -5.6893348694e-02,
                -4.8710629344e-01,  1.6934870481e+00, -8.1963825226e-01,
                -9.9433082342e-01},
                { 1.6150641441e+00,  2.2550213337e-01,  1.3934186101e-01,
                -6.8104225397e-01, -2.2220163047e-01,  1.1362174898e-01,
                    2.0110601187e-01},
                { 6.7898714542e-01, -7.3953729868e-01, -3.0562976003e-01,
                -4.8523655534e-01,  4.3536397815e-01, -2.4560281634e-01,
                    6.4579688013e-02},
                { 1.3424048424e+00,  1.6474188864e-01,  7.4488747120e-01,
                -5.9620857239e-01, -9.5960032940e-01,  4.5834407210e-01,
                    4.9313405156e-01},
                { 1.4139299393e+00, -1.3618037701e+00,  3.3027759194e-01,
                    6.8120902777e-01,  1.9601145983e+00, -4.0156817436e-01,
                -7.7237486839e-02},
                { 1.3182686567e+00,  3.3211612701e-01,  1.9552657604e+00,
                    1.4429262877e+00,  1.2531367540e+00, -1.5773595572e+00,
                    1.0225969553e+00}},

                {{ 4.6619251370e-01,  6.9228565693e-01,  8.8645166159e-01,
                -8.3506953716e-01, -5.2961051464e-01,  1.0165786743e-01,
                    5.3774905205e-01},
                {-6.0989326239e-01,  2.3970830441e-01,  2.3374938965e-01,
                -4.9342277646e-01, -3.7169873714e-01,  3.4093639255e-01,
                    2.3664817214e-01},
                {-7.5131034851e-01, -4.2149517685e-02, -4.3322569132e-01,
                    2.1540248394e+00, -6.9199752808e-01,  4.7342130542e-01,
                    1.9464567900e+00},
                { 2.4289269932e-03, -2.2609269619e+00, -3.6993902922e-01,
                -2.6160044670e+00, -1.0806908607e+00, -9.2318016291e-01,
                    9.6653455496e-01},
                {-4.3112087250e-01,  9.3174472451e-02,  2.1137170494e-01,
                    3.6451536417e-01,  6.0560785234e-02, -1.4053032398e+00,
                    2.6295976639e+00},
                { 3.3558085561e-01,  2.0609512925e-01,  8.1405574083e-01,
                -1.1626043916e-01, -9.6128863096e-01, -1.0162148476e+00,
                    2.2983274460e+00},
                {-5.1882678270e-01,  9.7170782089e-01,  5.9890896082e-01,
                -1.2613058090e+00, -4.7689700127e-01,  3.2950636744e-01,
                    2.5496333838e-01}},

                {{ 1.2547644973e-01, -2.1516680717e+00, -4.3004885316e-01,
                -1.1163233519e+00, -7.9468077421e-01, -8.5132592916e-01,
                -6.7698568106e-01},
                {-2.3809320927e+00,  9.1189408302e-01, -7.9828941822e-01,
                -2.1867971420e+00, -2.0300696790e-01, -6.1769866943e-01,
                -5.6792998314e-01},
                { 4.5785719156e-01, -3.5315357149e-02, -5.2074277401e-01,
                    1.2201535702e+00, -1.7547093630e+00, -9.1879181564e-02,
                -9.0850913525e-01},
                {-1.0663042068e+00,  9.3642288446e-01,  1.0326064825e+00,
                    2.7203741670e-01,  1.5793048143e+00, -7.6377516985e-01,
                    6.5407752991e-01},
                {-6.6077453084e-03,  4.6359539032e-01, -7.1511381865e-01,
                -2.0252700150e-01, -1.2316555977e+00,  5.3828799725e-01,
                    2.9643586278e-01},
                {-2.1578962803e+00,  1.4375370741e+00,  1.4743455648e+00,
                -1.8298947811e-01, -1.7145735025e+00,  1.9807685614e+00,
                -7.3558908701e-01},
                {-8.6257940531e-01,  8.4425401688e-01, -1.0371112823e+00,
                -4.0326038003e-01, -7.3599940538e-01,  3.4502989054e-01,
                -1.2538944483e+00}},

                {{ 1.3018572330e+00,  3.4584665298e-01,  3.7988024950e-01,
                    2.6572030783e-01, -1.4982204139e-01,  7.3580324650e-01,
                -1.3376350701e-01},
                {-1.1923942566e+00, -9.8181134462e-01,  2.2573418915e-01,
                    8.0120041966e-03, -9.5310580730e-01,  9.6748578548e-01,
                    5.4245507717e-01},
                {-4.9568879604e-01, -1.4902360439e+00,  4.9879044294e-02,
                -1.5037181377e+00,  1.5938287973e-01,  8.2041674852e-01,
                -1.0953415632e+00},
                {-4.9750682712e-01,  4.4327926636e-01, -2.5331549346e-02,
                -9.7987723351e-01,  1.3228254318e+00,  5.7619941235e-01,
                -5.6825790554e-02},
                {-1.5635057688e+00,  6.7909795046e-01, -4.3309000134e-01,
                    1.8277673721e+00, -9.7239983082e-01, -1.0455882549e-01,
                    5.8249467611e-01},
                {-7.6343780756e-01, -3.5443061590e-01, -5.5096411705e-01,
                -1.1431121826e+00, -8.4882009029e-01,  5.1973551512e-01,
                    2.6110163331e-01},
                { 2.3929489776e-02,  7.2413641214e-01,  4.2561513186e-01,
                -6.4914476871e-01, -5.4483998567e-02,  3.4525018930e-01,
                -3.9139431715e-01}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {{-0.0600201301,  0.3112891614,  0.2262887955,  0.1428407431}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,1,3,3> {
                {{{{-0.0483490229,  0.1010556668, -0.3016797900},
                {-0.0918154344, -0.0209818687,  0.3124184012},
                { 0.2390201986,  0.2415951192,  0.1040253267}}},


                {{{-0.2466100901,  0.2284083068,  0.0995316133},
                { 0.0553057604,  0.1444625109,  0.1359003782},
                {-0.2869182825, -0.0471504554, -0.0665103197}}},


                {{{ 0.0298744049,  0.1652455777, -0.2147967815},
                {-0.2902197242,  0.3079298437, -0.2284899652},
                {-0.1819925010,  0.2709769011,  0.1631795168}}},


                {{{ 0.1494711637, -0.0957429856, -0.2600780129},
                { 0.0156168938,  0.0227777958, -0.0343352184},
                {-0.1857107133, -0.2571181655, -0.2455177009}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,1,4,2,2> {
                {{{{-0.1385704875,  0.2320426404},
                    { 0.4221621752,  0.8324003816}},
                    {{ 0.2576041818, -0.0725638047},
                    { 0.4960045815,  0.6652954817}},
                    {{ 0.0438128375, -0.1093067303},
                    { 0.3498060405, -0.3388394713}},
                    {{ 0.9684044123,  0.4782117605},
                    {-0.0788729265,  0.4020597339}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
    }
    SECTION("point-wise") {
        SECTION("no stride, no dilation") {
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({1,1});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,4,3,3> {
                {{{{-0.2665283084,  1.0591213703, -0.5744783282},
                    { 2.1924018860, -0.9184432626, -0.4519051015},
                    { 0.5954509974,  0.0728591084, -0.4014659226}},

                    {{-1.0344575644,  1.3931720257,  0.3318610489},
                    {-0.6563054919,  0.2039012611,  1.4156180620},
                    {-0.8701118827,  1.4934384823, -2.0206739902}},

                    {{-0.5417286754,  0.0236786865, -1.1417526007},
                    {-0.0592311621, -0.3561672270,  0.4465615153},
                    { 0.5427954793, -0.4105411768, -1.2697076797}},

                    {{ 0.5128195882, -0.9545230865,  0.3979352117},
                    { 0.8590119481,  0.2024669945, -0.0111086201},
                    { 1.5435370207, -0.5318347216,  0.1749227196}}},


                    {{{-0.6836048961, -1.2168579102, -0.8282459378},
                    {-0.2170266211, -0.3614979684, -0.5755043030},
                    { 0.0710424408, -0.6281879544, -0.8610697985}},

                    {{-0.3863270581,  0.6085444093, -0.0376757309},
                    { 1.3019801378, -0.6326317191, -0.1477297693},
                    { 0.6462736726, -1.0503417253,  0.5722824931}},

                    {{-0.0206014682,  0.2297244966,  0.7034130096},
                    { 1.4367071390,  0.1368671060,  0.6463897228},
                    {-2.0419392586, -2.2926816940,  1.9855005741}},

                    {{ 1.6628106833, -0.4438441694,  1.3320568800},
                    { 0.3473397493, -0.5916580558,  1.3845838308},
                    { 0.3625002801,  2.3297600746, -0.2896993756}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {{-0.7087996006,  0.5437290668, -0.0904183388, -0.1987243891}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,1,1,1> {
                {{{{ 0.8847143650}}},
                {{{-0.5319792032}}},
                {{{ 0.0759756565}}},
                {{{ 0.1301449537}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,2,4,3,3> {
                {{{{-0.9446009994,  0.2282202840, -1.2170488834},
                    { 1.2308498621, -1.5213595629, -1.1086065769},
                    {-0.1819955558, -0.6443400979, -1.0639822483}},

                    {{ 1.0940389633, -0.1974094808,  0.3671858907},
                    { 0.8928699493,  0.4352578223, -0.2093503028},
                    { 1.0066105127, -0.2507491410,  1.6186856031}},

                    {{-0.1315765232, -0.0886193365, -0.1771637350},
                    {-0.0949184671, -0.1174783781, -0.0564905331},
                    {-0.0491790958, -0.1216094717, -0.1868852079}},

                    {{-0.1319835037, -0.3229507506, -0.1469351351},
                    {-0.0869283155, -0.1723743379, -0.2001701146},
                    { 0.0021591650, -0.2679399848, -0.1759590805}}},


                    {{{-1.3135946989, -1.7853713036, -1.4415606260},
                    {-0.9008061886, -1.0286220312, -1.2179565430},
                    {-0.6459473372, -1.2645665407, -1.4706003666}},

                    {{ 0.7492470145,  0.2199960947,  0.5637717843},
                    {-0.1488972902,  0.8802759647,  0.6223182082},
                    { 0.1999249160,  1.1024889946,  0.2392866760}},

                    {{-0.0919835493, -0.0729648694, -0.0369760729},
                    { 0.0187364295, -0.0800197721, -0.0413084552},
                    {-0.2455560118, -0.2646063268,  0.0604313724}},

                    {{ 0.0176820308, -0.2564884722, -0.0253639072},
                    {-0.1535198689, -0.2757256925, -0.0185277909},
                    {-0.1515468061,  0.1044821292, -0.2364273071}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [3,3], no dilation") {
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({1,1}, {3,3});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,1,4,3,3> {
                {{{{ 1.6097626686,  0.6317374706,  0.8121796846},
                {-0.7499074936, -0.3534076512, -0.3863078654},
                { 0.2124902308, -1.2060434818, -0.2350673527}},

                {{ 1.6450011730,  0.7838846445,  0.5905120373},
                { 1.3153772354, -2.0690157413, -0.0058457609},
                {-1.5660933256,  0.0484106764,  1.1444953680}},

                {{-0.3757407069,  0.5180828571, -0.1972250640},
                { 0.6753169894,  0.1572864950,  1.7338060141},
                {-0.3412690759,  0.1255278289,  0.7706317306}},

                {{-0.4972190559, -0.7812244892,  0.6989694834},
                {-0.0817485675, -0.3598272502, -0.9195072055},
                {-0.9635654092,  1.1187410355,  1.2071155310}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {{ 0.3924750090,  0.6698757410,  0.3069384098, -0.5433753729}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,1,1,1> {
                {
                    {{{-0.7436860800}}},
                    {{{-0.0358216763}}},
                    {{{ 0.0749748945}}},
                    {{{-0.8600753546}}}
                }
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,1,4,1,1> {
                {
                    {{{-0.8046830893}},
                    {{ 0.6109490395}},
                    {{ 0.2787672877}},
                    {{-0.1157295182}}}
                }
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [3,3], dilation [2,2]") { // same as 'no dilation' test
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({1,1}, {3,3}, {2,2});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,1,4,3,3> {
                {{{{ 1.6097626686,  0.6317374706,  0.8121796846},
                {-0.7499074936, -0.3534076512, -0.3863078654},
                { 0.2124902308, -1.2060434818, -0.2350673527}},

                {{ 1.6450011730,  0.7838846445,  0.5905120373},
                { 1.3153772354, -2.0690157413, -0.0058457609},
                {-1.5660933256,  0.0484106764,  1.1444953680}},

                {{-0.3757407069,  0.5180828571, -0.1972250640},
                { 0.6753169894,  0.1572864950,  1.7338060141},
                {-0.3412690759,  0.1255278289,  0.7706317306}},

                {{-0.4972190559, -0.7812244892,  0.6989694834},
                {-0.0817485675, -0.3598272502, -0.9195072055},
                {-0.9635654092,  1.1187410355,  1.2071155310}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {{ 0.3924750090,  0.6698757410,  0.3069384098, -0.5433753729}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,1,1,1> {
                {
                    {{{-0.7436860800}}},
                    {{{-0.0358216763}}},
                    {{{ 0.0749748945}}},
                    {{{-0.8600753546}}}
                }
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,1,4,1,1> {
                {
                    {{{-0.8046830893}},
                    {{ 0.6109490395}},
                    {{ 0.2787672877}},
                    {{-0.1157295182}}}
                }
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
    }
    SECTION("kernel size [5,5]") {
        SECTION("no stride, no dilation") {
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({5,5});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,4,7,7> {
                {{{{ 4.9335759878e-01,  1.1863079071e+00,  2.5131928921e-01,
                        1.6316433251e-01,  2.5724807382e-01,  1.7706827819e-01,
                    -9.5957201719e-01},
                    {-1.0026545525e+00, -8.3003485203e-01, -9.3903255463e-01,
                    -4.6593669057e-01, -7.7092325687e-01, -1.7429690361e+00,
                        4.9265477061e-01},
                    {-1.3393815756e+00, -1.5150873661e+00, -5.8329886198e-01,
                        1.7911167145e+00, -1.0506145954e+00,  6.3952505589e-01,
                        1.0916038752e+00},
                    { 7.4818961322e-02,  1.5963518620e+00,  1.2598557770e-01,
                        6.4491826296e-01, -3.0015480518e-01, -4.8072105646e-01,
                    -6.9521999359e-01},
                    {-1.1644884348e+00,  3.7635925412e-01, -1.3692667484e+00,
                    -7.2627186775e-02,  5.5572849512e-01,  6.1445808411e-01,
                    -1.3938227892e+00},
                    {-1.4636498690e+00, -9.7709447145e-01, -1.8374252319e-01,
                        5.8343982697e-01,  1.1417788267e+00, -4.4667036273e-03,
                        4.9295508862e-01},
                    {-1.7479445040e-01, -1.0437289476e+00, -1.3344774246e+00,
                    -1.9067826271e+00,  1.0409342051e+00,  6.6524130106e-01,
                    -1.6800432205e+00}},

                    {{ 9.7197180986e-01, -1.1580578089e+00,  1.5227444172e+00,
                    -2.1772108078e+00,  4.8213523626e-01, -3.4500488639e-01,
                    -6.7194223404e-01},
                    { 4.9432659149e-01, -1.0472580194e+00, -7.3332744837e-01,
                        1.0557442904e+00, -9.4611018896e-01,  4.7074545175e-02,
                        2.5027732849e+00},
                    {-2.0324008167e-01,  6.9782984257e-01, -5.9244088829e-02,
                        3.4188255668e-02,  6.4118854702e-02, -1.1833695322e-01,
                        1.7038782835e+00},
                    {-6.1576306820e-01,  1.1467368603e+00,  9.0839028358e-02,
                        1.4732836485e+00, -3.2838854194e-01,  1.1726535559e+00,
                        9.6947526932e-01},
                    {-3.2122168690e-02,  1.3275359869e+00, -1.3638773561e-01,
                        2.1276748180e-01,  8.0851209164e-01, -9.8784273863e-01,
                        1.3375968933e+00},
                    {-5.4332029819e-01, -6.0529774427e-01, -6.0545504093e-01,
                        1.2644755840e+00, -8.6449778080e-01, -6.2184357643e-01,
                    -6.7940688133e-01},
                    {-5.1089364290e-01, -1.1370127201e+00, -1.9654258490e+00,
                    -2.0984578133e+00, -5.3804492950e-01, -2.2316617966e+00,
                        1.1619290113e+00}},

                    {{ 4.6988701820e-01, -5.1307964325e-01, -7.0698708296e-01,
                        5.7957285643e-01,  5.6874805689e-01, -1.9858320951e+00,
                    -1.7708021402e+00},
                    { 1.4547123909e+00,  3.7047669291e-01, -8.2360202074e-01,
                        1.9833043814e+00,  1.5422163904e-01, -6.8875616789e-01,
                        1.9319385290e+00},
                    { 9.7966343164e-02, -1.3681530952e+00,  6.8940818310e-01,
                    -1.0752324760e-01, -6.1970126629e-01,  1.8546850979e-01,
                    -4.5794528723e-01},
                    { 7.3246699572e-01,  4.9492576718e-01, -4.0711274743e-01,
                    -1.9404098857e-03, -1.5990917683e+00, -4.1567105055e-01,
                        1.6714956760e+00},
                    { 6.3360172510e-01, -1.1427930593e+00, -1.6082632542e-01,
                    -9.5651131868e-01, -1.1128952503e+00, -2.5961843133e-01,
                    -5.4337906837e-01},
                    { 3.8337892294e-01,  7.0294111967e-01, -2.2530713081e+00,
                        4.6874850988e-01, -3.7142974138e-01, -8.2423162460e-01,
                        3.1144621968e-01},
                    { 4.9787372351e-01, -1.0927795172e+00, -1.7619090080e+00,
                        4.4252237678e-01, -7.1531772614e-01,  2.7259647846e-01,
                    -4.3468984962e-01}},

                    {{-4.8326885700e-01, -3.9079174399e-01, -1.0790492296e+00,
                    -2.4060493708e-01, -3.3320981264e-01,  2.9022085667e-01,
                        1.3364650011e+00},
                    { 2.8901857138e-01, -3.9582711458e-01, -4.5644235611e-01,
                    -7.7036660910e-01,  8.4683763981e-01, -4.4504839182e-01,
                        3.8618934155e-01},
                    {-1.9830763340e+00, -8.0258053541e-01, -1.6078829765e+00,
                        2.1368785203e-01,  7.4899446964e-01, -5.6091493368e-01,
                        2.4929441512e-02},
                    {-5.3176864982e-02,  1.1627144814e+00, -5.2606719732e-01,
                        3.7888059020e-01,  7.5841093063e-01,  1.5893269777e+00,
                    -1.1967051029e+00},
                    { 1.7698400021e+00,  1.6875107288e+00, -1.2041009665e+00,
                        1.0938201100e-01, -1.3895796537e+00,  2.6665708423e-01,
                    -1.0111159086e+00},
                    {-5.7373844087e-02, -1.2338018417e+00, -2.9199585319e-01,
                    -2.0545010269e-01,  2.3327396810e-01, -8.3556395769e-01,
                    -2.0631006360e-01},
                    {-1.0011457205e+00,  6.4688628912e-01, -5.2283269167e-01,
                    -8.6595642567e-01,  4.4378590584e-01, -2.9411891475e-02,
                        2.0832476020e-01}}},


                    {{{-2.8625172377e-01, -4.6468538046e-01,  1.3221564293e+00,
                        2.1612360477e+00, -8.7142616510e-01,  1.9371863604e+00,
                    -1.2806377411e+00},
                    {-9.1990309954e-01, -2.0963511467e+00,  6.4161384106e-01,
                    -6.4031910896e-01, -6.1191931367e-02, -1.5462826490e+00,
                        7.9379910231e-01},
                    {-5.4952275753e-01,  5.6310713291e-01,  4.3853071332e-01,
                    -1.2469210625e+00, -1.1873561144e+00, -6.0527914762e-01,
                        5.4670602083e-01},
                    {-2.9861965775e-01,  2.5091698766e-01, -5.2039808035e-01,
                    -5.5684101582e-01, -6.3102495670e-01,  4.6421602368e-01,
                        1.5310447216e+00},
                    { 8.8189703226e-01, -1.7110499144e+00, -4.5705246925e-01,
                        4.8052483797e-01, -1.6791898012e+00, -1.8851631880e+00,
                        6.8464434147e-01},
                    {-2.1336026192e+00,  1.0871043205e+00,  4.0943421423e-02,
                        1.3751971722e-01, -1.1933552027e+00, -4.8217883706e-01,
                        1.6583485603e+00},
                    {-2.7782380581e-01, -1.1283507943e-01, -4.0001991391e-01,
                    -2.4640804529e-01, -1.5624488890e-01, -2.5247385502e+00,
                    -4.2229643464e-01}},

                    {{-1.0902142525e+00,  7.1061784029e-01,  5.2563959360e-01,
                        8.9594686031e-01,  3.3749544621e-01,  1.5508149862e+00,
                        3.1452372670e-01},
                    { 8.8045895100e-01, -1.0802421570e+00,  6.9660454988e-01,
                        7.6869897544e-02, -6.5158027411e-01, -6.1672717333e-01,
                    -1.2306349277e+00},
                    { 8.2995426655e-01, -3.1962795258e+00,  1.1294349432e+00,
                    -8.1900782883e-02, -1.1951421499e+00, -8.0654764175e-01,
                    -1.0077600479e+00},
                    {-8.5095930099e-01, -1.5733307600e-01,  1.7131972313e-01,
                    -2.5118584633e+00, -2.5612711906e+00, -1.2504032254e-01,
                        9.9039399624e-01},
                    { 1.6889984906e-01, -1.2384599447e+00, -3.2178740948e-02,
                    -6.5206307173e-01,  3.1484216452e-01, -1.3365371525e-01,
                    -4.7578561306e-01},
                    { 3.0374403000e+00,  6.3499100506e-02, -5.4018121958e-01,
                        3.0082745552e+00, -1.2163658142e+00, -3.3809682727e-01,
                    -7.0253151655e-01},
                    {-1.8768366575e+00, -1.3563711643e+00, -4.9710619450e-01,
                        1.0979669094e+00,  2.4758360386e+00, -7.8894788027e-01,
                    -4.7926986217e-01}},

                    {{-1.4381635189e+00,  1.4026446342e+00, -3.1678429246e-01,
                        7.0693075657e-01,  6.9888514280e-01,  5.8169060946e-01,
                        1.0585654974e+00},
                    { 3.5511282086e-01, -1.2119283527e-01,  6.5024077892e-01,
                    -6.7714643478e-01, -6.6425532103e-01, -2.4314621091e-01,
                        2.0232704282e-01},
                    {-7.3568201065e-01, -6.9552195072e-01, -9.7805447876e-02,
                    -3.5810253024e-01, -4.0582028031e-01, -7.9612672329e-02,
                    -9.9031373858e-02},
                    { 1.1441185474e+00,  3.6610561609e-01,  1.7374299467e-01,
                    -5.1957684755e-01, -7.6053464413e-01,  2.9512745142e-01,
                        2.1705639362e-01},
                    { 7.9289871454e-01, -1.2500211596e-01,  2.9821291566e-01,
                    -3.8476973772e-01,  1.1767704487e+00, -7.4524241686e-01,
                        3.7969255447e-01},
                    { 7.7420151234e-01,  1.6062602997e+00,  7.1063655615e-01,
                        1.2802153826e+00, -2.7737879753e-01,  7.0112681389e-01,
                    -1.1772309542e+00},
                    { 2.1208928525e-01, -1.2882195711e+00,  3.4972077608e-01,
                    -8.2289344072e-01, -1.4296934009e-01, -1.3751131296e+00,
                        7.3476749659e-01}},

                    {{-1.5929245949e-01,  6.7564934492e-01,  6.2561661005e-01,
                        1.3304146528e+00,  7.0209020376e-01,  1.0694117844e-01,
                    -1.4454191923e+00},
                    {-5.5573159456e-01, -2.2276285291e-01,  5.6988465786e-01,
                        9.7244775295e-01, -4.3723756075e-01,  7.7416992188e-01,
                        8.2548350096e-02},
                    { 2.7222864628e+00,  8.0904394388e-01, -4.6484589577e-01,
                    -9.2518895864e-01,  1.0536781549e+00, -4.6422225237e-01,
                        6.7778164148e-01},
                    { 7.3793217540e-02,  7.9145863652e-02, -1.2498629093e+00,
                    -1.2253532559e-01,  1.5088248253e-01,  1.1032183170e+00,
                    -5.4131639004e-01},
                    { 2.1829447746e+00, -1.5288269520e+00, -8.8960021734e-01,
                        8.2131899893e-02, -1.8998783827e-01, -1.8512618542e-01,
                    -3.4453171492e-01},
                    { 3.8420417905e-01, -1.0917562991e-01, -6.3616442680e-01,
                        1.1726476997e-01,  1.0503277779e+00, -6.6429930925e-01,
                        9.0642881393e-01},
                    {-1.1341298819e+00, -5.9495466948e-01, -3.9783969522e-01,
                    -7.2580540180e-01,  6.6621124744e-02, -4.1143927723e-02,
                    -2.2756290436e+00}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {
                { 0.0059141875, -0.0648065358, -0.0974549279, -0.0668982267}
            });
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,1,5,5> {
                {{{{ 0.0740372464, -0.1711187363,  0.0966774225,  0.1457725614,
                        0.0493013635},
                    { 0.0289726499,  0.1567692012,  0.0252312180, -0.0124352938,
                        0.1629220545},
                    {-0.1641854495, -0.0290440563,  0.0452437662, -0.1157503128,
                        0.0947008878},
                    {-0.0830174908, -0.0582915321, -0.0306369308,  0.1680577993,
                        0.0682382584},
                    { 0.1222482920, -0.1069305465, -0.0742284581, -0.0171356685,
                    -0.0342458002}}},


                    {{{ 0.1335894614, -0.1472281665,  0.0556286834, -0.1737921238,
                        0.1377953589},
                    {-0.1001852304,  0.1950409263,  0.0267154705, -0.1778282225,
                        0.0544498451},
                    {-0.1686174124, -0.0963439271,  0.0142339235,  0.1019947082,
                        0.1663077176},
                    {-0.0054439544, -0.0924759433,  0.0550796054,  0.0225215200,
                        0.0133644817},
                    {-0.0317959562, -0.1560210288, -0.0677365586,  0.1075119525,
                        0.0507474206}}},


                    {{{ 0.1699814051, -0.1997351646,  0.0536758676,  0.0581439026,
                        0.0378645435},
                    { 0.1802627891,  0.1987431794,  0.0738811046, -0.0955903530,
                    -0.1651753038},
                    { 0.1358847916,  0.1417508423, -0.0334093831,  0.1283134073,
                    -0.1813320667},
                    { 0.0582411997, -0.0489385389, -0.0596107244,  0.1527736634,
                        0.0661540776},
                    { 0.0447563417, -0.1846967936,  0.0630980507,  0.1160114333,
                    -0.0923686028}}},


                    {{{-0.0659249350,  0.0793606564, -0.0854479820, -0.1152713820,
                        0.1727648824},
                    { 0.1545656770, -0.0538719185, -0.0680776834, -0.1562429518,
                    -0.0725132227},
                    { 0.1585823596,  0.0286217686, -0.0436206348,  0.0533598661,
                    -0.1532071382},
                    {-0.0669565201,  0.0751482025,  0.0940384641,  0.1469179243,
                    -0.0734879524},
                    { 0.0089315651, -0.1766889840, -0.0285181757,  0.1730310023,
                        0.1393895000}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,2,4,3,3> {
                {{{{-0.5857352614,  0.1086293235, -0.3229822814},
                    {-0.7310093045, -0.3348580301, -0.0214850176},
                    { 1.2433251143,  0.4028868377, -0.2706802487}},

                    {{ 0.0309218448, -0.5836250782,  0.8768596649},
                    { 0.1736581773, -0.0594436042,  0.0133085661},
                    {-0.0390867218,  0.2015268654,  0.4916410446}},

                    {{ 0.2147885114, -0.5134234428, -0.0374717414},
                    { 0.2516125441,  0.3315618634, -1.1428399086},
                    { 1.0725688934, -0.3064109087, -0.8532077670}},

                    {{-0.5997072458, -0.2242568582,  0.2218082845},
                    { 0.1030889302, -0.8108748794, -0.1896360815},
                    { 0.3333139122,  0.0513630882, -0.1338601708}}},


                    {{{ 0.5120676160, -0.5367197394,  0.2702569067},
                    {-0.1443247199, -0.4722655416, -0.1593769193},
                    {-0.6662371159, -0.2179034948,  0.5732577443}},

                    {{-0.6737872958,  0.5619254708, -0.6692476869},
                    {-0.8073410392, -0.1703055203, -0.1989304423},
                    { 1.4196149111,  0.5330380797, -1.2780163288}},

                    {{-0.6440194249,  0.3993457556, -0.1229868680},
                    { 0.0640285239, -0.4547872841, -0.1695011109},
                    { 0.4479903877,  0.0911103636, -0.5269957185}},

                    {{ 0.1002222970, -0.1615311801, -0.4674149454},
                    { 0.1204236448,  0.2234887183, -0.2007223815},
                    { 0.4577222168, -0.2835268676, -0.6621670723}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [2,2], no dilation") {
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({5,5}, {2,2});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,4,7,7> {
                {{{{ 3.8520759344e-01, -1.5048544109e-01, -2.1790374815e-01,
                    -5.7652354240e-01,  1.1100100279e+00, -2.4167035520e-01,
                        1.0990920067e+00},
                    { 8.7922102213e-01,  1.8489408493e+00, -2.0763272047e-01,
                        1.6101540625e-01,  1.4712316990e+00,  9.0872722864e-01,
                        5.9666723013e-01},
                    { 2.3513903618e+00,  3.5370627046e-01,  4.6655362844e-01,
                        3.6453476548e-01, -1.3369771242e+00, -3.9518725872e-01,
                        1.4669662714e+00},
                    { 1.2112803459e+00,  3.3809375763e-01, -7.8605490923e-01,
                        1.2191309929e+00,  1.8707159758e+00, -1.5493103862e-01,
                    -4.8493990302e-01},
                    {-8.7178820372e-01, -9.3710070848e-01, -1.1901003122e+00,
                    -3.0594833195e-02,  9.9669891596e-01,  1.9136610031e+00,
                    -1.4605854750e+00},
                    {-8.1902602687e-03,  3.5515683889e-01, -1.2319850922e+00,
                        1.3729830980e+00, -2.1615109444e+00,  1.0895189047e+00,
                    -3.2240214944e-01},
                    { 7.7308756113e-01,  1.0597428083e+00,  8.5827887058e-01,
                    -7.9133516550e-01, -1.9188613892e+00,  1.2579337358e+00,
                    -1.2377259731e+00}},

                    {{-6.7472612858e-01, -3.4404060245e-01,  1.2304736376e+00,
                        3.4865313768e-01, -5.6978863478e-01,  3.5225439817e-02,
                        1.0718777180e+00},
                    { 1.2797117233e+00,  4.2904919386e-01, -8.7542390823e-01,
                    -1.3666222095e+00, -5.1030803472e-02, -1.2733145058e-01,
                        1.1057097465e-01},
                    {-7.2918736935e-01,  1.1353262663e+00,  4.0105110407e-01,
                        3.2652910799e-02, -7.9244107008e-01,  5.7068455219e-01,
                        2.5546798110e-01},
                    {-9.1081774235e-01, -1.6454605758e-01,  3.1387217045e+00,
                        5.1187878847e-01,  7.3688519001e-01, -6.5781360865e-01,
                    -3.6698520184e-01},
                    {-3.7950250506e-01,  9.2945128679e-01, -6.6260379553e-01,
                        4.8890876770e-01,  1.2294455767e+00,  1.9234917164e+00,
                        8.0498385429e-01},
                    {-5.4067701101e-01, -2.9110896587e-01, -5.1824927330e-01,
                    -1.1617597938e-01,  1.3733102083e+00,  6.9681173563e-01,
                        6.2707096338e-01},
                    { 5.7201117277e-01,  1.5840615332e-01,  2.0593723655e-01,
                    -8.1141608953e-01,  8.5261273384e-01, -1.6056976318e+00,
                    -5.2579122782e-01}},

                    {{ 1.6815655231e+00,  2.4704076350e-01, -1.1197048426e+00,
                    -4.7988933325e-01,  1.4443746209e-01,  6.8120814860e-02,
                    -6.0399234295e-01},
                    { 2.6893126965e-01,  1.1254637241e+00,  1.2356828451e+00,
                        7.0232260227e-01, -1.7059510946e+00,  1.7088449001e-01,
                        3.2452866435e-01},
                    { 1.7605692148e+00,  1.0952962637e+00,  2.0282413960e+00,
                        7.0874804258e-01,  6.6728973389e-01,  5.7557627559e-02,
                    -2.2893531248e-02},
                    { 7.6859766245e-01, -1.5146261454e+00, -1.3063246012e-01,
                        4.9653983116e-01, -6.1272948980e-01,  6.1812108755e-01,
                        2.9842779040e-01},
                    {-7.9819858074e-01, -1.5216785669e+00,  1.5476153791e-01,
                    -3.0010658503e-01, -8.9212977886e-01,  1.3135321140e+00,
                        4.5638892055e-01},
                    {-4.5912411064e-02,  3.1731166840e+00, -1.1454867572e-01,
                        3.9372527599e-01, -3.0301496387e-01, -9.5864409208e-01,
                        6.4990806580e-01},
                    {-1.5128309838e-02, -1.8659442663e+00, -1.0648315400e-01,
                    -2.6408338547e+00, -1.2393210828e-01,  1.3168338537e+00,
                    -6.2912321091e-01}},

                    {{-8.6029833183e-03, -2.5194084644e-01,  3.4589302540e-01,
                        1.0429813862e+00,  1.6685616970e+00, -7.2287905216e-01,
                    -8.8275146484e-01},
                    {-1.1796358824e+00,  7.4369359016e-01,  1.1751577854e+00,
                    -8.0447465181e-01, -3.3473432064e-01, -4.7937799245e-02,
                    -1.8288640976e+00},
                    { 5.6663048267e-01, -5.3825604916e-01,  1.8124829531e+00,
                        8.6427420378e-02, -1.5517222881e+00,  2.1185632050e-01,
                        9.4422245026e-01},
                    {-1.9948658943e+00,  1.2489651442e+00,  1.2926094532e+00,
                        6.2967604399e-01,  1.0601581335e+00, -1.4124554396e+00,
                    -1.0759254694e+00},
                    { 1.7446736097e+00,  8.5903286934e-01,  1.2479382753e-01,
                        1.0916360617e+00,  3.5449057817e-01, -1.9337905645e+00,
                        8.8214844465e-02},
                    { 2.2092112899e-01,  1.0851465464e+00, -8.5641130805e-02,
                    -6.1427676678e-01, -1.0848737955e+00,  1.7291833460e-01,
                    -9.3117421865e-01},
                    { 4.9415281415e-01, -7.1096634865e-01, -1.2350386381e+00,
                        4.8438447714e-01, -8.0378723145e-01, -2.3194132373e-02,
                    -8.8567745686e-01}}},


                    {{{ 9.3833208084e-01, -9.3867695332e-01, -1.9296536446e+00,
                    -1.9469395280e-01, -1.0064001083e+00,  6.7425054312e-01,
                        5.7381069660e-01},
                    {-5.8385449648e-01,  1.5392524004e+00, -7.3658037186e-01,
                    -4.9099606276e-01, -5.8427224867e-03, -4.9734053016e-01,
                        2.0966405869e+00},
                    { 4.4381022453e-02,  1.9009371996e+00, -3.1770554185e-01,
                    -4.5139196515e-01,  2.2562942505e+00,  1.0809175968e+00,
                    -7.8067958355e-01},
                    { 1.2378455400e+00, -1.2802067995e+00, -1.3410314322e+00,
                    -1.7746871710e-01, -9.6855717897e-01, -6.6797292233e-01,
                    -2.7914598584e-01},
                    {-2.2995612621e+00, -4.6167243272e-02, -1.1212759018e+00,
                    -8.9812129736e-01, -5.6873339415e-01, -6.2530684471e-01,
                    -1.1113914251e+00},
                    { 9.4398176670e-01,  2.9730677605e-01,  4.0788778663e-01,
                    -6.1924046278e-01,  3.7405481935e-01, -7.1785598993e-01,
                        5.2785265446e-01},
                    { 1.4844427109e+00,  1.5798323154e+00,  7.9189050198e-01,
                        2.1535563469e+00, -1.3852857351e+00,  1.6917630434e+00,
                        2.3598566055e+00}},

                    {{-7.7599078417e-01,  4.9129545689e-01, -6.9907718897e-01,
                        5.8299517632e-01, -8.1232386827e-01, -6.8906480074e-01,
                        6.0145241022e-01},
                    { 6.5379202366e-01, -2.5990147591e+00,  5.7937479019e-01,
                    -2.4067652225e+00, -5.0686568022e-02, -6.1713993549e-01,
                        1.3297734261e+00},
                    {-5.2623099089e-01,  5.4217547178e-01, -1.7074975967e+00,
                    -1.3474613428e-02, -4.3210104108e-01, -1.3601350784e+00,
                        1.1019977331e+00},
                    {-3.2511076331e-01,  7.4853056669e-01,  8.6941182613e-01,
                        2.6319536567e-01, -2.1560071036e-03, -6.2360137701e-01,
                        6.6978454590e-01},
                    {-4.7999924421e-01,  1.5140286684e+00,  5.0155067444e-01,
                        2.3926508427e-01, -1.4036533423e-02,  1.0717345476e+00,
                        4.9181202054e-01},
                    {-1.1894277334e+00, -4.2690724134e-01, -1.0564312935e+00,
                        1.3100820780e-01, -1.5495970249e+00,  3.2739278674e-01,
                        2.1079761982e+00},
                    { 8.7008196115e-01, -4.4802580029e-02, -3.0779972672e-01,
                        9.4748604298e-01,  5.0919568539e-01, -5.6785094738e-01,
                        1.8958197534e-01}},

                    {{ 7.7712529898e-01, -5.4674464464e-01, -4.3654015660e-01,
                    -1.7690027133e-02, -1.0759859085e+00,  7.9439246655e-01,
                        4.0082043409e-01},
                    {-1.4334075451e+00, -1.1236054450e-01,  4.2285147309e-01,
                    -4.3151515722e-01,  1.8296922743e-01, -7.5662362576e-01,
                        1.2256839275e+00},
                    {-1.2095364332e+00,  2.7471596003e-01, -7.1152734756e-01,
                        8.0925470591e-01, -1.0531398058e+00,  1.3366776705e+00,
                    -1.4349820614e+00},
                    {-6.5796740353e-02, -1.6194750369e-01,  1.7100380361e-01,
                    -3.0003476143e-01, -1.7518389225e+00,  1.0243947059e-01,
                        1.8502172232e+00},
                    { 1.8798203468e+00, -5.6445449591e-02,  2.2965614498e-01,
                    -5.0052756071e-01, -3.1924626827e+00, -2.3384423554e-01,
                    -1.8923732042e+00},
                    {-8.3112102747e-01,  1.0703138113e+00, -1.0538098812e+00,
                    -5.6717932224e-01,  6.6653525829e-01,  1.0731325299e-01,
                    -1.4036635160e+00},
                    { 6.6975963116e-01, -1.0376057625e+00,  1.4635567665e+00,
                        6.0049772263e-01,  2.6335725188e-01,  1.7023352385e+00,
                        3.9096495509e-01}},

                    {{-5.2209907770e-01, -7.3401969671e-01, -4.9598002434e-01,
                    -1.1101231575e+00,  2.7940380573e-01,  3.7108734250e-01,
                    -5.3757792711e-01},
                    {-1.7226947546e+00, -8.4843122959e-01,  3.0122289062e-01,
                    -8.2913970947e-01,  1.7452031374e+00,  7.5711089373e-01,
                        3.6754548550e-01},
                    {-3.5317954421e-01, -2.7988141403e-02,  8.1158816814e-01,
                        1.1813087463e+00,  1.7010580301e+00,  3.6692687869e-01,
                        7.9475116730e-01},
                    { 7.1114557981e-01, -4.3524390459e-01, -1.2870337963e+00,
                    -3.3187520504e-01,  4.9446484447e-01, -5.0515407324e-01,
                    -7.1395359933e-02},
                    { 1.3766362667e+00, -3.5036647320e-01, -2.2399795055e+00,
                    -1.8095448017e+00,  2.2540309429e+00,  1.6960443258e+00,
                    -6.1720281839e-01},
                    {-2.4066153169e-01, -3.4883093834e-01, -1.1646213382e-01,
                    -5.0506269932e-01,  1.1594126225e+00, -3.2168120146e-01,
                    -1.5061002970e+00},
                    { 1.0476481915e+00,  1.9588752985e+00,  3.6338061094e-01,
                        2.0577123165e+00, -1.0460792780e+00, -3.1160068512e+00,
                    -1.7618523836e+00}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {
                {-0.0200376045, -0.0020939112, -0.1464084685, -0.0419981480}
            });
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,1,5,5> {
                {{{{-0.0712780729, -0.1232913956,  0.1876820773, -0.0419011600,
                    -0.1424695104},
                    { 0.0584071651, -0.1193018928,  0.1307084858,  0.1676846296,
                    -0.0871880054},
                    {-0.0948264599,  0.0315888636, -0.1759090424, -0.0207793470,
                        0.1858711988},
                    {-0.1399959624, -0.0186878927, -0.0617008917,  0.0002223492,
                        0.1727847904},
                    {-0.0758293867,  0.0122927902, -0.0473791622, -0.1468438208,
                    -0.0557731166}}},

                    {{{-0.0829651132,  0.0315707214,  0.0823683068, -0.1360922605,
                    -0.0236635935},
                    { 0.1936927885, -0.0261211153, -0.1094252840, -0.1744011492,
                        0.0887691975},
                    { 0.0449672453,  0.0418232940,  0.0158894062,  0.1032823324,
                        0.1539564580},
                    {-0.1828922033, -0.0399240516,  0.0496491455,  0.1540371031,
                        0.1999383718},
                    {-0.0052583455,  0.1408428699,  0.0648615360, -0.1485663503,
                    -0.0073370696}}},

                    {{{-0.0690000057,  0.0339213610, -0.1460432559, -0.1896216869,
                    -0.0705224052},
                    {-0.1869531870, -0.1661461592, -0.0214934107,  0.1090264320,
                        0.0115755796},
                    {-0.1388747245, -0.0404886007,  0.0298435446, -0.1655783951,
                    -0.1112038419},
                    {-0.1958016008,  0.0370284095,  0.0006295681,  0.1554906219,
                    -0.1377223581},
                    { 0.0179722793,  0.0870472714, -0.0224549528, -0.0847019181,
                    -0.1470272839}}},

                    {{{-0.1011667028,  0.1455960721, -0.0806858540,  0.1572577953,
                    -0.0550846569},
                    { 0.0752714872,  0.0279489048,  0.1414941847,  0.0247094631,
                    -0.0008786917},
                    {-0.0451189540, -0.0664234906,  0.1269026548, -0.1047828719,
                        0.0019106150},
                    {-0.1722858250, -0.1355526745, -0.0965286270,  0.1626135856,
                    -0.0860176086},
                    {-0.0193410646, -0.1041662693, -0.0561585911,  0.1893668473,
                    -0.1290524751}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,2,4,2,2> {
                {{{{-0.7954307795,  0.6042141914},
                    { 0.2831384242, -0.7216922641}},
                    {{ 1.1425485611, -1.0336279869},
                    { 0.3416210711,  1.3282159567}},
                    {{-0.6753546596, -0.7460098267},
                    { 0.0863537639, -1.0971375704}},
                    {{ 0.3648000360, -1.4717553854},
                    {-0.2303827107,  0.7337518334}}},

                    {{{ 0.1193560362, -0.5002098680},
                    {-0.6565298438, -0.0648934469}},
                    {{ 0.7361341119,  0.1737762839},
                    {-0.5042620897,  1.6453049183}},
                    {{ 1.0128767490, -0.1996757686},
                    {-0.0394040421,  0.4287980795}},
                    {{-0.9122012258,  0.8897001147},
                    {-0.0642478392, -0.2671076953}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [2,2], dilation [2,2]") {
            ConvDepthWise_Op<2> conv_op = ConvDepthWise_Op<2>({5,5}, {2,2}, {2,2});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,1,4,11,11> {
                {{{{-7.9724937677e-01,  9.1211748123e-01,  2.4363055825e-01,
                        2.4279198647e+00, -1.1906795502e+00, -1.2431346178e+00,
                        7.9783517122e-01,  3.3326399326e-01, -8.8273769617e-01,
                        6.3607555628e-01,  1.6719125509e+00},
                    {-8.0337154865e-01,  4.3899053335e-01,  2.2643094063e+00,
                    -3.3888676763e-01, -2.1396698952e+00, -2.4637742341e-01,
                        5.8492517471e-01,  1.1872248352e-01, -4.6652513742e-01,
                        1.1809904873e-01,  1.1721512079e+00},
                    { 1.5571426153e+00, -6.7109817266e-01,  7.4499303102e-01,
                        1.0628908873e+00, -2.0967121422e-01, -9.4654607773e-01,
                        4.4076669216e-01,  7.2388553619e-01,  4.3450137973e-01,
                    -4.0051737428e-01, -1.0369918346e+00},
                    { 1.1229771376e+00,  7.1691840887e-02,  2.1389411390e-01,
                        5.7155287266e-01,  6.8389695883e-01,  5.4999643564e-01,
                        9.9025702477e-01, -1.6951800883e-01, -7.1715158224e-01,
                        1.1616971493e+00,  1.4596436024e+00},
                    { 6.1506503820e-01, -5.5823451281e-01, -7.7102023363e-01,
                        1.3960702419e+00, -2.3227377236e-01,  1.5084296465e+00,
                        2.4943335354e-01,  1.1076819897e+00, -5.2056992054e-01,
                    -1.0660402775e+00,  1.5976787806e+00},
                    { 2.1903742850e-01,  1.9679501653e-02,  3.1386128068e-01,
                    -4.0626621246e-01, -7.5507235527e-01, -1.0893251896e+00,
                        5.7655651122e-02,  1.7972362041e-01, -9.0133118629e-01,
                        7.7605450153e-01, -6.6169762611e-01},
                    {-6.5132927895e-01, -8.7547087669e-01, -9.1218388081e-01,
                        2.3385442793e-01, -3.1539368629e-01, -1.4544982910e+00,
                    -6.6423857212e-01, -9.6526098251e-01, -6.6600215435e-01,
                        6.4714908600e-01,  8.3344441652e-01},
                    {-1.5785104036e+00,  7.4335277081e-01, -1.1462401152e+00,
                        1.0709639788e+00,  2.4840381742e-01,  5.4372686148e-01,
                        5.2218210697e-01,  1.9122928381e+00, -1.7239218950e+00,
                    -1.0918600559e+00, -4.8291814327e-01},
                    { 9.1093665361e-01,  4.8966014385e-01, -5.2526658773e-01,
                    -5.5381953716e-01,  2.4685945511e+00,  1.8153283596e+00,
                        1.3610179424e+00,  1.5524566174e+00, -1.0007113218e+00,
                        9.3928158283e-01, -4.0532606840e-01},
                    { 9.2250317335e-01, -5.1795337349e-02, -9.9768131971e-01,
                        1.6249650717e+00, -9.7957354784e-01,  8.2169145346e-01,
                        9.0952342749e-01, -2.4217249453e-01,  5.2506601810e-01,
                        1.4915968180e+00,  1.8634383678e+00},
                    {-1.2066408396e+00, -2.1929471493e+00, -2.1238762140e-01,
                    -7.7625429630e-01,  9.4938635826e-01, -1.8003302813e+00,
                        3.4820947051e-01, -6.0576725006e-01, -4.6567910910e-01,
                        7.3214060068e-01, -7.3195713758e-01}},

                    {{ 5.3023105860e-01,  5.2188825607e-01, -7.3025727272e-01,
                        4.6667468548e-01, -1.9152610004e-01, -3.5157033801e-01,
                    -5.2651941776e-02, -4.8071330786e-01, -1.4187107980e-01,
                    -4.0443262458e-01, -9.0072855353e-02},
                    { 1.7881003022e-01,  1.0663819313e+00, -1.0256117582e+00,
                        8.7569499016e-01,  7.3652589321e-01, -2.6246693730e-01,
                    -1.6060233116e+00, -2.0135894418e-02, -3.0415063724e-02,
                        3.0278328061e-01,  1.0328743458e+00},
                    { 3.2101709396e-02, -1.9900113344e-01, -5.6631088257e-01,
                    -7.4470794201e-01,  1.7958736420e-01, -1.4189696312e+00,
                    -1.8464855850e-01, -1.0358128548e+00,  1.9816583395e+00,
                    -9.1884893179e-01,  9.0449694544e-03},
                    {-2.0810999870e+00, -2.4015277624e-01, -3.2068858147e+00,
                    -1.2780202925e-01,  5.5257743597e-01,  8.7900471687e-01,
                        1.0303863287e+00,  1.0323342085e+00, -7.1367937326e-01,
                    -1.9911302328e+00, -4.7755372524e-01},
                    { 4.7770789266e-01, -1.2471196651e+00,  1.4831469059e+00,
                    -8.7475031614e-01,  6.2958401442e-01,  2.4512550831e+00,
                        1.1762837172e+00,  1.0817874968e-01, -5.5265057087e-01,
                    -8.7890112400e-01, -8.8465034962e-01},
                    { 7.3697751760e-01,  1.0448017120e+00,  5.1342499256e-01,
                    -6.6379088163e-01,  1.3169301748e+00,  9.0157186985e-01,
                        7.0772147179e-01,  4.2946752161e-02,  2.8955113888e-01,
                        2.5413966179e-01,  5.9332638979e-01},
                    { 5.0560563803e-01,  1.8920665979e+00,  3.0823582411e-01,
                        1.1087694168e+00, -2.0810942352e-01, -5.2579015493e-01,
                    -8.4162759781e-01, -2.1426311135e-01, -2.2446355820e+00,
                        3.9921768010e-02,  6.9279879332e-01},
                    { 1.1803445816e+00, -8.5259515047e-01, -9.9684113264e-01,
                        3.4527037144e+00, -1.5741628408e+00, -3.5193979740e-01,
                    -1.4004269838e+00,  4.2186367512e-01, -4.9055755138e-02,
                    -1.2086832523e-01,  1.7582530975e+00},
                    { 5.2762091160e-01, -7.7123051882e-01,  2.0454251766e+00,
                        2.2788069248e+00, -1.5510680676e+00, -1.9315464497e+00,
                    -9.0742290020e-01, -1.6089993715e+00, -5.3302454948e-01,
                        3.5658752918e-01,  4.8080060631e-02},
                    { 1.8064410686e+00,  3.6816290021e-01, -1.5741494894e+00,
                        5.8802300692e-01,  4.7199991345e-01,  1.2889680862e+00,
                        8.7419849634e-01, -3.9012792706e-01,  5.6346172094e-01,
                    -7.6179641485e-01,  1.3050407171e+00},
                    {-9.1590619087e-01, -1.8752954006e+00,  1.4963175058e+00,
                        3.2759961486e-01,  9.2106527090e-01, -1.1356848478e+00,
                    -1.3705831766e+00, -8.2606017590e-01, -1.3079185486e+00,
                        2.6973825693e-01, -6.5730160475e-01}},

                    {{ 9.5533591509e-01, -9.9876197055e-04, -1.4797580242e+00,
                        2.1985734999e-01,  4.8844724894e-01,  4.3145084381e-01,
                    -2.1500962973e-01, -7.0111054182e-01,  6.9973039627e-01,
                    -7.2476547956e-01,  4.8026397824e-01},
                    {-8.8500595093e-01,  1.8992747068e+00,  7.7701354027e-01,
                    -6.0169208050e-01,  1.0013473034e+00,  8.4491990507e-02,
                    -9.8977982998e-01, -7.9020494223e-01,  3.2907465100e-01,
                    -1.2078856677e-01,  1.5202083588e+00},
                    {-2.7657735348e-01,  4.6925004572e-02,  1.2281295061e+00,
                    -1.8579104543e-01,  1.0336677730e-01,  2.5693323612e+00,
                        8.9827783406e-02,  2.2823791504e+00, -8.2009571791e-01,
                        2.0414433479e+00, -9.0024584532e-01},
                    { 2.6029777527e+00,  4.0565639734e-01,  1.2988950312e-01,
                    -1.2674516439e+00,  5.8589053154e-01,  2.4598875046e+00,
                        8.9385128021e-01,  6.4068651199e-01,  1.7348393798e-02,
                        1.2424468994e+00, -8.4993803501e-01},
                    { 5.7889044285e-01,  3.8729000092e-01, -8.8090997934e-01,
                        2.1381093562e-01,  1.4890091419e+00,  1.5105549097e+00,
                        1.4098797739e-01,  1.0446792096e-01, -1.9159198999e+00,
                        1.3064564764e-01, -1.6926348209e-01},
                    { 1.1417644024e+00, -1.4733666182e+00,  3.2986238599e-01,
                        1.8303622305e-01,  5.6586086750e-01, -5.2473092079e-01,
                    -7.5201815367e-01, -1.5739550814e-02, -1.5592651367e+00,
                    -1.4688136578e+00, -3.3142146468e-01},
                    {-7.3924712837e-02,  1.8161753416e+00, -9.5422208309e-01,
                        3.4323176742e-01, -2.2727070749e-01, -1.1031615734e+00,
                        5.7045269012e-01,  1.6896954775e+00,  1.0372216702e+00,
                    -1.3280247152e-01, -1.3075873852e+00},
                    {-4.0329605341e-01, -1.1308847666e+00, -5.7332462072e-01,
                    -4.2800852656e-01,  7.3079723120e-01,  1.4624267817e-01,
                    -2.4124519527e-01,  1.6443549395e+00,  2.1521264315e-01,
                    -3.0984909534e+00,  2.1323997974e+00},
                    { 5.5337917060e-02, -7.7057784796e-01,  3.2530885935e-01,
                        1.3282178640e+00, -3.2126638293e-01,  2.9032289982e-01,
                    -2.4100792408e-01, -1.1505941153e+00, -4.0858381987e-01,
                        3.8038328290e-01,  1.0238400698e+00},
                    {-1.0223561525e+00,  5.4754292965e-01,  8.9632779360e-01,
                        4.0344274044e-01, -7.0289498568e-01, -1.1168614626e+00,
                        3.1760175228e+00,  2.0348765850e+00, -1.0406352282e+00,
                        1.0582931042e+00,  1.1740338057e-01},
                    { 6.0107231140e-01,  8.4875309467e-01, -8.5171341896e-02,
                    -1.2264981270e+00,  1.1493313313e+00, -1.9127263129e-01,
                        5.3371381760e-01, -4.7718715668e-01,  8.9841789007e-01,
                    -4.7041997313e-01, -1.1772131920e+00}},

                    {{ 4.0120658278e-01,  6.7281287909e-01, -4.7505354881e-01,
                    -3.1049102545e-02, -5.2430522442e-01, -9.7885608673e-01,
                    -8.1729829311e-01,  2.8434273601e-01,  2.2878241539e-01,
                        7.0183002949e-01, -6.1007946730e-01},
                    {-1.4632455111e+00, -9.4703143835e-01,  3.1175765395e-01,
                        1.7414463758e+00,  1.2987858057e+00,  2.5278210640e+00,
                    -2.5223663449e-01, -7.2194322944e-02, -9.3486815691e-01,
                    -5.4429602623e-01, -1.5758562088e+00},
                    {-1.1150578260e+00,  3.1018608809e-01, -1.0259387493e+00,
                        4.9761269242e-02,  2.2564062476e-01,  2.2673048079e-01,
                    -1.2348350286e+00, -4.8837900162e-01, -5.5627411604e-01,
                        2.3974895477e+00,  1.5627510548e+00},
                    { 1.3537845612e+00, -1.4481093884e+00, -5.8862978220e-01,
                    -9.2907649279e-01,  1.7989814281e-01, -6.1403113604e-01,
                        3.9550009370e-01,  2.0637707412e-01,  1.4092805982e-01,
                        1.4354915619e+00,  4.1124477983e-01},
                    { 5.9437131882e-01,  2.5175651908e-01, -1.4724839926e+00,
                    -7.9224598408e-01, -1.0697947443e-01, -9.3873560429e-01,
                    -8.3823198080e-01,  1.0682547092e+00,  1.4871965647e+00,
                    -1.7402729392e-01, -4.1061699390e-01},
                    { 6.1316050589e-02, -5.1780641079e-01,  3.9551302791e-01,
                        1.3394130766e-01,  1.0029216856e-01,  5.2646106482e-01,
                    -2.3723851889e-02, -7.3339444399e-01,  9.1420966387e-01,
                    -9.4718337059e-01, -4.3122315407e-01},
                    { 3.1069964170e-01, -5.1376241446e-01,  4.0816228837e-02,
                    -1.0862566233e+00, -7.4995791912e-01,  1.4363372326e-01,
                    -2.1348357201e+00,  7.7824163437e-01,  1.4786756039e-01,
                    -7.7644962072e-01,  5.6383001804e-01},
                    {-5.8460813761e-01,  2.1913936362e-02,  2.7573537827e+00,
                        1.6296634078e-01,  4.5511564612e-01, -1.9915504456e+00,
                        2.9748791456e-01,  4.2073163390e-01, -7.9228687286e-01,
                        7.1524131298e-01, -2.3795824051e+00},
                    { 3.2347491384e-01, -1.2526123524e+00,  1.0551978350e+00,
                    -7.1423876286e-01, -2.3097810149e-01, -1.5859640837e+00,
                    -9.0056812763e-01,  2.4794772267e-01,  1.1709301472e+00,
                    -9.2559927702e-01, -1.4513528347e+00},
                    {-6.9863849878e-01, -5.9516018629e-01,  8.8701313734e-01,
                        1.2166969776e+00, -1.2960427999e+00,  1.5974614620e+00,
                    -1.1376231909e+00,  3.8471198082e-01, -2.3815085888e+00,
                    -1.9736941159e-01,  1.2386144400e+00},
                    { 1.3419134617e+00, -3.7865355611e-02, -6.7163608968e-02,
                        6.4137578011e-01, -4.0734642744e-01, -3.7681248784e-01,
                    -4.8736298084e-01,  5.5279612541e-01,  9.2274624109e-01,
                    -1.1439754963e+00,  3.9059036970e-01}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {
                {-0.0264293905,  0.1734595597, -0.0226496458,  0.0903987661}
            });
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,1,5,5> {
                {{{{ 0.0299229864, -0.0070633413,  0.0387998335, -0.1524392813,
                    -0.1102494225},
                    { 0.0792495012, -0.1920675784, -0.0624185093,  0.0321180820,
                        0.0158791542},
                    {-0.1553061306, -0.1615069360,  0.1939006597, -0.1880072653,
                        0.1276774853},
                    {-0.1049263999, -0.1676749289,  0.0386066921,  0.1836618483,
                    -0.1901892275},
                    {-0.0461415537, -0.1792095006, -0.1822651625,  0.1757787466,
                    -0.1020090356}}},

                    {{{ 0.1273712367, -0.0565941110,  0.0162034985,  0.0944691673,
                        0.1084327474},
                    { 0.0596342087,  0.0055869580,  0.0252192747,  0.1085267588,
                    -0.1543913186},
                    { 0.0770991370, -0.1246689111,  0.1107715368, -0.1920998394,
                    -0.0687034130},
                    { 0.0345953479, -0.1752771586, -0.0221131798, -0.1240047216,
                    -0.1447991878},
                    {-0.1521565169,  0.0231835600, -0.0339560769, -0.1456816643,
                    -0.0323682800}}},

                    {{{-0.0775303841, -0.0420084260,  0.0729985982, -0.1716768742,
                        0.0249956138},
                    {-0.1547612250,  0.0420673378,  0.0686141029, -0.0187673103,
                        0.0606986992},
                    {-0.0045782328,  0.1753131598,  0.0497865193,  0.1222290322,
                    -0.0149712088},
                    {-0.0015272141, -0.0793256983, -0.0027205944,  0.0122313974,
                        0.0382683761},
                    { 0.1789925098,  0.1004394516,  0.1536173671,  0.0328379385,
                    -0.1461270154}}},

                    {{{-0.0308369156,  0.0518908277, -0.0238906145,  0.0774943829,
                        0.1786959022},
                    {-0.1896944493,  0.0252965689,  0.1969553977,  0.0170207266,
                    -0.1349055022},
                    { 0.1111816689, -0.1292364597,  0.1484523267,  0.1492144167,
                    -0.0654201508},
                    {-0.0588786863,  0.0246491432,  0.1550032198,  0.1314135045,
                    -0.0048915865},
                    { 0.0985754058, -0.0685965568, -0.1517471522, -0.1095958278,
                        0.1006983072}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,1,4,2,2> {
                {{{{-0.0801755786, -0.4114282727},
                    { 0.8874194026, -0.3093988597}},
                    {{ 0.2365355641,  0.5801096559},
                    { 1.2374895811,  1.2885351181}},
                    {{ 0.2366760373, -0.4241298735},
                    {-0.2304262370,  0.8197762370}},
                    {{ 0.1419754326, -0.6357300878},
                    {-0.7330727577,  0.3483830392}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
    }
}