/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <cmath>    // std::abs
#include <cstddef>  // std::size_t
#include <memory>

#include <catch2/catch_test_macros.hpp>

#include "aidge/backend/cpu/operator/AvgPoolingImpl.hpp"
#include "aidge/data/Data.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/operator/AvgPooling.hpp"
#include "aidge/utils/ArrayHelpers.hpp"

using namespace Aidge;

TEST_CASE("[cpu/operator] AvgPooling(forward)", "[AvgPooling][CPU]") {
    std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,2,5,5> { //NCHW
        {
            {
                {{  0,   1,   2,   3,   4},
                 {  5,   6,   7,   8,   9},
                 { 10,  11,  12,  13,  14},
                 { 15,  16,  17,  18,  19},
                 { 20,  21,  22,  23,  24}},

                {{ 25,  26,  27,  28,  29},
                 { 30,  31,  32,  33,  34},
                 { 35,  36,  37,  38,  39},
                 { 40,  41,  42,  43,  44},
                 { 45,  46,  47,  48,  49}}
            },
            {
                {{100, 101, 102, 103, 104},
                 {105, 106, 107, 108, 109},
                 {110, 111, 112, 113, 114},
                 {115, 116, 117, 118, 119},
                 {120, 121, 122, 123, 124}},

                {{125, 126, 127, 128, 129},
                 {130, 131, 132, 133, 134},
                 {135, 136, 137, 138, 139},
                 {140, 141, 142, 143, 144},
                 {145, 146, 147, 148, 149}}
            }
        }
    });
    SECTION("Stride") {
        std::shared_ptr<Node> myAvgPool = AvgPooling({2,2}, "mycdw", {2,2});
        auto op = std::static_pointer_cast<AvgPooling_Op<2>>(myAvgPool -> getOperator());

        std::shared_ptr<Tensor> myOutput = std::make_shared<Tensor>(Array4D<float,2,2,2,2> {
            {
                {
                    {{  3,   5},
                     { 13,  15}},
                    {{ 28,  30},
                     { 38,  40}}
                },
                {
                    {{103, 105},
                     {113, 115}},
                    {{128, 130},
                     {138, 140}}
                }
            }
        });
        op->associateInput(0,myInput);
        op->setDataType(DataType::Float32);
        op->setBackend("cpu");
        myAvgPool->forward();
        op->getOutput(0)->print();
        REQUIRE(*(op->getOutput(0)) == *myOutput);
    }

    SECTION("Stride >= feature dim") {
        std::shared_ptr<Tensor> myInput2 = std::make_shared<Tensor>(Array4D<float,1,1,3,3> { //NCHW
        {
            {
                {{0.3745, 0.9507, 0.7320},
                 {0.5987, 0.1560, 0.1560},
                 {0.0581, 0.8662, 0.6011}}
            }
        }
        });
        std::shared_ptr<Node> myAvgPool = AvgPooling({3,3}, "mycdw", {3,3});
        auto op = std::static_pointer_cast<AvgPooling_Op<2>>(myAvgPool -> getOperator());

        Tensor myOutput = Array4D<float,1,1,1,1> {
            {{{{(0.3745 + 0.9507 + 0.7320 + 0.5987 + 0.1560 + 0.1560 + 0.0581 + 0.8662 + 0.6011)/9.0}}}}
        };
        op->associateInput(0,myInput2);
        op->setDataType(DataType::Float32);
        op->setBackend("cpu");
        myAvgPool->forward();
        op->getOutput(0)->print();
        float* outPtr = static_cast<float*>(op->getOutput(0)->getImpl()->rawPtr());
        float* expectedOutPtr = static_cast<float*>(myOutput.getImpl()->rawPtr());
        for (std::size_t i = 0; i < 1; ++i) {
            REQUIRE(std::abs(outPtr[i] - expectedOutPtr[i]) < 0.00001);
        }
    }
    SECTION("Dilations") {
        std::shared_ptr<Tensor> myInput3 = std::make_shared<Tensor>(Array4D<float,1,1,5,5> { // NCHW
        {
            {
                {{ 1,  2,  3,  4,  5},
                { 6,  7,  8,  9, 10},
                {11, 12, 13, 14, 15},
                {16, 17, 18, 19, 20},
                {21, 22, 23, 24, 25}}
            }
        }
        });

        // Dilation of 2 means we take every second element in the window
        std::shared_ptr<Node> myAvgPool = AvgPooling({2,2}, "mycdw", {1,1}, {2,2}); 
        auto op = std::static_pointer_cast<AvgPooling_Op<2>>(myAvgPool -> getOperator());

        std::shared_ptr<Tensor> myOutput3 = std::make_shared<Tensor>(Array4D<float,1,1,3,3> {
            {
                {
                    {{  7,  8,  9},
                    { 12, 13, 14},
                    { 17, 18, 19}}
                }
            }
        });

        op->associateInput(0, myInput3);
        op->setDataType(DataType::Float32);
        op->setBackend("cpu");
        myAvgPool->forward();
        op->getOutput(0)->print();
        REQUIRE(*(op->getOutput(0)) == *myOutput3);
    }
    SECTION("Ceil Mode") {
        std::shared_ptr<Tensor> myInput4 = std::make_shared<Tensor>(Array4D<float,1,1,5,5> { // NCHW
        {
            {
                {
                    { 1,  2,  3,  4,  5},
                    { 6,  7,  8,  9, 10},
                    {11, 12, 13, 14, 15},
                    {16, 17, 18, 19, 20},
                    {21, 22, 23, 24, 25}
                }
            }
        }
        });

        // AvgPool with ceil_mode = true
        std::shared_ptr<Node> myAvgPool1 = AvgPooling({2,2}, "mycdw", {2,2}, {1,1}, true);
        auto op1 = std::static_pointer_cast<AvgPooling_Op<2>>(myAvgPool1 -> getOperator());

        std::shared_ptr<Tensor> myOutput4 = std::make_shared<Tensor>(Array4D<float,1,1,3,3> {
            {
                {
                    {
                        {  4.0,  6.0,  7.5 },
                        { 14.0, 16.0, 17.5 },
                        { 21.5, 23.5, 25.0 }
                    }
                }
            }
        });
        op1->associateInput(0, myInput4);
        op1->setDataType(DataType::Float32);
        op1->setBackend("cpu");
        myAvgPool1->forward();
        op1->getOutput(0)->print();
        REQUIRE(*(op1->getOutput(0)) == *myOutput4);

        // AvgPool with ceil_mode = false
        std::shared_ptr<Node> myAvgPool2 = AvgPooling({2,2}, "mycdw", {2,2}, {1,1}, false);
        auto op2 = std::static_pointer_cast<AvgPooling_Op<2>>(myAvgPool2 -> getOperator());
        std::shared_ptr<Tensor> myOutput5 = std::make_shared<Tensor>(Array4D<float,1,1,2,2> {
            {
                {
                    {
                        {  4.0,  6.0 },
                        { 14.0, 16.0 }
                    }
                }
            }
        });
        op2->associateInput(0, myInput4);
        op2->setDataType(DataType::Float32);
        op2->setBackend("cpu");
        myAvgPool2->forward();
        op2->getOutput(0)->print();
        REQUIRE(*(op2->getOutput(0)) == *myOutput5);
    }

    SECTION("Simple test") {
      std::shared_ptr<Tensor> tensor =
          std::make_shared<Tensor>(Array4D<int32_t, 1, 1, 7, 7>{{{{
              {0, 8, 26, 35, 49, 45, 22},
              {2, 24, 48, 66, 60, 46, 26},
              {8, 41, 64, 68, 39, 18, 9},
              {10, 48, 72, 76, 42, 14, 9},
              {6, 29, 52, 65, 27, 7, 3},
              {1, 9, 24, 31, 18, 7, 1},
              {0, 0, 4, 6, 7, 1, 1}}}}});

        auto op = AvgPooling2D_Op({7, 7});
        op.setDataType(DataType::Int32);
        op.setBackend("cpu");

        op.associateInput(0, tensor);
        op.forwardDims();
        op.forward();
        REQUIRE(op.getOutput(0)->get<int32_t>(0) == 26);
    }
}