/********************************************************************************
 * Copyright (c) 2025 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <cstddef>  // std::size_t
#include <memory>

#include <catch2/catch_test_macros.hpp>

#include "aidge/data/Tensor.hpp"
#include "aidge/operator/Dropout.hpp"
#include "aidge/utils/TensorUtils.hpp"

using namespace Aidge;

 TEST_CASE("[cpu/operator] Dropout(forward - inference mode / MC dropout)", "[Dropout][CPU]") {

    SECTION("MC Dropout - check stochastic output and scaling") {
        constexpr const std::size_t nb_elements = 6;
        std::shared_ptr<Tensor> input = std::make_shared<Tensor>(Array1D<cpptype_t<DataType::Float32>,nb_elements> {
            {1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f}
        });

        constexpr const float dropout_prob = 0.5f;
        std::shared_ptr<Node> myDropout = Dropout(dropout_prob); // assumes dropout always active
        auto op = std::static_pointer_cast<OperatorTensor>(myDropout->getOperator());

        op->associateInput(0, input);
        op->setBackend("cpu");
        op->forwardDType();

        myDropout->forward();
        auto output = op->getOutput(0);

        std::size_t num_zero = 0, num_scaled = 0;
        constexpr const float scale = 1.0f / (1.0f - dropout_prob);

        for (std::size_t i = 0; i < nb_elements; ++i) {
            const float out = output->get<cpptype_t<DataType::Float32>>(i);
            if (out == 0.0f)
                ++num_zero;
            else {
                REQUIRE(approxEq<cpptype_t<DataType::Float32>>(Tensor(out),Tensor(scale)));  // scaled version of 1.0f
                ++num_scaled;
            }
        }

        // Ensure dropout is working
        REQUIRE(num_zero + num_scaled == nb_elements);
        REQUIRE(output->dims() == input->dims());  // TODO: test this in core module
    }

    SECTION("Stochasticity - multiple forward passes differ") {
        // /!\ Warning: With too few elements, this test has a small
        //     but real chance of failing.
        constexpr const std::size_t nb_elements = 100;
        std::shared_ptr<Tensor> input = std::make_shared<Tensor>(Array1D<cpptype_t<DataType::Float32>, nb_elements> {
            {1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
             1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f, 1.0f}
        });

        constexpr const float dropout_prob = 0.3f;
        std::shared_ptr<Node> myDropout = Dropout(dropout_prob);
        auto op = std::static_pointer_cast<OperatorTensor>(myDropout->getOperator());
        op->associateInput(0, input);
        op->setBackend("cpu");
        op->forwardDType();

        std::vector<cpptype_t<DataType::Float32>> run1, run2;

        myDropout->forward();
        auto out1 = op->getOutput(0);
        for (std::size_t i = 0; i < nb_elements; ++i)
            run1.push_back(out1->get<cpptype_t<DataType::Float32>>(i));

        myDropout->forward();
        auto out2 = op->getOutput(0);
        for (std::size_t i = 0; i < nb_elements; ++i)
            run2.push_back(out2->get<cpptype_t<DataType::Float32>>(i));

        // Not all elements should be identical between the two runs
        std::size_t same_count = 0;
        for (std::size_t i = 0; i < nb_elements; ++i) {
            if (run1[i] == run2[i])
                same_count++;
        }
    }
}