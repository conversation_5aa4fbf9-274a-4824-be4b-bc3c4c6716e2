/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/
#include <chrono>      // std::micro, std::chrono::time_point,
                       // std::chrono::system_clock
#include <cstddef>   // std::size_t
#include <cstdint>   // std::uint16_t
#include <chrono>
#include <memory>
#include <numeric>
#include <random>    // std::random_device, std::mt19937, std::uniform_real_distribution

#include <catch2/catch_test_macros.hpp>
#include <fmt/core.h>

#include "aidge/backend/cpu/data/TensorImpl.hpp"
#include "aidge/backend/cpu/operator/BitShiftImpl.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/operator/BitShift.hpp"
#include "aidge/utils/TensorUtils.hpp"

namespace Aidge {

TEST_CASE("[cpu/operator] BitShift_TEST", "[BitShift][CPU]") {
    constexpr std::uint16_t NBTRIALS = 15;
    // Create a random number generator
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<int> valueDist(-15, 15);
    std::uniform_int_distribution<std::size_t> dimSizeDist(std::size_t(2), std::size_t(5));
    std::uniform_int_distribution<std::size_t> nbDimsDist(std::size_t(1), std::size_t(3));
    std::uniform_int_distribution<int> boolDist(0,1);

    BitShift_Op::BitShiftDirection direction = BitShift_Op::BitShiftDirection::left;

    if(valueDist(gen) % 2 == 0)
    {
        direction = BitShift_Op::BitShiftDirection::right;
    }

    // Create BitShift Operator
    std::shared_ptr<Node> myBitShift = BitShift(direction);
    auto op = std::static_pointer_cast<OperatorTensor>(myBitShift-> getOperator());
    op->setDataType(DataType::Int32);
    op->setBackend("cpu");

    // Create 2 input Tensors
    std::shared_ptr<Tensor> T0 = std::make_shared<Tensor>();
    op->associateInput(0,T0);
    T0->setDataType(DataType::Int32);
    T0->setBackend("cpu");
    std::shared_ptr<Tensor> T1 = std::make_shared<Tensor>();
    op -> associateInput(1,T1);
    T1->setDataType(DataType::Int32);
    T1->setBackend("cpu");

    // Create results Tensor
    std::shared_ptr<Tensor> Tres = std::make_shared<Tensor>();
    Tres->setDataType(DataType::Int32);
    Tres->setBackend("cpu");

    // To measure execution time of 'BitShift_Op::forward()' member function call
    std::chrono::time_point<std::chrono::system_clock> start;

    std::chrono::time_point<std::chrono::system_clock> end;
    std::chrono::duration<double, std::micro> duration{};

    SECTION("BitShiftImpl_cpu::forward()") {
        SECTION("Test Forward Kernel with same dimensions") {
            std::size_t number_of_operation = 0;

            for (std::uint16_t trial = 0; trial < NBTRIALS; ++trial) {
                // generate 2 random Tensors
                const std::size_t nbDims = nbDimsDist(gen);
                std::vector<std::size_t> dims;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    dims.push_back(dimSizeDist(gen));
                }
                const std::size_t nb_elements = std::accumulate(dims.cbegin(), dims.cend(), std::size_t(1), std::multiplies<std::size_t>());
                number_of_operation += nb_elements;

                // without broadcasting
                int* array0 = new int[nb_elements];
                int* array1 = new int[nb_elements];
                int* result = new int[nb_elements];

                for (std::size_t i = 0; i < nb_elements; ++i) {
                    array0[i] = valueDist(gen);
                    array1[i] = std::abs(valueDist(gen)); // bitshift is impossible with negative value
                    if(direction == BitShift_Op::BitShiftDirection::left)
                    {
                        result[i] = array0[i] << array1[i];
                    }
                    else
                    {
                        result[i] = array0[i] >> array1[i];
                    }
                }

                // input0
                T0->resize(dims);
                T0 -> getImpl() -> setRawPtr(array0, nb_elements);

                // input1
                T1->resize(dims);
                T1 -> getImpl() -> setRawPtr(array1, nb_elements);

                // results
                Tres->resize(dims);
                Tres -> getImpl() -> setRawPtr(result, nb_elements);

                op->forwardDims();
                start = std::chrono::system_clock::now();
                myBitShift->forward();
                end = std::chrono::system_clock::now();
                duration += std::chrono::duration_cast<std::chrono::microseconds>(end - start);

                bool is_eq = approxEq<int>(*(op->getOutput(0)), *Tres);

                auto Output = *(op->getOutput(0));
                auto prt = Output.getImpl()->rawPtr();

                REQUIRE(is_eq);

                delete[] array0;
                delete[] array1;
                delete[] result;


            }
            Log::info("number of elements over time spent: {}\n", (number_of_operation / duration.count()));
            Log::info("total time: {}μs\n", duration.count());
        }
        SECTION("Test Forward Kernel with same dimensions and applying rounding") {
            std::shared_ptr<Node> RoundBitShift = BitShift(BitShift_Op::BitShiftDirection::right,true);
            auto op_r = std::static_pointer_cast<OperatorTensor>(RoundBitShift-> getOperator());
            op_r->setDataType(DataType::Int32);
            op_r->setBackend("cpu");

            // Create 2 input Tensors
            std::shared_ptr<Tensor> T0_r = std::make_shared<Tensor>();
            op_r->associateInput(0,T0_r);
            T0_r->setDataType(DataType::Int32);
            T0_r->setBackend("cpu");
            std::shared_ptr<Tensor> T1_r = std::make_shared<Tensor>();
            op_r -> associateInput(1,T1_r);
            T1_r->setDataType(DataType::Int32);
            T1_r->setBackend("cpu");

            // Create results Tensor
            std::shared_ptr<Tensor> Tres_r = std::make_shared<Tensor>();
            Tres_r->setDataType(DataType::Int32);
            Tres_r->setBackend("cpu");
            std::size_t number_of_operation = 0;

            for (std::uint16_t trial = 0; trial < NBTRIALS; ++trial) {
                // generate 2 random Tensors
                const std::size_t nbDims = nbDimsDist(gen);
                std::vector<std::size_t> dims;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    dims.push_back(dimSizeDist(gen));
                }
                const std::size_t nb_elements = std::accumulate(dims.cbegin(), dims.cend(), std::size_t(1), std::multiplies<std::size_t>());
                number_of_operation += nb_elements;

                // without broadcasting
                int* array0 = new int[nb_elements];
                int* array1 = new int[nb_elements];
                int* result = new int[nb_elements];
                for (std::size_t i = 0; i < nb_elements; ++i)
                {
                    array0[i] = valueDist(gen);
                    array1[i] = std::abs(valueDist(gen)); // bitshift is impossible with negative value
                    result[i] = array0[i] >> array1[i];
                    if(array1[i] > 0) //Cannot use rounding when shift value is 0
                        result[i] = ((array0[i] >> (array1[i] - 1)) + 1) >> 1;
                }

                // input0
                T0_r->resize(dims);
                T0_r -> getImpl() -> setRawPtr(array0, nb_elements);

                // input1
                T1_r->resize(dims);
                T1_r -> getImpl() -> setRawPtr(array1, nb_elements);

                // results
                Tres_r->resize(dims);
                Tres_r -> getImpl() -> setRawPtr(result, nb_elements);

                op_r->forwardDims();
                start = std::chrono::system_clock::now();
                RoundBitShift->forward();
                end = std::chrono::system_clock::now();
                duration += std::chrono::duration_cast<std::chrono::microseconds>(end - start);

                bool is_eq_round = approxEq<int>(*(op_r->getOutput(0)), *Tres_r);
                auto Output = *(op_r->getOutput(0));
                auto prt = Output.getImpl()->rawPtr();

                REQUIRE(is_eq_round);

                delete[] array0;
                delete[] array1;
                delete[] result;
            }
            Log::info("number of elements over time spent: {}\n", (number_of_operation / duration.count()));
            Log::info("total time: {}μs\n", duration.count());
        }
        SECTION("Test BitShift kernels with Broadcasting") {
            std::size_t number_of_operation = 0;

            for (std::uint16_t trial = 0; trial < NBTRIALS; ++trial) {
                // generate 2 random Tensors
                // handle dimensions, replace some dimensions with '1' to get broadcasting
                constexpr std::size_t nbDims = 4;
                std::vector<std::size_t> dims;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    dims.push_back(dimSizeDist(gen));
                }
                std::vector<std::size_t> dims0 = dims;
                std::vector<std::size_t> dims1 = dims;
                std::vector<std::size_t> dimsOut = dims;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    if (boolDist(gen)) {
                        dims0[i] = 1;
                    }
                    if (boolDist(gen)) {
                        dims1[i] = 1;
                    }
                    dimsOut[i] = (dims0[i] == 1) ? dims1[i] : dims0[i];
                }

                // create arrays and fill them with random values
                int* array0 = new int[dims0[0]*dims0[1]*dims0[2]*dims0[3]];
                int* array1 = new int[dims1[0]*dims1[1]*dims1[2]*dims1[3]];
                int* result = new int[dimsOut[0]*dimsOut[1]*dimsOut[2]*dimsOut[3]];

                for (std::size_t i = 0; i < dims0[0]*dims0[1]*dims0[2]*dims0[3]; ++i) {
                    array0[i] = valueDist(gen);
                }
                for (std::size_t i = 0; i < dims1[0]*dims1[1]*dims1[2]*dims1[3]; ++i) {
                    array1[i] = std::abs(valueDist(gen));
                }

                //True result with broadcast
                const std::size_t strides0[nbDims] = {dims0[1]*dims0[2]*dims0[3], dims0[2]*dims0[3], dims0[3], 1};
                const std::size_t strides1[nbDims] = {dims1[1]*dims1[2]*dims1[3], dims1[2]*dims1[3], dims1[3], 1};
                for (std::size_t a = 0; a < dimsOut[0]; ++a) {
                    for (std::size_t b = 0; b < dimsOut[1]; ++b) {
                        const std::size_t idx0_0 = strides0[0] * ((dims0[0] > 1) ? a : 0)
                                                    + strides0[1] * ((dims0[1] > 1) ? b : 0);
                        const std::size_t idx1_0 = strides1[0] * ((dims1[0] > 1) ? a : 0)
                                                    + strides1[1] * ((dims1[1] > 1) ? b : 0);
                        for (std::size_t c = 0; c < dimsOut[2]; ++c) {
                            const std::size_t idx_out = dimsOut[3] * (c + dimsOut[2] * (b + dimsOut[1] * a));
                            for (std::size_t d = 0; d < dimsOut[3]; ++d) {
                                std::size_t idx0 = idx0_0
                                                    + strides0[2] * ((dims0[2] > 1) ? c : 0)
                                                    + ((dims0[3] > 1) ? d : 0);
                                std::size_t idx1 = idx1_0
                                                    + strides1[2] * ((dims1[2] > 1) ? c : 0)
                                                    + ((dims1[3] > 1) ? d : 0);
                                if(direction == BitShift_Op::BitShiftDirection::left)
                                {
                                    result[idx_out + d] = array0[idx0] << array1[idx1];
                                }
                                else
                                {
                                    result[idx_out + d] = array0[idx0] >> array1[idx1];
                                }
                            }
                        }
                    }
                }

                // conversion to Aidge::Tensors
                // input0
                T0->resize(dims0);
                T0 -> getImpl() -> setRawPtr(array0, dims0[0]*dims0[1]*dims0[2]*dims0[3]);

                // input1
                T1->resize(dims1);
                T1 -> getImpl() -> setRawPtr(array1, dims1[0]*dims1[1]*dims1[2]*dims1[3]);

                // results
                Tres->resize(dimsOut);
                Tres -> getImpl() -> setRawPtr(result, dimsOut[0]*dimsOut[1]*dimsOut[2]*dimsOut[3]);

                // compute result
                op->forwardDims();
                start = std::chrono::system_clock::now();
                myBitShift->forward();
                end = std::chrono::system_clock::now();
                duration += std::chrono::duration_cast<std::chrono::microseconds>(end - start);

                // comparison between truth and computed result
                REQUIRE(approxEq<int>(*(op->getOutput(0)), *Tres));

                delete[] array0;
                delete[] array1;
                delete[] result;

                const std::size_t nb_elements = std::accumulate(dimsOut.cbegin(), dimsOut.cend(), std::size_t(1), std::multiplies<std::size_t>());
                number_of_operation += nb_elements;
            }
            Log::info("number of elements over time spent: {}\n", (number_of_operation / duration.count()));
            Log::info("total time: {}μs\n", duration.count());
        }

}
} // namespace Aidge
}