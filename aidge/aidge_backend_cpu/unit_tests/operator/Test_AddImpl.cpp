/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <memory>
#include <random>

#include <catch2/catch_test_macros.hpp>

#include "aidge/backend/cpu/operator/AddImpl.hpp"
#include "aidge/data/Data.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/operator/Add.hpp"
#include "aidge/utils/ArrayHelpers.hpp"
#include "aidge/utils/TensorUtils.hpp"

using namespace Aidge;

TEST_CASE("[cpu/operator] Add(forward)", "[Add][CPU]") {
    std::shared_ptr<Tensor> input1 = std::make_shared<Tensor>(Array4D<int,3,3,3,2> {
        {                                       //
            {                                   //
                {{20, 47},{21, 48},{22, 49}},   //
                {{23, 50},{24, 51},{25, 52}},   //
                {{26, 53},{27, 54},{28, 55}}    //
            },                                  //
            {                                   //
                {{29, 56},{30, 57},{31, 58}},   //
                {{32, 59},{33, 60},{34, 61}},   //
                {{35, 62},{36, 63},{37, 64}}    //
            },                                  //
            {                                   //
                {{38, 65},{39, 66},{40, 67}},   //
                {{41, 68},{42, 69},{43, 70}},   //
                {{44, 71},{45, 72},{46, 73}}    //
            }                                   //
        }                                       //
    });                                         //

    SECTION("Two inputs") {
        std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<int,3,3,3,2> {
            {
                {
                    {{40,  94},{42,  96},{44,  98}},
                    {{46, 100},{48, 102},{50, 104}},
                    {{52, 106},{54, 108},{56, 110}}
                },
                {
                    {{58, 112},{60, 114},{62, 116}},
                    {{64, 118},{66, 120},{68, 122}},
                    {{70, 124},{72, 126},{74, 128}}
                },
                {
                    {{76, 130},{78, 132},{80, 134}},
                    {{82, 136},{84, 138},{86, 140}},
                    {{88, 142},{90, 144},{92, 146}}
                }
            }
        });

        std::shared_ptr<Node> myAdd = Add();
        auto op = std::static_pointer_cast<OperatorTensor>(myAdd -> getOperator());
        op->associateInput(0, input1);
        op->associateInput(1, input1);
        op->setBackend("cpu");
        op->setDataType(DataType::Int32);
        myAdd->forward();

        REQUIRE(*(op->getOutput(0)) == *expectedOutput);
    }

    SECTION("Broadcasting") {
        std::shared_ptr<Tensor> input_0 = std::make_shared<Tensor>(Array4D<int,3,1,3,2> {
        {                                       //
            {                                   //
                {{0, 1},{2, 3},{4, 5}}          //
            },                                  //
            {                                   //
                {{6, 7},{8, 9},{10, 11}}        //
            },                                  //
            {                                   //
                {{12, 13},{14, 15},{16, 17}}    //
            }                                   //
        }                                       //
        });                                     //
        std::shared_ptr<Tensor> input_1 = std::make_shared<Tensor>(Array4D<int,1,3,3,2> {
        {                                       //
            {                                   //
                {{20, 21},{22, 23},{24, 25}},   //
                {{26, 27},{28, 29},{30, 31}},   //
                {{32, 33},{34, 35},{36, 37}}    //
            }                                   //
        }                                       //
        });                                     //

        std::shared_ptr<Tensor> input_2 = std::make_shared<Tensor>(Array1D<int,2> {{100,200}});
        Tensor expectedOutput = Array4D<int,3,3,3,2> {
            {                                               //
                {                                           //
                    {{ 120, 222},{ 124, 226},{ 128, 230}},  //
                    {{ 126, 228},{ 130, 232},{ 134, 236}},  //
                    {{ 132, 234},{ 136, 238},{ 140, 242}}   //
                },                                          //
                {                                           //
                    {{ 126, 228},{ 130, 232},{ 134, 236}},  //
                    {{ 132, 234},{ 136, 238},{ 140, 242}},  //
                    {{ 138, 240},{ 142, 244},{ 146, 248}}   //
                },                                          //
                {                                           //
                    {{ 132, 234},{ 136, 238},{140, 242}},   //
                    {{ 138, 240},{ 142, 244},{146, 248}},   //
                    {{ 144, 246},{ 148, 250},{152, 254}}    //
                }                                           //
            }                                               //
        };                                                 //

        std::shared_ptr<Node> myAdd_0 = Add();
        std::shared_ptr<Node> myAdd_1 = Add();
        auto op_0 = std::static_pointer_cast<OperatorTensor>(myAdd_0 -> getOperator());
        auto op_1 = std::static_pointer_cast<OperatorTensor>(myAdd_1 -> getOperator());
        op_0->associateInput(0, input_0);
        op_0->associateInput(1, input_1);

        op_1->associateInput(0, input_2);
        op_1->associateInput(1, op_0->getOutput(0));
        op_0->setDataType(DataType::Int32);
        op_1->setDataType(DataType::Int32);
        op_0->setBackend("cpu");
        op_1->setBackend("cpu");
        myAdd_0->forward();
        myAdd_1->forward();
        Log::info("Add_1 Tensor:\n{}", *(op_1->getOutput(0)));
        Log::info("Expected Add_1 Tensor:\n{}", expectedOutput);
        REQUIRE(*op_1->getOutput(0) == expectedOutput);
    }
}

TEST_CASE("[cpu/operator] Add(backward)", "[Add][CPU]") {
    std::shared_ptr<Add_Op> op = std::make_shared<Add_Op>();
    op->setDataType(DataType::Float32);
    op->setBackend("cpu");

    // NOTE: The first four tests use fixed values, the last one uses random values but static dimensions.

    SECTION("Case 1: 1D and 2D Tensors") {
        const auto T0 = std::make_shared<Tensor>(
            Array2D<cpptype_t<DataType::Float32>, 2, 3>({{{1, 2, 3}, {4, 5, 6}}}));

        const auto T1 =
            std::make_shared<Tensor>(Array1D<cpptype_t<DataType::Float32>, 3>({0.1, 0.2, 0.3}));

        op->associateInput(0, T0);
        op->associateInput(1, T1);
        op->forwardDims();

        op->getOutput(0)->setGrad(std::make_shared<Tensor>(
            Array2D<float, 2, 3>({{{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}}})));
        op->backward();

        const Tensor expectedGrad0 =
            Array2D<cpptype_t<DataType::Float32>, 2, 3>({{{1, 1, 1}, {1, 1, 1}}});

        const Tensor expectedGrad1 = Array1D<cpptype_t<DataType::Float32>, 3>({2, 2, 2});


        REQUIRE(approxEq<cpptype_t<DataType::Float32>>(*(op->getInput(0)->grad()), expectedGrad0));
        REQUIRE(approxEq<cpptype_t<DataType::Float32>>(*(op->getInput(1)->grad()), expectedGrad1));
    }

    SECTION("Case 2: 3D and 1D tensors") {
        const auto T0 = std::make_shared<Tensor>(Array3D<float, 2, 2, 3>(
            {{{{1.0, 2.0, 3.0}, {4.0, 5.0, 6.0}},
              {{7.0, 8.0, 9.0}, {10.0, 11.0, 12.0}}}}));

        const auto T1 =
            std::make_shared<Tensor>(Array1D<float, 3>({0.3, 0.2, 0.1}));

        const auto newGrad = std::make_shared<Tensor>(Array3D<float, 2, 2, 3>(
            {{{{1, 1, 1}, {1, 1, 1}}, {{1, 1, 1}, {1, 1, 1}}}}));

        const Tensor expectedGrad0 =
            Array3D<float, 2, 2, 3>({{{{1, 1, 1}, {1, 1, 1}},
                                      {{1, 1, 1}, {1, 1, 1}}}});

        const Tensor expectedGrad1 = Array1D<cpptype_t<DataType::Float32>, 3>({4, 4, 4});

        op->associateInput(0, T0);
        op->associateInput(1, T1);
        op->forwardDims();

        op->getOutput(0)->setGrad(newGrad);
        op->backward();

        REQUIRE(approxEq<cpptype_t<DataType::Float32>>(*(op->getInput(0)->grad()), expectedGrad0));
        REQUIRE(approxEq<cpptype_t<DataType::Float32>>(*(op->getInput(1)->grad()), expectedGrad1));
    }

    SECTION("Case 3: 4D and 2D tensors") {
        const auto T0 = std::make_shared<Tensor>(Array4D<cpptype_t<DataType::Float32>, 2, 2, 3, 3>(
            {{{{{1.0, 2.0, 3.0}, {4.0, 5.0, 6.0}, {7.0, 8.0, 9.0}},
               {{10.0, 11.0, 12.0}, {13.0, 14.0, 15.0}, {16.0, 17.0, 18.0}}},
              {{{19.0, 20.0, 21.0}, {22.0, 23.0, 24.0}, {25.0, 26.0, 27.0}},
               {{28.0, 29.0, 30.0},
                {31.0, 32.0, 33.0},
                {34.0, 35.0, 36.0}}}}}));

        const auto T1 = std::make_shared<Tensor>(Array2D<cpptype_t<DataType::Float32>, 3, 3>(
            {{{0.5, 0.3, 0.1}, {0.4, 0.2, 0.6}, {0.7, 0.8, 0.9}}}));

        const auto newGrad =
            std::make_shared<Tensor>(Array4D<cpptype_t<DataType::Float32>, 2, 2, 3, 3>(
                {{{{{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}},
                   {{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}}},
                  {{{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}},
                   {{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}}}}}));

        const Tensor expectedGrad0 =
            Array4D<cpptype_t<DataType::Float32>, 2, 2, 3, 3>(
                {{{{{1, 1, 1}, {1, 1, 1}, {1, 1, 1}},
                   {{1, 1, 1}, {1, 1, 1}, {1, 1, 1}}},
                  {{{1, 1, 1}, {1, 1, 1}, {1, 1, 1}},
                   {{1, 1, 1}, {1, 1, 1}, {1, 1, 1}}}}});

        const Tensor expectedGrad1 =
            Array2D<cpptype_t<DataType::Float32>, 3, 3>({{
                                   {4.0, 4.0, 4.0},
                                   {4.0, 4.0, 4.0},
                                   {4.0, 4.0, 4.0}}});

        op->associateInput(0, T0);
        op->associateInput(1, T1);
        op->forwardDims();

        op->getOutput(0)->setGrad(newGrad);
        op->backward();

        REQUIRE(approxEq<cpptype_t<DataType::Float32>>(*(op->getInput(0)->grad()), expectedGrad0));
        REQUIRE(approxEq<cpptype_t<DataType::Float32>>(*(op->getInput(1)->grad()), expectedGrad1));
    }

    SECTION("Case 4: 3D and 2D tensors") {
        const auto T0 = std::make_shared<Tensor>(
            Array3D<float, 2, 3, 4>({{{
                                          {1.0, 2.0, 3.0, 4.0},
                                          {5.0, 6.0, 7.0, 8.0},
                                          {9.0, 10.0, 11.0, 12.0},
                                      },
                                      {
                                          {13.0, 14.0, 15.0, 16.0},
                                          {17.0, 18.0, 19.0, 20.0},
                                          {21.0, 22.0, 23.0, 24.0},
                                      }}}));

        const auto T1 = std::make_shared<Tensor>(
            Array2D<cpptype_t<DataType::Float32>, 3, 4>({{{0.1, 0.2, 0.3, 0.4},
                                   {0.5, 0.6, 0.7, 0.8},
                                   {0.9, 1.0, 1.1, 1.2}}}));

        const auto newGrad = std::make_shared<Tensor>(
            Array3D<cpptype_t<DataType::Float32>, 2, 3, 4>({{{
                                          {1.0, 1.0, 1.0, 1.0},
                                          {1.0, 1.0, 1.0, 1.0},
                                          {1.0, 1.0, 1.0, 1.0},
                                      },
                                      {
                                          {1.0, 1.0, 1.0, 1.0},
                                          {1.0, 1.0, 1.0, 1.0},
                                          {1.0, 1.0, 1.0, 1.0},
                                      }}}));

        const Tensor expectedGrad0 =
            Array3D<cpptype_t<DataType::Float32>, 2, 3, 4>({{{{1, 1, 1, 1},
                                       {1, 1, 1, 1},
                                       {1, 1, 1, 1}},
                                      {{1, 1, 1, 1},
                                       {1, 1, 1, 1},
                                       {1, 1, 1, 1}}}});

        const Tensor expectedGrad1 =
            Array2D<cpptype_t<DataType::Float32>, 3, 4>({{{2.0, 2.0, 2.0, 2.0},
                                   {2.0, 2.0, 2.0, 2.0},
                                   {2.0, 2.0, 2.0, 2.0}}});

        op->associateInput(0, T0);
        op->associateInput(1, T1);
        op->forwardDims();
        op->getOutput(0)->setGrad(newGrad);

        op->backward();

        REQUIRE(approxEq<cpptype_t<DataType::Float32>>(*(op->getInput(0)->grad()), expectedGrad0));
        REQUIRE(approxEq<cpptype_t<DataType::Float32>>(*(op->getInput(1)->grad()), expectedGrad1));
    }

    SECTION("Case 5: Tensors with random values") {

        // Use random values
        const std::vector<std::size_t> dims0 = {5, 2, 1, 7}; // First tensor
        const std::vector<std::size_t> dims1 = {2, 6, 7};    // Second tensor
        const std::vector<std::size_t> outputDims = {5, 2, 6, 7};

        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dist(0.1f, 1.0f);

        auto T0 = std::make_shared<Tensor>(dims0);
        T0->setDataType(DataType::Float32);
        T0->setBackend("cpu");
        float* input0Data = static_cast<float*>(T0->getImpl()->rawPtr());
        // Fill with random values
        for (std::size_t i = 0; i < T0->size(); ++i) {
            input0Data[i] = dist(gen);
        }

        auto T1 = std::make_shared<Tensor>(dims1);
        T1->setDataType(DataType::Float32);
        T1->setBackend("cpu");
        float* input1Data = static_cast<float*>(T1->getImpl()->rawPtr());
        // Fill with random values
        for (std::size_t i = 0; i < T1->size(); ++i) {
            input1Data[i] = dist(gen);
        }

        op->associateInput(0, T0);
        op->associateInput(1, T1);

        op->forwardDims();
        op->forward();

        Tensor expectedOutput{outputDims};
        expectedOutput.setBackend("cpu");
        float* expectedOutputData = static_cast<float*>(expectedOutput.getImpl()->rawPtr());

        for (std::size_t n = 0; n < 5; ++n) {
            for (std::size_t c = 0; c < 2; ++c) {
                for (std::size_t h = 0; h < 6; ++h) {
                    for (std::size_t w = 0; w < 7; ++w) {
                        std::size_t outIdx = w + 7 * (h + 6 * (c + 2 * n));
                        std::size_t in0Idx =
                            w + 7 * (0 + 1 * (c + 2 * n)); // middle dim is 1
                        std::size_t in1Idx =
                            w + 7 * (h + 6 * c);           // no n dimension

                        expectedOutputData[outIdx] = input0Data[in0Idx] + input1Data[in1Idx];
                    }
                }
            }
        }

        auto outputTensor = op->getOutput(0);

        REQUIRE(approxEq<float>(*outputTensor, expectedOutput));

        // Backward pass
        std::vector<float> gradOutputData(expectedOutput.size());
        for (auto &val : gradOutputData) {
            val = dist(gen);
        }

        op->getOutput(0)->setGrad(std::make_shared<Tensor>(outputDims));
        op->getOutput(0)->grad()->getImpl()->setRawPtr(gradOutputData.data(),
                                                       expectedOutput.size());

        // Compute reference gradients
        std::vector<float> expectedGrad0(T0->size(), 0.0f);
        std::vector<float> expectedGrad1(T1->size(), 0.0f);

        for (std::size_t n = 0; n < 5; ++n) {
            for (std::size_t c = 0; c < 2; ++c) {
                for (std::size_t h = 0; h < 6; ++h) {
                    for (std::size_t w = 0; w < 7; ++w) {
                        std::size_t outIdx = w + 7 * (h + 6 * (c + 2 * n));
                        std::size_t in0Idx = w + 7 * (0 + 1 * (c + 2 * n));
                        std::size_t in1Idx = w + 7 * (h + 6 * c);

                        // Gradient for input0: just accumulate grad_output
                        expectedGrad0[in0Idx] += gradOutputData[outIdx];

                        // Gradient for input1: just accumulate grad_output
                        expectedGrad1[in1Idx] += gradOutputData[outIdx];
                    }
                }
            }
        }

        // Perform backward pass
        op->backward();

        auto expectedGrad0Tensor = std::make_shared<Tensor>();
        expectedGrad0Tensor->resize(T0->dims());
        expectedGrad0Tensor->setBackend("cpu");
        expectedGrad0Tensor->setDataType(DataType::Float32);
        expectedGrad0Tensor->getImpl()->setRawPtr(expectedGrad0.data(),
                                                    expectedGrad0.size());

        auto expectedGrad1Tensor = std::make_shared<Tensor>(T1->dims());
        expectedGrad1Tensor->setBackend("cpu");
        expectedGrad1Tensor->setDataType(DataType::Float32);
        expectedGrad1Tensor->getImpl()->setRawPtr(expectedGrad1.data(),
                                                    expectedGrad1.size());

        // Verify backward pass
        REQUIRE(approxEq<float>(*T0->grad(), *expectedGrad0Tensor));
        REQUIRE(approxEq<float>(*T1->grad(), *expectedGrad1Tensor));
    }
}

