/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <chrono>      // std::micro, std::chrono::time_point,
                       // std::chrono::system_clock
#include <cstddef>     // std::size_t
#include <cstdint>     // std::uint16_t
#include <functional>  // std::multiplies
#include <memory>
#include <numeric>     // std::accumulate
#include <random>      // std::random_device, std::mt19937
                       // std::uniform_int_distribution, std::uniform_real_distribution
#include <vector>

#include <catch2/catch_test_macros.hpp>
#include <fmt/core.h>

#include "aidge/backend/cpu/data/TensorImpl.hpp"
#include "aidge/backend/cpu/operator/SubImpl.hpp"
#include "aidge/data/Data.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/operator/Sub.hpp"
#include "aidge/operator/OperatorTensor.hpp"
#include "aidge/utils/TensorUtils.hpp"

namespace Aidge {

TEST_CASE("[cpu/operator] Sub", "[Sub][CPU]") {
    constexpr std::uint16_t NBTRIALS = 10;
    // Create a random number generator
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> valueDist(0.1f, 1.1f); // Random float distribution between 0 and 1
    std::uniform_int_distribution<std::size_t> dimSizeDist(std::size_t(2), std::size_t(10));
    std::uniform_int_distribution<std::size_t> nbDimsDist(std::size_t(1), std::size_t(5));
    std::uniform_int_distribution<int> boolDist(0,1);

    // Create MatMul Operator
    std::shared_ptr<Node> mySub = Sub();
    auto op = std::static_pointer_cast<OperatorTensor>(mySub-> getOperator());
    op->setDataType(DataType::Float32);
    op->setBackend("cpu");

    // Create 2 input Tensors
    std::shared_ptr<Tensor> T0 = std::make_shared<Tensor>();
    op->associateInput(0,T0);
    T0->setDataType(DataType::Float32);
    T0->setBackend("cpu");
    std::shared_ptr<Tensor> T1 = std::make_shared<Tensor>();
    op -> associateInput(1,T1);
    T1->setDataType(DataType::Float32);
    T1->setBackend("cpu");

    // Create results Tensor
    std::shared_ptr<Tensor> Tres = std::make_shared<Tensor>();
    Tres->setDataType(DataType::Float32);
    Tres->setBackend("cpu");

    // To measure execution time of 'MatMul_Op::forward()' member function call
    std::chrono::time_point<std::chrono::system_clock> start;
    std::chrono::time_point<std::chrono::system_clock> end;
    std::chrono::duration<double, std::micro> duration{};

    SECTION("SubImpl_cpu::forward()") {
        SECTION("Scalar / Scalar") {

        }
        SECTION("Scalar / +1-D Tensor") {

        }
        SECTION("+1-D Tensor / +1-D Tensor - same dimensions") {
            std::size_t number_of_operation = 0;

            for (std::uint16_t trial = 0; trial < NBTRIALS; ++trial) {
                // generate 2 random Tensors
                const std::size_t nbDims = nbDimsDist(gen);
                std::vector<std::size_t> dims;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    dims.push_back(dimSizeDist(gen));
                }
                const std::size_t nb_elements = std::accumulate(dims.cbegin(), dims.cend(), std::size_t(1), std::multiplies<std::size_t>());
                number_of_operation += nb_elements;

                // without broadcasting
                float* array0 = new float[nb_elements];
                float* array1 = new float[nb_elements];
                float* result = new float[nb_elements];

                for (std::size_t i = 0; i < nb_elements; ++i) {
                    array0[i] = valueDist(gen);
                    array1[i] = valueDist(gen);
                    result[i] = array0[i] - array1[i];
                }

                // input0
                T0->resize(dims);
                T0 -> getImpl() -> setRawPtr(array0, nb_elements);

                // input1
                T1->resize(dims);
                T1 -> getImpl() -> setRawPtr(array1, nb_elements);

                // results
                Tres->resize(dims);
                Tres -> getImpl() -> setRawPtr(result, nb_elements);

                op->forwardDims();
                start = std::chrono::system_clock::now();
                mySub->forward();
                end = std::chrono::system_clock::now();
                duration += std::chrono::duration_cast<std::chrono::microseconds>(end - start);

                REQUIRE(approxEq<float>(*(op->getOutput(0)), *Tres));

                delete[] array0;
                delete[] array1;
                delete[] result;

                // with broadcasting
            }
            Log::info("number of elements over time spent: {}\n", (number_of_operation / duration.count()));
            Log::info("total time: {}μs\n", duration.count());
        }

        SECTION("+1-D Tensor / +1-D Tensor - broadcasting") {
            std::size_t number_of_operation = 0;

            for (std::uint16_t trial = 0; trial < NBTRIALS; ++trial) {
                // generate 2 random Tensors
                // handle dimensions, replace some dimensions with '1' to get broadcasting
                constexpr std::size_t nbDims = 4;
                std::vector<std::size_t> dims;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    dims.push_back(dimSizeDist(gen));
                }
                std::vector<std::size_t> dims0 = dims;
                std::vector<std::size_t> dims1 = dims;
                std::vector<std::size_t> dimsOut = dims;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    if (boolDist(gen)) {
                        dims0[i] = 1;
                    }
                    if (boolDist(gen)) {
                        dims1[i] = 1;
                    }
                    dimsOut[i] = (dims0[i] == 1) ? dims1[i] : dims0[i];
                }

                // create arrays and fill them with random values
                float* array0 = new float[dims0[0]*dims0[1]*dims0[2]*dims0[3]];
                float* array1 = new float[dims1[0]*dims1[1]*dims1[2]*dims1[3]];
                float* result = new float[dimsOut[0]*dimsOut[1]*dimsOut[2]*dimsOut[3]];

                for (std::size_t i = 0; i < dims0[0]*dims0[1]*dims0[2]*dims0[3]; ++i) {
                    array0[i] = valueDist(gen);
                }
                for (std::size_t i = 0; i < dims1[0]*dims1[1]*dims1[2]*dims1[3]; ++i) {
                    array1[i] = valueDist(gen);
                }

                // compute true result
                const std::size_t strides0[nbDims] = {dims0[1]*dims0[2]*dims0[3], dims0[2]*dims0[3], dims0[3], 1};
                const std::size_t strides1[nbDims] = {dims1[1]*dims1[2]*dims1[3], dims1[2]*dims1[3], dims1[3], 1};
                for (std::size_t a = 0; a < dimsOut[0]; ++a) {
                    for (std::size_t b = 0; b < dimsOut[1]; ++b) {
                        const std::size_t idx0_0 = strides0[0] * ((dims0[0] > 1) ? a : 0)
                                                    + strides0[1] * ((dims0[1] > 1) ? b : 0);
                        const std::size_t idx1_0 = strides1[0] * ((dims1[0] > 1) ? a : 0)
                                                    + strides1[1] * ((dims1[1] > 1) ? b : 0);
                        for (std::size_t c = 0; c < dimsOut[2]; ++c) {
                            const std::size_t idx_out = dimsOut[3] * (c + dimsOut[2] * (b + dimsOut[1] * a));
                            for (std::size_t d = 0; d < dimsOut[3]; ++d) {
                                std::size_t idx0 = idx0_0
                                                    + strides0[2] * ((dims0[2] > 1) ? c : 0)
                                                    + ((dims0[3] > 1) ? d : 0);
                                std::size_t idx1 = idx1_0
                                                    + strides1[2] * ((dims1[2] > 1) ? c : 0)
                                                    + ((dims1[3] > 1) ? d : 0);
                                result[idx_out + d] = array0[idx0] - array1[idx1];
                                // std::cout << "(" << idx0 << ", " << idx1 << ") -> " << array0[idx0] << " - " << array1[idx1] << " -> " << idx_out + d << std::endl;
                            }
                        }
                    }
                }

                // conversion to Aidge::Tensors
                // input0
                T0->resize(dims0);
                T0 -> getImpl() -> setRawPtr(array0, dims0[0]*dims0[1]*dims0[2]*dims0[3]);

                // input1
                T1->resize(dims1);
                T1 -> getImpl() -> setRawPtr(array1, dims1[0]*dims1[1]*dims1[2]*dims1[3]);

                // results
                Tres->resize(dimsOut);
                Tres -> getImpl() -> setRawPtr(result, dimsOut[0]*dimsOut[1]*dimsOut[2]*dimsOut[3]);

                // compute result
                op->forwardDims();
                start = std::chrono::system_clock::now();
                mySub->forward();
                end = std::chrono::system_clock::now();
                duration += std::chrono::duration_cast<std::chrono::microseconds>(end - start);

                // comparison between truth and computed result
                REQUIRE(approxEq<float>(*(op->getOutput(0)), *Tres));

                delete[] array0;
                delete[] array1;
                delete[] result;

                const std::size_t nb_elements = std::accumulate(dimsOut.cbegin(), dimsOut.cend(), std::size_t(1), std::multiplies<std::size_t>());
                number_of_operation += nb_elements;
            }
            Log::info("number of elements over time spent: {}\n", (number_of_operation / duration.count()));
            Log::info("total time: {}μs\n", duration.count());
        }
        SECTION("+1-D Tensor / 1-D Tensor") {
            std::size_t number_of_operation = 0;
            std::uniform_int_distribution<std::size_t> nbRemovedDimsDist(std::size_t(1), std::size_t(3));

            for (std::uint16_t trial = 0; trial < NBTRIALS; ++trial) {
                // generate 2 random Tensors
                // handle dimensions
                constexpr std::size_t nbDims = 4;
                std::vector<std::size_t> dims0(4);
                for (std::size_t i = 0; i < nbDims; ++i) {
                    dims0[i] = dimSizeDist(gen);
                }
                std::vector<std::size_t> dimsOut = dims0;
                std::vector<std::size_t> dims1 = dims0;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    if (boolDist(gen)) {
                        dims1[i] = 1;
                    }
                }
                dims1.erase(dims1.cbegin(), dims1.cbegin() + nbRemovedDimsDist(gen));

                // create arrays and fill them with random values
                float* array0 = new float[dims0[0]*dims0[1]*dims0[2]*dims0[3]];
                std::size_t array1_size = std::accumulate(dims1.cbegin(), dims1.cend(), std::size_t(1), std::multiplies<std::size_t>());
                float* array1 = new float[array1_size];
                float* result = new float[dimsOut[0]*dimsOut[1]*dimsOut[2]*dimsOut[3]];

                for (std::size_t i = 0; i < (dims0[0]*dims0[1]*dims0[2]*dims0[3]); ++i) {
                    array0[i] = valueDist(gen);
                }
                for (std::size_t i = 0; i < array1_size; ++i) {
                    array1[i] = valueDist(gen);
                }

                // compute true result
                auto dims1_tmp = dims1;
                dims1_tmp.insert(dims1_tmp.cbegin(), 4 - dims1_tmp.size(), std::size_t(1));

                const std::size_t strides0[nbDims] = {dims0[1]*dims0[2]*dims0[3], dims0[2]*dims0[3], dims0[3], 1};
                const std::size_t strides1[nbDims] = {dims1_tmp[1]*dims1_tmp[2]*dims1_tmp[3], dims1_tmp[2]*dims1_tmp[3], dims1_tmp[3], 1};
                for (std::size_t a = 0; a < dimsOut[0]; ++a) {
                    for (std::size_t b = 0; b < dimsOut[1]; ++b) {
                        const std::size_t idx0_0 = strides0[0] * ((dims0[0] > 1) ? a : 0)
                                                    + strides0[1] * ((dims0[1] > 1) ? b : 0);
                        const std::size_t idx1_0 = strides1[0] * ((dims1_tmp[0] > 1) ? a : 0)
                                                    + strides1[1] * ((dims1_tmp[1] > 1) ? b : 0);
                        for (std::size_t c = 0; c < dimsOut[2]; ++c) {
                            const std::size_t idx_out = dimsOut[3] * (c + dimsOut[2] * (b + dimsOut[1] * a));
                            for (std::size_t d = 0; d < dimsOut[3]; ++d) {
                                std::size_t idx0 = idx0_0
                                                    + strides0[2] * ((dims0[2] > 1) ? c : 0)
                                                    + ((dims0[3] > 1) ? d : 0);
                                std::size_t idx1 = idx1_0
                                                    + strides1[2] * ((dims1_tmp[2] > 1) ? c : 0)
                                                    + ((dims1_tmp[3] > 1) ? d : 0);
                                result[idx_out + d] = array0[idx0] - array1[idx1];
                                // std::cout << "(" << idx0 << ", " << idx1 << ") -> " << array0[idx0] << " - " << array1[idx1] << " -> " << idx_out + d << std::endl;
                            }
                        }
                    }
                }

                // conversion to Aidge::Tensors
                // input0
                T0->resize(dims0);
                T0 -> getImpl() -> setRawPtr(array0, dims0[0]*dims0[1]*dims0[2]*dims0[3]);

                // input1
                T1->resize(dims1);
                T1 -> getImpl() -> setRawPtr(array1, array1_size);

                // results
                Tres->resize(dimsOut);
                Tres -> getImpl() -> setRawPtr(result, dimsOut[0]*dimsOut[1]*dimsOut[2]*dimsOut[3]);

                // compute result
                op->forwardDims();
                start = std::chrono::system_clock::now();
                mySub->forward();
                end = std::chrono::system_clock::now();
                duration += std::chrono::duration_cast<std::chrono::microseconds>(end - start);

                // comparison between truth and computed result
                REQUIRE(approxEq<float>(*(op->getOutput(0)), *Tres));

                delete[] array0;
                delete[] array1;
                delete[] result;

                const std::size_t nb_elements = std::accumulate(dimsOut.cbegin(), dimsOut.cend(), std::size_t(1), std::multiplies<std::size_t>());
                number_of_operation += nb_elements;
            }

            Log::info("number of elements over time spent: {}\n", (number_of_operation / duration.count()));
            Log::info("total time: {}μs\n", duration.count());
        }
    }
}


TEST_CASE("[CPU/Operator] Sub(Backward)", "[Sub][CPU][Backward]") {
    std::shared_ptr<Node> mySub = Sub();
    auto op = std::static_pointer_cast<OperatorTensor>(mySub->getOperator());
    op->setDataType(DataType::Float32);
    op->setBackend("cpu");

    SECTION("Case 1: 1D and 2D Tensors") {
        const auto T0 = std::make_shared<Tensor>(
            Array2D<float, 2, 3>({{{1, 2, 3}, {4, 5, 6}}}));

        const auto T1 =
            std::make_shared<Tensor>(Array1D<float, 3>({0.1, 0.2, 0.3}));

        T0->setDataType(DataType::Float32);
        T0->setBackend("cpu");
        T1->setDataType(DataType::Float32);
        T1->setBackend("cpu");

        op->associateInput(0, T0);
        op->associateInput(1, T1);
        op->forwardDims();

        op->getOutput(0)->setGrad(std::make_shared<Tensor>(
            Array2D<float, 2, 3>({{{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}}})));
        mySub->backward();

        // For subtraction: grad_input0 = grad_output
        const auto expectedGrad0 = std::make_shared<Tensor>(
            Array2D<float, 2, 3>({{{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}}}));

        // For subtraction: grad_input1 = -grad_output (summed across broadcast dimensions)
        const auto expectedGrad1 =
            std::make_shared<Tensor>(Array1D<float, 3>({-2, -2, -2}));

        REQUIRE(approxEq<float>(*(op->getInput(0)->grad()), *expectedGrad0));
        REQUIRE(approxEq<float>(*(op->getInput(1)->grad()), *expectedGrad1));
    }

    SECTION("Case 2: 3D and 1D tensors") {
        const auto T0 = std::make_shared<Tensor>(Array3D<float, 2, 2, 3>(
            {{{{1.0, 2.0, 3.0}, {4.0, 5.0, 6.0}},
              {{7.0, 8.0, 9.0}, {10.0, 11.0, 12.0}}}}));

        const auto T1 =
            std::make_shared<Tensor>(Array1D<float, 3>({0.3, 0.2, 0.1}));

        const auto newGrad = std::make_shared<Tensor>(Array3D<float, 2, 2, 3>(
            {{{{1, 1, 1}, {1, 1, 1}}, {{1, 1, 1}, {1, 1, 1}}}}));

        const auto expectedGrad0 = std::make_shared<Tensor>(Array3D<float, 2, 2, 3>(
            {{{{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}},
              {{1.0, 1.0, 1.0}, {1.0, 1.0, 1.0}}}}));

        const auto expectedGrad1 =
            std::make_shared<Tensor>(Array1D<float, 3>({-4.0, -4.0, -4.0}));

        for (auto T : {T0, T1, newGrad, expectedGrad0, expectedGrad1}) {
            T->setBackend("cpu");
            T->setDataType(DataType::Float32);
        }

        op->associateInput(0, T0);
        op->associateInput(1, T1);
        op->forwardDims();

        op->getOutput(0)->setGrad(newGrad);
        mySub->backward();

        REQUIRE(approxEq<float>(*(op->getInput(0)->grad()), *expectedGrad0));
        REQUIRE(approxEq<float>(*(op->getInput(1)->grad()), *expectedGrad1));
    }

    SECTION("Case 3: Random values with broadcasting") {
        // Use random values
        std::vector<std::size_t> dims0 = {5, 2, 1, 7}; // First tensor
        std::vector<std::size_t> dims1 = {2, 6, 7};    // Second tensor
        std::vector<std::size_t> outputDims = {5, 2, 6, 7};

        const auto input0Size = 5 * 2 * 1 * 7;
        const auto input1Size = 2 * 6 * 7;
        const auto outputSize = 5 * 2 * 6 * 7;

        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dist(0.1f, 1.0f);

        std::vector<float> input0Data(input0Size);
        std::vector<float> input1Data(input1Size);
        std::vector<float> gradOutputData(outputSize);

        // Fill with random values
        for (auto &val : input0Data) val = dist(gen);
        for (auto &val : input1Data) val = dist(gen);
        for (auto &val : gradOutputData) val = dist(gen);

        auto T0 = std::make_shared<Tensor>();
        auto T1 = std::make_shared<Tensor>();

        T0->setDataType(DataType::Float32);
        T0->setBackend("cpu");
        T0->resize(dims0);
        T0->getImpl()->setRawPtr(input0Data.data(), input0Size);

        T1->setDataType(DataType::Float32);
        T1->setBackend("cpu");
        T1->resize(dims1);
        T1->getImpl()->setRawPtr(input1Data.data(), input1Size);

        op->associateInput(0, T0);
        op->associateInput(1, T1);
	op->forwardDims();

        // Set gradient of output
        op->getOutput(0)->setGrad(std::make_shared<Tensor>(outputDims));
        op->getOutput(0)->grad()->getImpl()->setRawPtr(gradOutputData.data(), outputSize);

        // Compute reference gradients
        std::vector<float> expectedGrad0(input0Size, 0.0f);
        std::vector<float> expectedGrad1(input1Size, 0.0f);

        for (std::size_t n = 0; n < 5; ++n) {
            for (std::size_t c = 0; c < 2; ++c) {
                for (std::size_t h = 0; h < 6; ++h) {
                    for (std::size_t w = 0; w < 7; ++w) {
                        std::size_t outIdx = w + 7 * (h + 6 * (c + 2 * n));
                        std::size_t in0Idx = w + 7 * (0 + 1 * (c + 2 * n));
                        std::size_t in1Idx = w + 7 * (h + 6 * c);

                        // Gradient for input0: grad_output
                        expectedGrad0[in0Idx] += gradOutputData[outIdx];
                        // Gradient for input1: -grad_output
                        expectedGrad1[in1Idx] += -gradOutputData[outIdx];
                    }
                }
            }
        }

        // Perform backward pass
        mySub->backward();

        auto expectedGrad0Tensor = std::make_shared<Tensor>();
        expectedGrad0Tensor->resize(T0->dims());
        expectedGrad0Tensor->setBackend("cpu");
        expectedGrad0Tensor->setDataType(DataType::Float32);
        expectedGrad0Tensor->getImpl()->setRawPtr(expectedGrad0.data(), expectedGrad0.size());

        auto expectedGrad1Tensor = std::make_shared<Tensor>();
        expectedGrad1Tensor->resize(T1->dims());
        expectedGrad1Tensor->setBackend("cpu");
        expectedGrad1Tensor->setDataType(DataType::Float32);
        expectedGrad1Tensor->getImpl()->setRawPtr(expectedGrad1.data(), expectedGrad1.size());

        // Verify backward pass
        REQUIRE(approxEq<float>(*T0->grad(), *expectedGrad0Tensor));
        REQUIRE(approxEq<float>(*T1->grad(), *expectedGrad1Tensor));
    }
}
} // namespace Aidge
