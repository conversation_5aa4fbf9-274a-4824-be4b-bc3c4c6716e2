/********************************************************************************
 * Copyright (c) 2024 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <cstddef> // std::size_t
#include <cstdint> // std::uint16_t
#include <memory>
#include <random>  // std::random_device, std::mt19937, std::uniform_int_distribution, std::uniform_real_distribution

#include <catch2/catch_test_macros.hpp>

#include "aidge/backend/cpu/operator/AndImpl.hpp"
#include "aidge/data/Data.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/operator/And.hpp"
#include "aidge/utils/ArrayHelpers.hpp"

using namespace Aidge;

TEST_CASE("[cpu/operator] And(forward)", "[And][CPU]") {
    SECTION("ForwardDims") {
        constexpr std::uint16_t NBTRIALS = 10;
        // Create a random number generator
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<int> boolDist(0, 1); // Use 0 for false, 1 for true
        std::uniform_int_distribution<std::size_t> dimSizeDist(2, 10);
        std::uniform_int_distribution<std::size_t> nbDimsDist(1, 5);

        SECTION("Same dimensions") {
            for (std::uint16_t trial = 0; trial < NBTRIALS; ++trial) {
                DimSize_t nbDims = nbDimsDist(gen);
                std::vector<DimSize_t> dims(nbDims);
                for (std::size_t i = 0; i < nbDims; ++i) {
                    dims[i] = dimSizeDist(gen);
                }
                const std::size_t nb_elements = std::accumulate(dims.cbegin(), dims.cend(), std::size_t(1), std::multiplies<std::size_t>());
                float* array0 = new float[nb_elements];
                float* array1 = new float[nb_elements];
                for (std::size_t i = 0; i < nb_elements; ++i) {
                    array0[i] = boolDist(gen);
                    array1[i] = boolDist(gen);
                }
                std::shared_ptr<Tensor> myInput1 = std::make_shared<Tensor>(dims);
                std::shared_ptr<Tensor> myInput2 = std::make_shared<Tensor>(dims);
                myInput1->setDataType(DataType::Float32);
                myInput2->setDataType(DataType::Float32);
                myInput1->setBackend("cpu");
                myInput2->setBackend("cpu");

                myInput1 -> getImpl() -> setRawPtr(array0, nb_elements);
                myInput2 -> getImpl() -> setRawPtr(array1, nb_elements);

                std::shared_ptr<Node> myAnd = And();
                auto op = std::static_pointer_cast<OperatorTensor>(myAnd->getOperator());
                op->associateInput(0, myInput1);
                op->associateInput(1, myInput2);
                op->setDataType(DataType::Float32);
                op->setBackend("cpu");
                op->forwardDims();

                const auto outputDims = op->getOutput(0)->dims();
                REQUIRE(outputDims == dims);
                delete[] array0;
                delete[] array1;
            }
        }

        SECTION("Broadcasting") {
            for (std::uint16_t trial = 0; trial < NBTRIALS; ++trial) {
                DimSize_t nbDims = nbDimsDist(gen);
                std::vector<DimSize_t> dims1(nbDims, 1);
                std::vector<DimSize_t> dims2(nbDims, 1);
                std::vector<DimSize_t> expectedOutDims;
                for (std::size_t i = 0; i < nbDims; ++i) {
                    DimSize_t dim = dimSizeDist(gen);
                    if (boolDist(gen)) dims1[i] = dim;
                    if (boolDist(gen)) dims2[i] = dim;
                    expectedOutDims.push_back(std::max(dims1[i], dims2[i]));
                }

                const std::size_t nb_elements0 = std::accumulate(dims1.cbegin(), dims1.cend(), std::size_t(1), std::multiplies<std::size_t>());
                const std::size_t nb_elements1 = std::accumulate(dims2.cbegin(), dims2.cend(), std::size_t(1), std::multiplies<std::size_t>());
                float* array0 = new float[nb_elements0];
                float* array1 = new float[nb_elements1];
                for (std::size_t i = 0; i < nb_elements0; ++i) {
                    array0[i] = boolDist(gen);
                }
                for (std::size_t i = 0; i < nb_elements1; ++i) {
                    array1[i] = boolDist(gen);
                }

                std::shared_ptr<Tensor> myInput1 = std::make_shared<Tensor>(dims1);
                std::shared_ptr<Tensor> myInput2 = std::make_shared<Tensor>(dims2);
                myInput1->setDataType(DataType::Float32);
                myInput2->setDataType(DataType::Float32);
                myInput1->setBackend("cpu");
                myInput2->setBackend("cpu");
                myInput1 -> getImpl() -> setRawPtr(array0, nb_elements0);
                myInput2 -> getImpl() -> setRawPtr(array1, nb_elements1);


                std::shared_ptr<Node> myAnd = And();
                auto op = std::static_pointer_cast<OperatorTensor>(myAnd->getOperator());
                op->associateInput(0, myInput1);
                op->associateInput(1, myInput2);
                op->setDataType(DataType::Float32);
                op->setBackend("cpu");

                op->forwardDims();

                const auto outputDims = op->getOutput(0)->dims();
                REQUIRE(outputDims == expectedOutDims);
                delete[] array0;
                delete[] array1;
            }
        }
    }

    SECTION("Same size inputs") {
        std::shared_ptr<Tensor> input1 = std::make_shared<Tensor>(Array4D<float, 2, 2, 2, 2>{
            {
                {{{1, 0}, {0, 1}},
                {{1, 1}, {0, 0}}},
                {{{0, 1}, {1, 0}},
                {{1, 0}, {0, 1}}}}
            });
        std::shared_ptr<Tensor> input2 = std::make_shared<Tensor>(Array4D<float, 2, 2, 2, 2>{
            {
                {{{1, 1}, {0, 0}},
                {{0, 1}, {1, 1}}},
                {{{1, 1}, {0, 0}},
                {{0, 1}, {1, 0}}}}
            });
        std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float, 2, 2, 2, 2>{
            {
                {{{1, 0}, {0, 0}},
                {{0, 1}, {0, 0}}},
                {{{0, 1}, {0, 0}},
                {{0, 0}, {0, 0}}}}
            });

        std::shared_ptr<Node> myAnd = And();
        auto op = std::static_pointer_cast<OperatorTensor>(myAnd->getOperator());
        op->associateInput(0, input1);
        op->associateInput(1, input2);
        op->setBackend("cpu");
        op->setDataType(DataType::Float32);
        myAnd->forward();
        op->getOutput(0)->print();
        REQUIRE(*(op->getOutput(0)) == *expectedOutput);
    }

    SECTION("Broadcasting") {
        std::shared_ptr<Tensor> input_1 = std::make_shared<Tensor>(Array4D<float, 1, 2, 2, 2>{
            {
                {{{1, 0}, {1, 0}},
                {{1, 1}, {0, 0}}}}
            });
        std::shared_ptr<Tensor> input_2 = std::make_shared<Tensor>(Array1D<float, 2>{{1, 0}});
        std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float, 1, 2, 2, 2>{
            {
                {{{1, 0}, {1, 0}},
                {{1, 0}, {0, 0}}}}
            });

        std::shared_ptr<Node> myAnd = And();
        auto op = std::static_pointer_cast<OperatorTensor>(myAnd->getOperator());
        op->associateInput(0, input_1);
        op->associateInput(1, input_2);
        op->setDataType(DataType::Float32);
        op->setBackend("cpu");
        myAnd->forward();

        REQUIRE(*(op->getOutput(0)) == *expectedOutput);
    }
}
