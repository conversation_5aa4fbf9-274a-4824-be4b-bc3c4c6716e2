/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <aidge/utils/Types.h>
#include <memory>

#include <catch2/catch_test_macros.hpp>
#include <fmt/core.h>

#include "aidge/backend/cpu/operator/ConvImpl.hpp"
#include "aidge/data/Data.hpp" // DataType
#include "aidge/data/Tensor.hpp"
#include "aidge/filler/Filler.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/operator/Conv.hpp"
#include "aidge/operator/Pad.hpp"
#include "aidge/utils/TensorUtils.hpp"

namespace Aidge {

template <DimSize_t DIM>
static std::shared_ptr<OperatorTensor>
setupTestConv(const DimSize_t batchSize,
              const DimSize_t inChannels,
              const DimSize_t outChannels,
              const std::array<DimSize_t, DIM> kernelSize,
              const std::array<DimSize_t, DIM> dataSize,
              const std::array<DimSize_t, DIM> stride,
              const std::array<DimSize_t, DIM> dilation,
              const std::array<DimSize_t, 2 * DIM> padding,
              const std::shared_ptr<Tensor> input,
              const std::shared_ptr<Tensor> weights,
              const std::shared_ptr<Tensor> biases) {
    input->setBackend("cpu");
    weights->setBackend("cpu");
    biases->setBackend("cpu");
    std::shared_ptr<Node> convNode;
    convNode = Conv(inChannels,
                    outChannels,
                    kernelSize,
                    "myconv",
                    std::array<DimSize_t, DIM>({stride}),
                    dilation);
    auto op =
        std::static_pointer_cast<OperatorTensor>(convNode->getOperator());

    op->setDataType(DataType::Float32);
    op->setBackend("cpu");

    op->associateInput(0, input);
    op->associateInput(1, weights);
    op->associateInput(2, biases);

    REQUIRE_NOTHROW(op->forwardDims(true));

    return op;
}

/**
 * @brief ConvDepthWise reference cpp backend forward implmentation tests.
 *
 * Summary
 * =======
 * kernel [3, 3]
 *  no stride, no dilation
 *  stride [2,2], no dilation
 *  stride [2,2], dilation [2,2]
 * kernel [1,1]
 *  no stride, no dilation
 *  stride [3,3], no dilation
 *  stride [3,3], dilation [2,2]
 * kernel [5,5]
 *  no stride, no dilation
 *  stride [2,2], no dilation
 *  stride [2,2], dilation [2,2]
 */
TEST_CASE("[cpu/operator] Conv(forward)", "[Conv][CPU]") {
    SECTION("2D") {
    SECTION("Conv with kernel [3,3]") {
        SECTION("No stride, no dilation") {
            std::shared_ptr<Node> myConv = Conv(3,4,{3,3}, "myconv");
            auto op = std::static_pointer_cast<OperatorTensor>(myConv -> getOperator());
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,3,5,5> {
                {{{{ 1.9589154720, -1.0110363960, -1.3467419147, -1.2994621992,
                        0.1868611127},
                    {-2.1160471439,  2.6068224907, -0.3956520855,  0.6124371886,
                        0.5558118224},
                    {-1.3752416372, -0.5381416678,  1.3515229225,  0.3483452201,
                        1.2234963179},
                    { 0.0059552360, -0.2397697568,  0.2965213358,  2.8708021641,
                    -0.1894584149},
                    {-1.1417659521, -0.7091106772,  0.4265739620,  1.2625461817,
                        1.3426892757}},

                    {{-1.7054084539, -0.0123512046,  1.6428576708, -0.6893027425,
                    -0.7225475907},
                    { 0.5119231343, -0.6280173063, -1.1115980148,  0.4660657048,
                    -0.7594850659},
                    { 0.3270559013, -0.5950503945, -0.5041811466,  1.5374211073,
                    -0.1167122573},
                    { 0.1087838784,  0.5390511155,  1.2179180384,  0.3980854154,
                    -0.7828828692},
                    { 0.0556977428,  0.7218912244,  1.9021573067, -1.0387030840,
                    -0.8821150064}},

                    {{ 1.4301366806,  1.6713913679,  2.7843642235,  2.1220099926,
                    -0.1225114316},
                    { 0.9676079750,  0.0927167758,  1.3160738945, -1.1043174267,
                        1.1129677296},
                    { 1.7959889174,  0.8373055458,  0.9646789432, -1.0112720728,
                    -0.5838463902},
                    { 2.2678804398,  1.4151918888,  1.3015384674,  1.0426887274,
                        0.5917658210},
                    {-0.4324578047, -0.8626666665,  1.4560189247,  0.4216258824,
                        0.4797532558}}},


                    {{{ 0.7857398987,  1.0575772524,  1.4281150103,  0.5534361601,
                    -0.7034347653},
                    {-0.6367500424,  0.0736645982, -0.1755308807, -0.0363982692,
                    -0.2698654532},
                    {-0.1483689547, -1.4097578526,  3.0468130112, -0.9070094824,
                    -0.0465935729},
                    {-0.4035107195,  0.3865649998,  0.5000861287,  0.0409870148,
                    -0.2879518867},
                    {-0.3219492733, -0.8549274206,  0.6380167007,  1.0422019958,
                        0.6655231714}},

                    {{ 1.8096567392,  0.8781581521,  0.8389310837, -0.5663586259,
                    -0.3415665030},
                    { 0.6761546135, -1.8892842531, -0.4562507868, -1.3220169544,
                    -0.0600548759},
                    {-2.3044283390, -0.6273749471, -0.4794641733,  0.3725788891,
                    -0.0789731145},
                    {-0.0977325812, -0.9537382126,  1.6169245243, -1.5318340063,
                    -0.0146348709},
                    { 2.8766520023, -1.4148812294, -1.6623396873, -0.1664140671,
                    -0.4492034912}},

                    {{-0.1821998209,  0.2622891963, -0.1877782643, -1.6476312876,
                    -0.6388390660},
                    { 0.3169876039, -0.8038316965, -0.0962172970,  0.9118794799,
                    -0.6303430200},
                    {-1.1592572927, -0.7246652842,  2.0476946831, -0.0111423340,
                        0.1810427308},
                    {-0.9517292976,  0.8139786720,  0.2079211175,  1.1996579170,
                    -2.9504573345},
                    {-0.0734997243, -0.1853470206, -0.7156494260,  0.0105203446,
                        0.1248303726}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {{-0.0953646377,  0.0252329484,  0.0736814514, -0.1786542684}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,3,3,3> {
                {{{{-9.8095126450e-02, -1.2300920486e-01,  9.8884001374e-02},
                    { 9.1700568795e-02, -1.4668887854e-01,  1.8008193374e-01},
                    { 1.5175457299e-01, -1.7447699606e-01,  1.4544752240e-01}},

                    {{ 1.0927890241e-01,  1.4000165276e-02, -6.3778474927e-02},
                    {-6.1189714819e-02, -1.1776087433e-01, -1.3381159306e-01},
                    {-1.0581787676e-01, -2.2982399911e-02, -1.7992550135e-01}},

                    {{ 1.6829776764e-01, -1.4381179214e-01,  6.8733291700e-03},
                    {-1.2539045885e-02,  5.9776338749e-03, -1.6262140870e-01},
                    {-1.8150532246e-01, -5.7762067765e-02, -1.2558361888e-01}}},


                    {{{-1.9167131186e-01, -2.0615238696e-02, -1.4749580622e-01},
                    { 2.8844498098e-02, -1.4776159823e-01, -1.4355856180e-01},
                    { 8.4779271856e-04,  6.2282480299e-02, -4.7553293407e-02}},

                    {{ 1.3088253140e-01,  1.5134441853e-01, -2.6965653524e-02},
                    { 1.0772592388e-02,  1.2854418159e-01,  1.0366836190e-01},
                    {-1.1401490122e-01,  7.7274993062e-02,  3.0564883724e-02}},

                    {{ 5.1155414432e-02, -9.1598711908e-02, -3.4925807267e-02},
                    {-4.7612894326e-02, -1.3718418777e-01, -1.6633707285e-01},
                    {-1.0674133152e-01, -1.2472828478e-01, -2.7257451788e-02}}},


                    {{{-1.6760022938e-01,  7.3507070541e-02, -7.9185843468e-02},
                    {-1.4954875410e-01, -1.2724696100e-01, -1.7345303297e-01},
                    {-6.1098476872e-03, -1.0876300931e-01,  5.5467881262e-02}},

                    {{ 1.2978881598e-01,  1.3939674199e-01, -1.1531168967e-01},
                    {-2.2687437013e-02, -1.7840284854e-02, -1.6743370891e-01},
                    { 1.0837453604e-01,  1.8985052407e-01, -1.0379110277e-01}},

                    {{ 6.6287182271e-02,  6.2025051564e-02,  1.2377030216e-02},
                    { 3.9242565632e-02, -6.0004377738e-03, -6.8803697824e-02},
                    { 4.1780071333e-03,  2.6938719675e-02,  7.6389208436e-02}}},


                    {{{ 1.1024503410e-01, -2.0850643516e-02, -1.8233209848e-01},
                    { 6.6961102188e-02, -4.2337212712e-02,  7.4952361174e-03},
                    {-9.7807966173e-02, -1.1996924877e-01, -1.0953181982e-01}},

                    {{-5.0487142056e-02,  1.6390360892e-01,  8.4205769002e-02},
                    {-5.8809131384e-02,  1.2781466544e-01, -9.8992012441e-02},
                    { 8.6972511781e-05,  1.8518652767e-02,  2.3319255561e-02}},

                    {{ 1.3843496144e-01,  1.2726350129e-01,  6.0541676357e-03},
                    {-1.7569662631e-01, -3.2354578376e-02, -2.8981804848e-02},
                    { 1.2195236981e-01, -1.5436136723e-01,  1.9030691683e-01}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,2,4,3,3> {
                {{{{-1.5940358639,  0.1530998051,  0.7961921692},
                    {-0.6456220150, -0.9623295069, -0.3714832962},
                    {-0.2949652672, -0.2190868855, -0.7510035634}},

                    {{-1.9549642801,  0.3644225001,  0.2327819020},
                    {-0.6244611144, -1.4931452274,  0.0343996882},
                    {-0.1242256761, -0.9374671578, -1.3280395269}},

                    {{ 0.0045107063, -0.0462101400,  0.9180620313},
                    { 1.1481817961, -0.4496926665, -0.1462283134},
                    { 0.2508939803,  0.0321760587, -0.2549723089}},

                    {{ 0.7890433073,  0.4426230788, -0.1316385418},
                    {-0.5980579257, -0.4781580269, -0.2697018385},
                    {-0.3229375780, -0.6225202084, -0.4930999875}}},


                    {{{ 1.0800328255, -0.7354405522,  0.4513073266},
                    { 0.6686266065, -0.9933773279,  0.1854345798},
                    { 0.3025504351, -0.8967062235,  0.9597628117}},

                    {{-0.0881052017, -0.1263627559, -0.3096815050},
                    {-0.5136051774, -0.8888333440, -0.4660321772},
                    {-1.5337569714, -0.1932583302,  0.0154455183}},

                    {{ 0.5402041674, -0.0840467885,  0.1597940624},
                    {-0.6559837461,  0.1566532403, -0.6488818526},
                    {-0.7716163993,  0.0849530324, -0.4354790747}},

                    {{-0.3671937585, -0.3662823737, -0.3439203203},
                    {-0.5602309704, -0.4117053151, -1.0589636564},
                    {-1.2864066362,  0.0953547135, -0.0424274690}}}}
            });

            op->associateInput(0,myInput);
            op->associateInput(1,myWeights);
            op->associateInput(2,myBiases);
            op->setDataType(DataType::Float32);
            op->setBackend("cpu");
            op->forwardDims();
            myConv->forward();
            REQUIRE(approxEq<float>(*(op->getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("Stride [2,2] | no dilation") {
            fmt::print("Stride conv\n");
            std::shared_ptr<Node> myConv = Conv(2,1,{3,3}, "myconv", {2,2});
            auto op = std::static_pointer_cast<OperatorTensor>(myConv -> getOperator());
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,1,2,5,5> {
                {
                    {
                        {{-0.9172891974, -0.4144149423, -0.0127728982, -0.6073911190, -0.0152466390},
                        { 0.4086987972,  0.5984987617, -0.6368257999, -0.0744020939,0.9958203435},
                        { 0.5346475244, -0.0788366571, -1.4571775198, -0.5634615421, 1.9504889250},
                        { 1.1559145451,  0.4456179738,  1.5754781961,  0.0340409055, -0.2864624560},
                        {-0.2880946100, -1.1225816011,  0.6820554733, -1.6727229357, 0.5375806093}},

                        {{ 1.4201650620, -1.7509239912, -1.0208708048, -2.2132670879, 0.1117813289},
                        {-0.2961948812, -0.6673586369, -0.0750549659, -0.6074910164, 2.7782683372},
                        {-0.5388702750, -2.9463961124,  2.1617200375, -0.5921722054, 0.5093105435},
                        { 0.3627473414,  0.6647079587,  0.9116655588, -1.1410249472, -1.2326570749},
                        { 2.0130438805,  0.2274843603, -0.3941729367,  1.5164465904, -1.4629846811}}
                    }
                }
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,1> {{0.0510118753}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,1,2,3,3> { //NCHW
                {
                    {
                        {{-0.0313824862,  0.0508503988,  0.0164926797},
                        { 0.1006948650, -0.1172138453, -0.1695717275},
                        { 0.0428596064, -0.0209989939,  0.0409581661}},

                        {{ 0.1578378379,  0.1206310838, -0.0435518846},
                        {-0.1232439354, -0.0746039376, -0.1832553446},
                        {-0.2099054456, -0.0485608950, -0.0263982005}}
                    }
                }
            });
            std::shared_ptr<Tensor> myOutput = std::make_shared<Tensor>(Array4D<float,1,1,2,2> {
                    {{{{ 0.4589637220, -1.5007210970},
                    {-1.3768237829,  0.8838264346}}}}
                });
            op->associateInput(0,myInput);
            op->associateInput(1,myWeights);
            op->associateInput(2,myBiases);
            op->setDataType(DataType::Float32);
            op->setBackend("cpu");
            op->forwardDims();
            myConv->forward();
            REQUIRE(approxEq<float>(*(op->getOutput(0)),*myOutput, 1e-5f, 1e-8f));
        }
        SECTION("Stride [2,2] | dilation [2,2]") {
            std::shared_ptr<Node> myConv = Conv(3,4,{3,3}, "myconv", {2,2},{2,2});
            auto op = std::static_pointer_cast<OperatorTensor>(myConv -> getOperator());
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,3,8,8> {
                {{{{-0.1188705787,  0.2816951275, -0.2984274328, -1.1976412535,
                0.8114287257,  0.4823331535, -1.0786532164,  0.9851297736},
            { 0.0904922038, -1.1438461542, -0.0669364706,  0.3452534974,
                0.7429151535,  0.2972919941,  1.0456926823,  1.4523943663},
            {-0.3802470863, -0.6462681890, -0.0720853731, -0.2438412607,
            -0.4248653948,  1.1631586552,  0.1168384328, -0.6349191666},
            {-0.6761771441,  0.4402402937,  0.1597155333,  1.9242455959,
                0.5197503567,  0.2465762347, -0.4339638352, -1.8066062927},
            {-1.9127304554,  0.6646876931, -0.4783093035, -1.3473324776,
                0.0341839045, -2.2884213924, -0.1275558323, -1.7231218815},
            {-0.2966701984, -1.3017544746, -1.1662167311,  0.1658521742,
            -0.6164554954,  0.0081211235, -1.0151772499, -1.5334042311},
            { 0.7240908146, -0.3402064443,  0.6997579932, -2.0220518112,
            -0.3068118095, -1.0308376551, -0.2388850898, -0.7060801387},
            { 0.1785067171,  1.7402675152, -2.3868498802, -1.1331337690,
            -0.4219121039,  1.8711329699, -0.6606232524, -1.6592081785}},

            {{ 0.6190823913,  0.8092262745, -0.7060685158,  0.0304337163,
                0.4091666043,  0.0813116133,  0.4840675890,  0.2641656399},
            { 0.2542354465,  0.2307302207,  0.6869923472,  0.2362013012,
            -1.1283814907,  0.8362480402,  0.8266768456,  0.0802312344},
            {-0.3555500209, -2.2554228306, -1.5869985819,  0.3333877325,
            -0.4702588022, -0.6803518534,  0.6176669002,  0.4762512147},
            { 1.1077108383,  1.1330136061,  1.8400819302, -1.0991362333,
                2.1611855030,  0.1117769480, -0.6205040216, -0.2117952108},
            {-1.5193798542, -0.1550827324, -0.5265267491, -1.2790243626,
                0.3303467929,  1.0218718052,  1.1192175150, -0.7710328102},
            {-0.0476187728,  0.4485085905, -0.1582977027,  0.0724322647,
            -1.1117008924, -0.8183123469,  0.5641981363, -1.6811249256},
            { 0.9987262487, -0.1189468578,  2.1283075809,  0.4363055527,
            -0.3778467178, -0.0662526861,  1.8629752398,  0.0138667496},
            { 0.7676854134,  0.1937866956,  1.4422281981, -1.1290216446,
                0.7080894709,  2.5929143429, -0.7571783662,  0.2852989435}},

            {{ 1.4924588203, -0.4435261190,  2.0567798615, -0.0626328290,
            -0.1295254529, -0.7903441191, -0.8875020146, -0.0774324834},
            {-0.1324225664,  1.0868368149, -1.1364834309, -1.6390762329,
                1.9040355682, -0.4707730114,  0.0161281377, -0.6975379586},
            {-0.3238699436,  0.8842161894,  1.1445614100, -1.6232908964,
            -0.8882713914, -1.8093744516, -0.1777562201,  0.5819293261},
            { 0.7295795679,  0.1291531473, -0.4221164286, -0.7543993592,
                1.7530804873,  1.2137289047,  0.1712447554,  0.7797858715},
            {-0.9555802941,  0.5811409950, -0.8670540452,  0.2635410130,
            -0.7124243975, -1.1684633493, -0.9052340388,  1.9412840605},
            { 1.9100213051, -1.0305522680,  0.5274596214, -1.2627660036,
            -0.2148997933,  0.2035689354, -1.7192043066, -0.2544563115},
            { 0.2017714083,  0.4016665220,  0.3390722871, -0.3035760522,
            -0.3663327694,  0.0600613877, -0.0339251719, -0.8913670778},
            { 1.7665598392,  0.9260235429,  0.3003851175, -0.9005023837,
                1.1928522587,  0.4629152417, -0.5554659963,  0.7210552692}}},


            {{{-1.4669480324, -1.9388267994,  1.5483425856,  1.1181882620,
            -0.3805114627,  1.1109890938, -0.2052619010,  1.1042164564},
            { 0.4673211575,  0.4196352065,  0.4227792621, -0.5219387412,
                0.0205618106,  0.1554571241,  0.0663015544, -0.9489745498},
            {-1.0466605425,  0.5461820960,  0.6410058141, -1.3620399237,
            -1.0813049078,  0.1793443412, -0.1706669927, -0.4602096379},
            {-1.7209292650, -0.2953333557,  1.7118937969, -0.4912810624,
            -0.1289002448,  0.1328577548,  0.1901275814, -0.3454665840},
            { 0.8632204533, -0.5402244329, -0.7203282118,  0.1909070015,
            -0.9598633051, -0.3567193747, -1.2241401672, -1.4285988808},
            {-0.0053109927,  0.5492724180, -0.6969500184, -0.0806153566,
                0.0108112106,  1.0541758537,  0.7616159320,  0.4281075597},
            {-0.4106684327, -0.1015827805,  0.3509808779,  0.5693497658,
            -0.1018603221,  1.4039390087, -0.0777162984,  0.6270876527},
            {-1.0135161877, -1.8348534107,  0.4114980996,  0.3335310519,
                0.1524153352,  0.3699457347,  0.7671044469, -0.4262715578}},

            {{-1.6966865063, -1.6478683949, -1.9790385962,  0.2527430952,
            -0.0905110165, -0.0635698363, -0.0573330820,  1.0314729214},
            {-0.6635334492,  0.6282949448, -1.7904577255, -0.8557172418,
            -0.0034775073, -1.8597713709,  0.0629400164,  1.8645975590},
            {-0.2417802215, -2.0907909870, -0.6610770226,  0.0370707251,
            -0.3790464699,  0.4056134224, -0.6770003438, -0.1630496234},
            { 0.5351417661,  0.5648558736,  0.0339234099, -1.0622112751,
                0.2600688934, -0.9012448788, -0.1649469733,  1.0918279886},
            { 0.1757414639, -0.9574738145, -0.1036170200,  0.4119592607,
            -0.1864566058, -0.7414899468, -1.0112596750,  0.5932180285},
            { 2.5017285347, -1.1581023932,  1.3093953133, -0.7198146582,
            -1.5908024311,  0.8121448755,  0.7034096718, -0.9230241179},
            { 0.2434841692, -0.3430828154, -0.5492738485,  1.2563036680,
            -0.7979055047, -0.5963852406, -1.2386600971,  1.0888540745},
            {-1.0111418962, -0.8539634347, -0.5791223645, -0.1067883298,
            -1.5724925995,  2.1116111279, -1.1383074522, -1.0958977938}},

            {{-1.1234122515,  0.1245573759,  1.0143580437,  1.5302449465,
                0.2031295598, -1.9043928385,  1.3508439064, -0.2800186276},
            {-0.9589865804,  0.1616169512,  0.2391400337, -1.4750261307,
                0.0099803358, -0.1171493977,  0.6740672588, -0.1370818019},
            {-0.1391109377,  0.0727060661, -0.4593637884, -0.7321376801,
                1.5460444689, -0.8100689054, -0.9888796806, -0.4120852351},
            {-0.0212404300, -0.4793040454,  0.6356511712, -0.4630482495,
                0.2108679265, -0.3083102107,  0.5054508448,  1.1919053793},
            {-0.0884858668,  3.4338581562,  0.7329123616, -2.1103246212,
                0.7225313187,  0.9016330242,  0.9377334714, -0.4367531836},
            {-0.3704574108,  1.6491469145,  0.3141648471,  0.4510190189,
                0.4318002760, -0.9045167565,  0.8301303983,  0.7684385180},
            {-0.8037292361, -0.5441461802, -0.7591288686,  0.0744654313,
                1.3689713478, -0.5011886954,  1.7228851318,  0.5603687763},
            {-0.6605531573, -0.6788222194, -0.7361486554, -0.5757233500,
                1.0730458498, -1.3874051571, -0.3474266231, -0.8265513778}}}}
            });
            std::shared_ptr<Tensor> myBias = std::make_shared<Tensor>(Array1D<float,4> {{ 0.0990660042, -0.1225081310,  0.1313948184,  0.1762123108}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,3,3,3> { //NCHW
                {
                    {
                        {{-0.0742266178, -0.1015041918, -0.1837180406},
                        { 0.1212819815,  0.0690591782, -0.1593577117},
                        {-0.0843696669, -0.1086091846, -0.0535376482}},
                        {{-0.1789309531,  0.0376559310,  0.0013079831},
                        {-0.1449880898, -0.0421767347, -0.1624120027},
                        {-0.0249556731,  0.0128599331, -0.0885131210}},
                        {{ 0.0676055402, -0.0983952284,  0.1473675370},
                        { 0.1839909703,  0.1130383387, -0.0591052435},
                        { 0.0585059784,  0.1588494480,  0.0837457627}}
                    },
                    {
                        {{-0.1567524076,  0.1625535488,  0.0182458963},
                        { 0.1850223690, -0.0358172096, -0.1628112793},
                        {-0.1376292855, -0.0344094560, -0.1884102225}},
                        {{-0.0423617847,  0.0293476824,  0.1536137760},
                        {-0.0270300750, -0.0305194370, -0.1655584276},
                        {-0.1863301992, -0.1162049547,  0.1737580448}},
                        {{ 0.0242811367, -0.1578935832,  0.0989272967},
                        { 0.1372362673, -0.0220122132,  0.1455551833},
                        {-0.1089558378, -0.0905081928,  0.0518258214}}
                    },
                    {
                        {{ 0.0139021352,  0.0587250255, -0.1449213177},
                        {-0.0330693051, -0.1812424064, -0.1139466539},
                        {-0.1164239123,  0.1715961397, -0.0794166178}},
                        {{ 0.1213000864, -0.0386510566, -0.1356311589},
                        {-0.0149245840,  0.0932215229, -0.0739067197},
                        {-0.0967126489,  0.1908948421, -0.1560722739}},
                        {{ 0.0209723040,  0.1112291217, -0.0647701770},
                        {-0.1392559260, -0.1320645362, -0.0444710776},
                        { 0.0430435687,  0.0755403191, -0.0292177629}}
                    },
                    {
                        {{-0.0096048070, -0.1168650612,  0.0795118213},
                        { 0.1292631775,  0.1187104806,  0.0055516954},
                        {-0.0513968319,  0.0119201895, -0.1323033124}},
                        {{-0.0103231622, -0.0167140234, -0.1640086174},
                        { 0.0006717141, -0.1033149883,  0.1532802731},
                        {-0.1091904417, -0.0362586826, -0.0466702394}},
                        {{ 0.1598802209,  0.0274402276, -0.0314338058},
                        {-0.0309233274, -0.0172249842,  0.0602610074},
                        {-0.0760313869,  0.1626167148, -0.1280857325}}
                    }
                }
            });
            std::shared_ptr<Tensor> myOutput = std::make_shared<Tensor>(Array4D<float,2,4,2,2> {
                {
                    {
                        {{ 2.5577675551e-02,  3.3609107137e-01},
                        {-4.1263043880e-01, -8.6513996124e-02}},
                        {{ 3.3599328995e-01,  6.7887884378e-01},
                        {-1.6548714638e+00, -4.3978449702e-01}},
                        {{ 2.5655159354e-01,  1.6271021962e-01},
                        { 1.2917815447e+00, -6.9365233183e-01}},
                        {{ 7.6308822632e-01,  4.2617604136e-01},
                        { 9.5820712158e-04, -8.5415104404e-03}}
                    },
                    {
                        {{ 4.8839592934e-01,  1.6159484386e+00},
                        { 8.3929586411e-01,  7.7249741554e-01}},
                        {{ 4.6416598558e-01, -1.5206467360e-02},
                        { 8.9938336611e-01, -3.4680870175e-01}},
                        {{ 1.1395914108e-01,  2.1783047915e-01},
                        { 2.8127232194e-01,  5.7355374098e-01}},
                        {{ 3.7263277918e-02,  3.5707062483e-01},
                        {-1.1406441033e-01,  2.7624604106e-01}}
                    }
                }
            });
            op->associateInput(0,myInput);
            op->associateInput(1,myWeights);
            op->associateInput(2,myBias);
            op->setDataType(DataType::Float32);
            op->setBackend("cpu");
            op->forwardDims();
            myConv->forward();
            REQUIRE(approxEq<float>(*(op->getOutput(0)),*myOutput, 1e-5f, 1e-8f));
        }
    }

    SECTION("Point-wise") {
        SECTION("no stride, no dilation") {
            std::shared_ptr<Node> myConv = Conv(3,4,{1,1}, "myconv", {1,1});
            auto op = std::static_pointer_cast<OperatorTensor>(myConv -> getOperator());
            op->setInput(0, std::make_shared<Tensor>(Array4D<float,2,3,3,3> {
                {
                    {
                        {{-1.38467371F, -0.87123615F, -0.22336592F},
                        { 1.71736145F,  0.31888032F, -0.42451897F},
                        { 0.30572093F, -0.77459252F, -1.55757248F}},
                        {{ 0.99563611F, -0.87978584F, -0.60114205F},
                        {-1.27415121F,  2.12278509F, -1.23465312F},
                        {-0.48791388F, -0.91382301F, -0.65813726F}},
                        {{ 0.07802387F,  0.52580875F, -0.48799172F},
                        { 1.19136906F, -0.81400764F, -0.73599279F},
                        {-1.40324783F,  0.03600367F, -0.06347727F}}
                    },
                    {
                        {{ 0.67561489F, -0.09780689F,  1.84459400F},
                        {-1.18453741F,  1.38354933F,  1.44513381F},
                        { 0.85641253F,  2.21807575F,  0.52316552F}},
                        {{ 0.34664667F, -0.19733144F,  1.14120162F},
                        { 0.05164360F,  0.72810954F, -0.71064192F},
                        {-0.60206831F,  0.96044880F,  0.40481427F}},
                        {{-1.35434294F,  1.33470297F,  0.48353928F},
                        {-0.19756168F,  1.26831138F,  1.22426283F},
                        { 0.09811721F,  1.74225271F, -1.35267365F}}
                    }
                }
            }));
            op->setInput(1, std::make_shared<Tensor>(Array4D<float,4,3,1,1> {
                {
                    {
                        {{ 0.33669037F}},
                        {{ 0.12880941F}},
                        {{ 0.23446237F}}
                    },
                    {
                        {{ 0.23033303F}},
                        {{-1.12285638F}},
                        {{-0.18632829F}}
                    },
                    {
                        {{ 2.20820141F}},
                        {{-0.63799703F}},
                        {{ 0.46165723F}}},
                    {
                        {{ 0.26735088F}},
                        {{ 0.53490466F}},
                        {{ 0.80935723F}}
                    }
                }
            }));
            op->setInput(2, std::make_shared<Tensor>(Array1D<float,4> {{ 1.11029029F, -1.68979895F, -0.98895991F,  0.95797181F}}));
            Tensor expectedOutput = Array4D<float,2,4,3,3> {
                {
                    {
                        {{ 0.79062498F,  0.82691115F,  0.84323663F},
                        { 1.80371785F,  1.30023468F,  0.63576132F},
                        { 0.82136691F,  0.74022496F,  0.48621333F}},
                        {{-3.14122939F, -1.00057328F, -0.97532475F},
                        {-0.08553087F, -3.84826040F, -0.26410526F},
                        {-0.81005937F, -0.84882969F, -1.29773819F}},
                        {{-4.64579105F, -2.10878062F, -1.32395494F},
                        { 4.16622877F, -2.01493120F, -1.47845459F},
                        {-0.65039843F, -2.09977841F, -4.03780890F}},
                        {{ 1.18349767F,  0.68001163F,  0.18174142F},
                        { 1.69980371F,  1.51988935F, -0.41162649F},
                        {-0.35700959F,  0.29121545F,  0.13813695F}}
                    },
                    {
                        {{ 1.06487226F,  1.36487913F,  1.99171650F},
                        { 0.67179936F,  1.96727657F,  1.79235911F},
                        { 1.34408879F,  2.38930249F,  1.02142799F}},
                        {{-1.67106462F, -1.73944509F, -2.63643050F},
                        {-1.98381400F, -2.42500663F, -0.78710288F},
                        {-0.83478457F, -2.58197999F, -1.77180362F}},
                        {{-0.34346789F, -0.46286502F,  2.57942152F},
                        {-3.72881150F,  2.18718910F,  3.22076392F},
                        { 1.33158576F,  4.10055828F, -0.71644694F}},
                        {{ 0.22787374F,  1.90652108F,  2.45291567F},
                        { 0.50901115F,  2.74385118F,  1.95506990F},
                        { 0.94429719F,  3.47482967F,  0.21958135F}}
                    }
                }
            };
            op->setDataType(DataType::Float32);
            op->setBackend("cpu");
            myConv->forward();
            REQUIRE(approxEq<float>(*(op->getOutput(0)),expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [3,3], no dilation") {
            std::shared_ptr<Node> myConv = Conv(3,4,{1,1}, "myconv", {3,3});
            auto op = std::static_pointer_cast<OperatorTensor>(myConv -> getOperator());
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,1,3,3,3> {
                {{{{ 0.5328165889, -0.4486202002, -0.4963828325},
                    { 0.2804954648,  0.4619753063, -0.9826803803},
                    { 0.4261451066,  0.5110000372,  1.9890428782}},

                    {{ 1.1952217817, -1.5133171082,  0.1646732241},
                    {-0.7254997492, -0.1677423269, -1.0745935440},
                    { 0.2478682548, -2.3416306973,  0.5321671963}},

                    {{ 0.7880555391,  2.6202778816, -1.0282965899},
                    {-0.5344914198, -0.5824835896,  0.4730331898},
                    {-1.3073508739,  0.2466813326, -0.3054754436}}}}
            });
            std::shared_ptr<Tensor> myBias = std::make_shared<Tensor>(Array1D<float,4> {{0.3835324347, 0.5210654140, 0.0670478195, 0.2723239064}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,3,1,1> { //NCHW
                {{{{ 0.3910248578}},
                    {{ 0.4545786083}},
                    {{-0.1685599536}}},

                    {{{-0.2521645725}},
                    {{ 0.1072919592}},
                    {{ 0.1053370386}}},

                    {{{ 0.4627405703}},
                    {{-0.4509093165}},
                    {{ 0.3544224203}}},

                    {{{ 0.4944877923}},
                    {{ 0.1518175900}},
                    {{ 0.1704865843}}}}
            });
            std::shared_ptr<Tensor> myOutput = std::make_shared<Tensor>(Array4D<float,1,4,1,1> {
                {{{{1.0023646355}},
                    {{0.5979570746}},
                    {{0.0539716333}},
                    {{0.8516038060}}}}
            });
            op->associateInput(0,myInput);
            op->associateInput(1,myWeights);
            op->associateInput(2,myBias);
            op->setDataType(DataType::Float32);
            op->setBackend("cpu");
            op->forwardDims();
            myConv->forward();
            REQUIRE(approxEq<float>(*(op->getOutput(0)),*myOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [3,3], dilation [2,2]") { // same as no dilation test
            std::shared_ptr<Node> myConv = Conv(3,4,{1,1}, "myconv", {3,3}, {2,2});
            auto op = std::static_pointer_cast<OperatorTensor>(myConv -> getOperator());
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,1,3,3,3> {
                {{{{ 0.5328165889, -0.4486202002, -0.4963828325},
                    { 0.2804954648,  0.4619753063, -0.9826803803},
                    { 0.4261451066,  0.5110000372,  1.9890428782}},

                    {{ 1.1952217817, -1.5133171082,  0.1646732241},
                    {-0.7254997492, -0.1677423269, -1.0745935440},
                    { 0.2478682548, -2.3416306973,  0.5321671963}},

                    {{ 0.7880555391,  2.6202778816, -1.0282965899},
                    {-0.5344914198, -0.5824835896,  0.4730331898},
                    {-1.3073508739,  0.2466813326, -0.3054754436}}}}
            });
            std::shared_ptr<Tensor> myBias = std::make_shared<Tensor>(Array1D<float,4> {{0.3835324347, 0.5210654140, 0.0670478195, 0.2723239064}});
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,3,1,1> { //NCHW
                {{{{ 0.3910248578}},
                    {{ 0.4545786083}},
                    {{-0.1685599536}}},

                    {{{-0.2521645725}},
                    {{ 0.1072919592}},
                    {{ 0.1053370386}}},

                    {{{ 0.4627405703}},
                    {{-0.4509093165}},
                    {{ 0.3544224203}}},

                    {{{ 0.4944877923}},
                    {{ 0.1518175900}},
                    {{ 0.1704865843}}}}
            });
            std::shared_ptr<Tensor> myOutput = std::make_shared<Tensor>(Array4D<float,1,4,1,1> {
                {{{{1.0023646355}},
                    {{0.5979570746}},
                    {{0.0539716333}},
                    {{0.8516038060}}}}
            });
            op->associateInput(0,myInput);
            op->associateInput(1,myWeights);
            op->associateInput(2,myBias);
            op->setDataType(DataType::Float32);
            op->setBackend("cpu");
            op->forwardDims();
            myConv->forward();
            REQUIRE(approxEq<float>(*(op->getOutput(0)),*myOutput, 1e-5f, 1e-8f));
        }
    }
    SECTION("kernel size [5,5]") {
        SECTION("no stride, no dilation") {
            Conv_Op<2> conv_op = Conv_Op<2>({5,5});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,3,7,7> {
                {{{{-1.5819821358e+00, -1.7854875326e+00,  6.9175791740e-01,
                        2.0996522903e+00,  9.6078282595e-01, -8.2813060284e-01,
                        1.6968919337e-01},
                    { 7.2002971172e-01, -1.8102344871e-01, -9.1400068998e-01,
                        1.2503153086e+00, -7.6566308737e-01,  3.8362321258e-01,
                    -1.5427072346e-01},
                    {-1.2361711264e+00,  7.7305638790e-01,  5.6913595647e-02,
                    -2.9433086514e-01, -2.4903245270e-01,  3.2472074032e-01,
                        1.4886678457e+00},
                    {-5.9757936001e-01,  8.0331146717e-01, -3.5785683990e-01,
                        2.5472685695e-01,  5.5055916309e-01,  1.0456261635e+00,
                        7.3776167631e-01},
                    { 4.7463580966e-01,  4.0943801403e-01,  2.1823734045e-01,
                        1.1387450248e-01, -1.2749063969e+00, -4.4165733457e-01,
                    -4.0056625009e-01},
                    { 1.5264246464e+00, -6.9763243198e-01,  2.9963770509e-01,
                    -5.3752285242e-01, -1.3321673870e+00, -3.8046565652e-01,
                        1.2825177908e+00},
                    {-1.0958684683e+00,  8.3749586344e-01, -2.0998646319e-01,
                        1.4721590281e+00, -5.6766863912e-02, -3.6367511749e-01,
                    -4.7073301673e-01}},

                    {{-5.6565409899e-01,  1.2466928959e+00,  1.7969487607e-01,
                        7.1508967876e-01,  8.4663391113e-01,  4.8241707683e-01,
                    -1.1983201504e+00},
                    { 8.1637918949e-02, -1.1711903811e+00,  1.1311115026e+00,
                        7.1283179522e-01,  9.2079514265e-01,  2.7613803744e-01,
                    -1.1035090685e+00},
                    {-2.1333950758e-01, -2.3015061021e-01, -1.9564050436e-01,
                    -5.0889742374e-01, -7.8319394588e-01,  1.0231192112e+00,
                    -9.3693393469e-01},
                    { 1.8086779118e+00, -1.1668262482e+00, -1.6222233772e+00,
                        1.9393971562e-01,  1.0758545399e+00,  7.6798349619e-01,
                        9.5232710242e-02},
                    { 4.4345301390e-01,  6.6509228945e-01,  3.1033048034e-01,
                        9.1944271326e-01, -2.6410639286e-01, -5.5876952410e-01,
                        4.4179755449e-01},
                    {-5.4229873419e-01, -2.9905325174e-01,  1.0674524307e+00,
                    -5.4987430573e-01,  1.2865034342e+00, -2.9273465276e-01,
                    -1.2198672295e+00},
                    { 5.6028085947e-01,  3.1508100033e-01,  1.1667277813e+00,
                    -1.3935315609e+00,  1.0823357105e+00, -5.4969668388e-01,
                    -1.0486271381e+00}},

                    {{ 4.7686126828e-01, -5.9134221077e-01, -1.9606289864e+00,
                    -1.6939393282e+00, -1.1419154406e+00, -1.9365699291e+00,
                        5.9356534481e-01},
                    { 7.7772504091e-01, -1.5844665766e+00,  4.1060101241e-02,
                    -1.2316554785e+00,  5.8156740665e-01, -4.3886345625e-01,
                    -1.6858860254e+00},
                    {-2.2140502930e-01,  8.1183856726e-01,  8.9134818316e-01,
                        2.3568744659e+00, -1.0616497993e+00,  1.6614040732e-01,
                        2.5361647829e-02},
                    { 2.4231983721e-01, -6.6797232628e-01,  6.0891377926e-01,
                    -1.1280845851e-01,  1.3479894400e+00, -1.0160627365e+00,
                    -3.1460383534e-01},
                    { 4.8870971799e-01,  6.7049396038e-01,  9.0237140656e-01,
                    -7.7934461832e-01, -4.3115192652e-01, -4.7609877586e-01,
                    -1.3054919243e+00},
                    {-3.8021788001e-01, -1.2455014884e-01,  4.5932388306e-01,
                    -8.7587934732e-01,  8.4449040890e-01, -3.2640647888e-01,
                        1.1044296026e+00},
                    { 5.3791737556e-01, -5.3963464499e-01,  5.8685314655e-01,
                        1.0961996317e+00,  9.6712523699e-01, -1.5506522655e+00,
                        5.9469139576e-01}}},


                    {{{ 1.4693295956e+00, -7.3901087046e-01,  3.5381242633e-01,
                        2.9703059793e-01, -2.4348771572e+00,  1.8592662811e+00,
                    -4.0466204286e-01},
                    {-9.3013238907e-01,  2.0250107627e-03,  1.0819822550e+00,
                        3.6489057541e-01, -2.3111122847e-01, -5.2143561840e-01,
                        5.6165838242e-01},
                    { 1.3814152777e-01,  2.3664423823e-01,  4.7802433372e-01,
                    -3.9427459240e-02, -2.2023189068e+00, -9.7024470568e-02,
                        9.7239714861e-01},
                    {-3.7371930480e-01, -9.4720816612e-01, -1.1839007139e+00,
                    -6.4284646511e-01, -1.9176955223e+00, -3.9734467864e-01,
                    -4.3485221267e-01},
                    {-4.3412786722e-01,  5.6266564131e-01, -5.7092076540e-01,
                        1.9345518351e+00,  1.1893578768e+00,  7.3246234655e-01,
                    -2.1017053127e+00},
                    { 5.0433129072e-01,  1.0112253428e+00,  9.6384412050e-01,
                        6.1708116531e-01,  2.0769470930e-01,  1.6922599077e-01,
                    -1.7680550814e+00},
                    { 9.6860373020e-01,  6.1789232492e-01, -1.2975339890e+00,
                        1.8161086738e-01, -6.1131346226e-01,  7.5746607780e-01,
                        8.1318039447e-03}},

                    {{-2.9031080008e-01, -7.4931091070e-01, -8.4527724981e-01,
                        1.1330122948e+00, -1.3819234371e+00, -4.6072882414e-01,
                    -9.6089905500e-01},
                    {-6.5712004900e-02,  8.1384368241e-02,  2.2198197842e+00,
                    -2.9237899184e-01, -7.6382297277e-01, -2.2724790573e+00,
                    -2.5980213284e-01},
                    { 5.4156178236e-01,  1.0466700792e+00,  1.5188544989e+00,
                    -1.7176572978e-01,  1.2582596540e+00,  1.9942443073e-01,
                        2.8897064924e-01},
                    {-9.4713824987e-01, -1.4355570078e-01,  1.4625415206e-01,
                    -6.9037866592e-01, -8.4468722343e-01, -6.6903305054e-01,
                    -4.6764537692e-01},
                    {-7.0394933224e-01,  1.4981827736e+00,  1.7820091546e-01,
                    -6.0410326719e-01,  3.1616929173e-01,  3.3617347479e-01,
                        9.6749967337e-01},
                    {-1.3977780342e+00, -1.0755797625e+00, -1.2804145813e+00,
                        3.8759008050e-01, -1.5185016394e+00, -1.7654757202e-01,
                    -6.6924899817e-01},
                    { 9.1542047262e-01, -1.2073645592e+00, -1.0723612309e+00,
                        5.2093189955e-01,  7.4030023813e-01, -4.8004593700e-03,
                    -3.8286569715e-01}},

                    {{-5.6654226035e-02,  5.2225315571e-01,  6.5561562777e-01,
                        3.0832710862e-01, -1.0121858120e+00, -6.5822112560e-01,
                        1.1624189615e+00},
                    {-4.6575185657e-01, -5.9653341770e-02,  1.0733175278e+00,
                    -9.0637534857e-01,  4.2416542768e-01,  2.2279551029e+00,
                        8.1080448627e-01},
                    { 1.3819074631e+00, -4.1368013620e-01, -3.1706240773e-01,
                    -1.2126101255e+00,  3.6613631248e-01,  6.6122449934e-02,
                        7.8346210718e-01},
                    { 4.8448505998e-01, -1.4276403189e-01, -6.0243755579e-01,
                        1.3074930906e+00,  1.4549188614e+00,  2.0044024289e-01,
                        3.3380195498e-01},
                    {-3.6014577746e-01, -1.3747563362e+00, -2.4885981083e+00,
                        7.2047698498e-01,  5.3208362311e-02,  2.1174606681e-01,
                    -1.9557200372e-01},
                    {-4.1237883270e-02,  8.6860567331e-01,  2.3110714555e-01,
                        6.6041219234e-01,  2.2416541576e+00, -7.1505290270e-01,
                        4.2335259914e-01},
                    { 2.0214543343e+00, -5.1382958889e-01,  1.2030944824e+00,
                        1.2382258177e+00, -5.3932261467e-01,  1.5783529282e+00,
                        2.3444575071e-01}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {
                {-0.0668615103,  0.0096536176,  0.1032274216,  0.0085389474}
            });
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,3,5,5> {
                {{{{-0.0256651584, -0.0010102347,  0.0679412410, -0.1094588861,
                    -0.0231344569},
                    { 0.0001781617, -0.0604480579,  0.0879044831,  0.0947295949,
                        0.0732583329},
                    { 0.0955041796, -0.0005995115,  0.0651906058,  0.0311463121,
                    -0.0287336204},
                    { 0.0884799510, -0.0909402817,  0.0743730441,  0.0313041285,
                    -0.1135403663},
                    {-0.0055052429, -0.0088848919, -0.0945433900, -0.0986202136,
                        0.0405280553}},

                    {{-0.0124288145,  0.0954111964,  0.0848305821, -0.0119373864,
                    -0.0877429247},
                    {-0.0569152571, -0.0907590389,  0.0886867270, -0.0892160609,
                    -0.0698366836},
                    {-0.0321554989, -0.0878270566,  0.0040294859,  0.0923006833,
                    -0.0500101894},
                    {-0.0134353461,  0.0157409590,  0.0994384289, -0.1078630984,
                        0.1124769971},
                    {-0.0414051823, -0.0653665662,  0.0932169184, -0.0028723227,
                        0.1107295603}},

                    {{-0.0775901973, -0.0497837812,  0.0233222693,  0.0232233945,
                        0.0641542301},
                    {-0.0499767400,  0.0732239708,  0.0240622535,  0.0533199385,
                        0.0095617082},
                    { 0.0203404855,  0.0888396129,  0.0836194754,  0.0820384473,
                    -0.0988118276},
                    { 0.0671891198, -0.0690774396,  0.0534351096,  0.1113787442,
                        0.0424823835},
                    { 0.0898227617,  0.0541929603, -0.0550950989, -0.0454236157,
                    -0.0338280164}}},


                    {{{-0.0010399260, -0.0008750338,  0.0267464351, -0.0620367154,
                    -0.0695600584},
                    { 0.0839569122, -0.0903549194, -0.0383492336, -0.1136710495,
                    -0.0013333842},
                    { 0.0458775200,  0.0615439937,  0.0627180859,  0.0060472852,
                        0.0809845850},
                    { 0.0803537294,  0.0442582481, -0.0573564842, -0.0694455057,
                        0.0130708050},
                    { 0.0842651576,  0.0554903932,  0.0676512942, -0.0407176018,
                    -0.0405663215}},

                    {{-0.0050318469, -0.0642497912,  0.0112077948, -0.0386103839,
                    -0.0723103955},
                    { 0.0766968951,  0.1154476032,  0.0771673843, -0.0140198674,
                        0.0007884651},
                    { 0.0696242377, -0.0228096284, -0.0823685005,  0.0049601034,
                    -0.0549438223},
                    {-0.0744984299, -0.0340416208, -0.0165108684,  0.0726555437,
                        0.0606348105},
                    { 0.0737286732,  0.0553442463, -0.0405892283,  0.0447632186,
                        0.0778847709}},

                    {{ 0.0277899262, -0.0676826686,  0.0787717402, -0.0618756786,
                    -0.0597167611},
                    {-0.0498202555,  0.0110518495,  0.0751497000, -0.1083162725,
                        0.0197963919},
                    { 0.0177669358,  0.0064084125, -0.0998755395,  0.1101382077,
                    -0.0407040417},
                    { 0.0109004201, -0.0155633893,  0.0020808843,  0.1043395475,
                        0.0241470188},
                    {-0.0832296610,  0.0045275348, -0.0467849411,  0.0846917927,
                    -0.0272654612}}},


                    {{{-0.0315979160, -0.1152938455, -0.0286736861,  0.0225985274,
                        0.0697309002},
                    {-0.0238809530, -0.0416267440,  0.1103974208,  0.0990703627,
                        0.0957340077},
                    {-0.0198308472,  0.0932704657, -0.0856214613, -0.0321927778,
                        0.0995117798},
                    { 0.0178589821, -0.0603880435,  0.0693830997,  0.0229547676,
                    -0.0488339476},
                    { 0.0703017265, -0.0215192195, -0.0029850313,  0.0313456170,
                        0.0309588723}},

                    {{-0.0417468436,  0.0079163658, -0.0190789141, -0.0332297161,
                    -0.0559522100},
                    { 0.0237717275, -0.0125475517, -0.0180152841, -0.0576930158,
                        0.0851965323},
                    { 0.0890824571, -0.0164867807,  0.0140483472,  0.0352087654,
                        0.0737347007},
                    {-0.0402757414,  0.0018327822,  0.0074899504, -0.0751349106,
                    -0.0055309837},
                    {-0.0124193439, -0.0278801825,  0.1109836772,  0.0963729918,
                    -0.0748507902}},

                    {{ 0.0692440420,  0.0490804240,  0.0596088283,  0.0401321165,
                    -0.0519519635},
                    { 0.0487414300,  0.0161796808,  0.1103035659, -0.0254597142,
                    -0.0346399099},
                    { 0.0937067941, -0.0507467575,  0.0202949513, -0.1111455485,
                    -0.0643658787},
                    {-0.0158051737,  0.0514767207, -0.1004245058,  0.0356756486,
                        0.1005240306},
                    {-0.0250654537,  0.0197961032, -0.0232695211, -0.0349051207,
                        0.0455882438}}},


                    {{{-0.0370168537,  0.0584056191, -0.0164116360, -0.0905687362,
                    -0.0273107067},
                    { 0.1133346409,  0.0383886844, -0.1096662879,  0.0884051472,
                    -0.0351093002},
                    {-0.0329314657,  0.0843854621, -0.0302542653, -0.0380227529,
                    -0.0992667377},
                    {-0.0894243866,  0.0335155874,  0.1042508706, -0.0078598047,
                    -0.0247038864},
                    {-0.0504006073, -0.1062115207,  0.0748218298, -0.0250077099,
                        0.0837111399}},

                    {{ 0.0187441055, -0.0280515030,  0.0867191330,  0.0816542357,
                        0.0727743357},
                    { 0.0749907270,  0.0491249822, -0.0638230145, -0.0226818472,
                    -0.0130754299},
                    { 0.1146481931,  0.0110172033, -0.0704981834,  0.0573508702,
                        0.0468072817},
                    {-0.0787231997,  0.0649248436,  0.0268039443, -0.0404104590,
                    -0.0502680764},
                    { 0.1082614362,  0.0888254121,  0.0610329509, -0.1026768908,
                        0.0593265593}},

                    {{-0.0167568102, -0.0386474803,  0.0916503966, -0.0223906469,
                        0.0836452991},
                    {-0.0814188346,  0.1102456823, -0.0443761721,  0.0915107206,
                        0.0361284241},
                    { 0.0015683545,  0.0883040577, -0.1119926274,  0.0363290384,
                    -0.0069464077},
                    {-0.0272085574,  0.1144676507, -0.0756823123, -0.0254881941,
                    -0.0074769561},
                    {-0.0568101220, -0.0335657224,  0.1033082232,  0.0875433162,
                    -0.0518697165}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,2,4,3,3> {
                {{{{-0.3871777952,  0.5219718218,  0.7862872481},
                    {-0.1375074536,  0.8885341883,  0.5779658556},
                    { 0.5681430101, -0.7254346609, -0.2990708053}},

                    {{ 0.0998495445,  0.3688535392,  0.5431413054},
                    {-0.1215116680,  0.4525770843, -0.7176234722},
                    {-0.3100067377,  0.4953409731, -0.7239166498}},

                    {{-0.0482354760, -0.1504757106, -1.0672740936},
                    { 0.2462310642,  0.3214653134,  0.2619933784},
                    { 0.6370882392,  0.3474441171,  1.1181805134}},

                    {{-1.0228110552, -0.5645121336,  0.4233708680},
                    { 0.6847354174, -0.2050004154, -0.1628678143},
                    { 0.4284777045,  0.8981749415, -1.3061262369}}},


                    {{{ 0.2609502375, -0.5564229488, -1.2170201540},
                    {-0.6059533358,  0.0885168016, -0.5935401320},
                    {-0.5572131276,  0.8337767124,  0.4323115349}},

                    {{ 0.3608099222,  1.0569038391,  0.0491272137},
                    { 0.2181473076, -0.1242226511,  0.0207630899},
                    { 0.5975542665,  0.2953254580, -0.2222344875}},

                    {{ 0.8388710022, -0.0150405290,  0.5986887813},
                    {-0.0973970443, -0.9291419387, -0.1056552008},
                    {-0.6437571645,  0.0552899837, -0.4056833386}},

                    {{-0.3430148065,  1.5227048397, -0.2973098159},
                    {-0.2977932394, -0.4785656929,  0.0867543519},
                    { 0.4107315838, -0.3220899701,  0.9598766565}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [2,2], no dilation") {
            Conv_Op<2> conv_op = Conv_Op<2>({5,5}, {2,2});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,3,9,9> {
                {{{{-1.0262345076e+00,  4.3911907077e-01,  1.8221689463e+00,
                    -9.0496063232e-02,  2.3124601841e+00,  1.2676076889e+00,
                        2.9154178500e-01, -1.0054944754e+00,  1.4668885469e+00},
                    {-2.6926884055e-01,  2.7054285631e-02, -6.5346807241e-02,
                        8.3025145531e-01, -9.5455622673e-01,  4.1868144274e-01,
                        1.3323990107e+00,  7.5650310516e-01,  8.5871744156e-01},
                    {-6.7330420017e-01, -3.4846577048e-01,  1.4346588850e+00,
                        2.7194222808e-01,  2.1521434784e+00,  1.6696736217e-01,
                        1.8376970291e-01,  7.1468442678e-01, -1.2447526306e-01},
                    { 8.0760914087e-01, -1.1017438173e+00,  5.1274144650e-01,
                        2.2169539332e-01, -1.0916151851e-01,  1.3694372177e+00,
                        1.1246484704e-02, -6.0367786884e-01,  1.1885926723e+00},
                    { 1.6050251722e+00,  1.5227701664e+00,  2.2580787539e-01,
                        3.3003208041e-01, -1.0569810867e+00, -6.6531229019e-01,
                    -1.5870132446e+00, -5.5765572935e-02,  1.2733308077e+00},
                    {-2.3591136932e+00, -1.6602599621e+00,  1.3936065137e-02,
                    -8.6029511690e-01,  4.3284755945e-01,  9.5207196474e-01,
                    -5.1348048449e-01, -8.7345111370e-01,  6.0008174181e-01},
                    { 1.5557394028e+00, -1.3211530447e+00,  1.1696275473e+00,
                        6.4125038683e-02, -1.2432120740e-01, -5.2999585867e-01,
                        6.8962436914e-01,  1.0586444139e+00, -1.5393349528e-01},
                    {-4.3554434180e-01, -1.5950162709e-01,  1.0490014553e+00,
                    -5.0765627623e-01, -3.0467399955e-01, -5.3264838457e-01,
                        9.8690450191e-01, -1.5088070631e+00,  8.9175784588e-01},
                    { 1.4247367382e+00, -5.8848276734e-02, -1.0292690992e+00,
                        1.0037636757e+00, -5.3313058615e-01, -8.4637176991e-01,
                    -2.3506495357e-01,  2.5999876857e-01,  1.0645623207e+00}},

                    {{ 1.1594262123e+00,  1.4667278528e-01, -1.3725358248e+00,
                    -6.8673837185e-01, -2.6230901480e-01, -1.0233192444e+00,
                        1.0699504614e+00, -1.4223144948e-01,  8.3192962408e-01},
                    { 1.0303292274e+00, -7.8168278933e-01, -8.6705344915e-01,
                    -1.4635435343e+00,  1.1048543453e+00, -5.7091850042e-01,
                        1.5543711185e-01, -5.3300577402e-01,  7.5885367393e-01},
                    { 3.8549888134e-01,  2.1701371670e+00, -1.8536053896e+00,
                        5.6785058975e-01, -1.8138351440e+00, -1.4978441596e-01,
                        6.9255560637e-01, -1.9088203087e-02, -9.3620312214e-01},
                    { 7.6818950474e-02, -7.7815485001e-01, -3.6633202434e-01,
                        3.7396013737e-01,  3.9735972881e-02,  5.3178119659e-01,
                    -6.2058432959e-03,  1.1743594408e+00, -1.3553234339e+00},
                    {-1.2857575417e+00,  1.1403552294e+00,  7.6715612411e-01,
                    -3.8674977422e-01,  3.0915462971e-01,  1.6230952740e+00,
                    -1.0317113400e+00, -3.4624069929e-01,  2.3419447243e-01},
                    { 2.0289704800e+00,  9.5344042778e-01,  1.3398091495e-01,
                        2.3676842451e-01, -9.1961878538e-01, -7.9850316048e-02,
                        1.7670296133e-01,  9.2019730806e-01,  3.2906150818e-01},
                    { 1.5497472286e+00, -7.5789727271e-02, -1.5621168613e+00,
                    -1.3265879452e-01,  2.3210446835e+00,  6.2546260655e-02,
                        1.3731292486e+00, -1.0590184927e+00, -1.1315354109e+00},
                    { 9.0949285030e-01,  1.5912884474e+00,  9.7548440099e-02,
                    -9.5485979319e-01,  8.1781722605e-02, -8.9541131258e-01,
                    -9.7539001703e-01,  1.4973148108e+00, -7.3013925552e-01},
                    {-2.6063349247e+00, -9.8948460817e-01, -4.6334408224e-02,
                    -5.6305401027e-02,  1.2275942564e+00, -1.0767419338e+00,
                        3.5429549217e-01, -8.4239321947e-01, -2.3129728436e-01}},

                    {{-3.6824011803e-01, -1.3600775003e+00, -5.3082842380e-02,
                        2.1362492442e-01, -3.3504801989e-01, -1.2447865009e+00,
                        1.2259862423e+00,  1.2943927050e+00,  4.1017323732e-01},
                    {-7.7389232814e-02,  5.6407207251e-01, -1.5415928364e+00,
                        6.2723839283e-01,  6.6969829798e-01,  1.7052684724e-01,
                        6.7901211977e-01, -9.1711390018e-01,  4.1649293900e-01},
                    {-1.9805129766e+00,  2.6968449354e-02,  2.7286293507e+00,
                        4.8898363113e-01,  1.0338652134e+00, -3.4376963973e-01,
                        1.5369942784e-01,  2.1052715778e+00,  9.6360033751e-01},
                    {-7.8345723450e-02,  1.7320346832e+00,  5.1241457462e-01,
                    -1.6069989204e+00, -8.2155573368e-01,  1.5159207582e+00,
                    -1.7706178427e+00, -3.5353070498e-01, -1.2306252122e-01},
                    { 9.9549388885e-01,  6.6899424791e-01,  1.8666473031e-01,
                        4.1127932072e-01,  1.6854909658e+00,  1.2500119209e+00,
                        6.2952446938e-01, -7.8491973877e-01, -1.7457501590e-01},
                    { 1.9429718256e+00, -1.9178773165e+00, -2.1454337239e-01,
                        2.3576610088e+00,  1.7864210904e-01, -1.3109503984e+00,
                        2.3597766459e-01,  2.8684207797e-01,  2.1074929833e-01},
                    { 1.2090591192e+00, -2.1073739976e-02, -1.5082824230e+00,
                    -8.3251363039e-01,  1.0880084038e+00, -5.8158898354e-01,
                    -3.0504870415e-01, -2.9301109910e-01,  8.9690053463e-01},
                    { 8.7137883902e-01,  4.2053112388e-01, -5.0221372396e-02,
                    -2.8163683414e-01, -1.3151681423e+00, -6.8825948238e-01,
                        3.9207798243e-01, -1.0277284384e+00,  5.0744730234e-01},
                    {-6.9271439314e-01,  5.3248941898e-01,  1.5569035895e-02,
                        3.7575492263e-01, -8.3689816296e-02,  1.1159549952e+00,
                    -1.4623420238e+00, -4.3859991431e-01,  1.1101962328e+00}}},


                    {{{-2.0423326641e-02, -4.2261308432e-01, -1.2248262167e+00,
                    -7.6990664005e-01,  6.8539634347e-02, -5.8175742626e-01,
                        1.3995911926e-02, -2.4920943379e-01,  1.6195765138e-01},
                    { 3.1753441691e-01, -4.5215657353e-01,  3.4099850059e-01,
                        8.2994532585e-01, -1.4502160251e-01,  4.6974977851e-01,
                    -1.0577541590e+00, -3.8428103924e-01, -1.5933537483e-01},
                    { 8.4931612015e-01, -1.4407234192e+00, -8.8770568371e-01,
                        5.6812566519e-01, -5.3451889753e-01, -7.9881912470e-01,
                        3.5436341166e-01,  6.9050423801e-02,  1.0797642469e+00},
                    { 1.5346392393e+00, -8.0458652973e-01,  1.1945800781e+00,
                        2.8993117809e-01, -3.4709338099e-02, -1.9005538225e+00,
                        5.7719033957e-01,  9.8633068800e-01, -8.1702458858e-01},
                    { 2.1732668877e+00,  9.3365108967e-01, -1.4125390053e+00,
                    -2.6723548770e-01,  7.6397609711e-01, -2.5253626704e-01,
                        5.1223450899e-01, -1.3197456598e+00,  3.5206422210e-01},
                    { 4.8497560620e-01, -5.9305703640e-01, -1.3207372427e+00,
                    -1.2734633684e+00, -1.8892226219e+00, -1.2254822254e+00,
                    -1.0012117624e+00,  4.4947278500e-01,  1.3996914029e-01},
                    {-7.1806615591e-01, -2.1445353031e+00,  8.4149742126e-01,
                    -1.2808227539e+00, -1.4514193535e+00,  8.5352408886e-01,
                        5.3722190857e-01,  1.0689587593e+00,  4.6941962838e-01},
                    {-2.9689872265e-01,  5.7039666176e-01, -9.7570866346e-01,
                    -7.5906850398e-02,  1.9404630363e-01, -1.2686843872e+00,
                        7.7697885036e-01, -9.5903050900e-01, -1.1655918360e+00},
                    { 6.3129204512e-01, -3.5601881146e-01,  6.5524661541e-01,
                    -5.0732725859e-01,  1.3058322482e-02, -5.4524648190e-01,
                        4.0899762511e-01, -9.8380684853e-01,  4.5132014155e-01}},

                    {{ 1.5725825727e-01,  2.1817979813e+00, -1.0229426622e+00,
                        5.2571322769e-02,  1.7906796932e+00,  1.5359158516e+00,
                    -1.6435106993e+00, -3.8198566437e-01, -1.5808371305e+00},
                    {-1.3824816942e+00,  7.8574791551e-02, -5.1695871353e-01,
                    -5.0357979536e-01, -1.1000699997e+00,  1.7837898433e-01,
                        7.6670318842e-01, -1.2971758842e+00,  1.3056064844e+00},
                    { 1.0061295033e+00,  2.9437178373e-01, -3.0505040288e-01,
                        1.4037330151e+00,  1.5578675270e+00, -2.3277984560e-01,
                        2.6896992326e-01,  2.0645604134e+00, -1.6063396931e+00},
                    { 4.0633159876e-01,  4.3755510449e-01, -4.1917449236e-01,
                    -1.2625947595e-02, -1.3815000653e-02,  7.8905373812e-01,
                        1.3740755618e-01,  1.4110846519e+00, -1.3393870592e+00},
                    {-9.1028696299e-01,  9.2755252123e-01, -1.0052075386e+00,
                    -9.9492824078e-01,  6.7882398143e-03,  4.5161217451e-01,
                        6.1087304354e-01,  1.1929332018e+00, -1.1769343615e+00},
                    { 1.5572510660e-01,  4.4804865122e-01,  5.9232199192e-01,
                        1.4278647900e+00,  3.1380197406e-01, -6.2812852859e-01,
                    -1.0075987577e+00, -5.8191227913e-01,  1.6295973063e+00},
                    {-8.1633257866e-01, -3.1518262625e-01,  1.1550375223e+00,
                    -1.8897730112e+00, -4.2993515730e-01, -5.9119540453e-01,
                        9.1979181767e-01, -1.7244141102e+00,  9.7749936581e-01},
                    {-1.2826606035e+00, -8.3349563181e-02, -8.0689124763e-02,
                    -2.3780565262e+00,  6.4297276735e-01, -8.6600404978e-01,
                    -1.0059145689e+00, -4.3131682277e-01,  7.4153000116e-01},
                    { 1.1657108068e+00,  1.3443695307e+00, -1.7663496733e-01,
                        1.2084038258e+00, -1.0071879625e+00, -9.3671619892e-01,
                        5.8391742408e-02, -9.3132650852e-01,  9.3861585855e-01}},

                    {{ 4.7928895219e-04,  1.5494421721e+00,  1.6211936474e+00,
                        2.2041907310e+00, -3.2932338119e-01, -1.6941326857e+00,
                        4.2259506881e-02, -3.0548694730e-01, -5.4214018583e-01},
                    {-9.2042051256e-02,  3.2141461968e-01,  1.4343656301e+00,
                        3.2310426235e-01, -4.1865095496e-01,  1.0167524815e+00,
                        4.7122836113e-01, -1.1745406389e+00,  3.3083841205e-01},
                    {-1.2731312513e+00,  1.6528505087e+00, -3.0167734623e-01,
                        1.3895208836e+00,  1.4259627461e-01,  8.3795058727e-01,
                    -7.1655702591e-01,  1.0907325745e+00, -2.2553758621e+00},
                    { 1.3037883043e+00, -4.7551321983e-01, -9.0221446753e-01,
                    -6.9939422607e-01,  8.2557731867e-01,  2.2087992728e-01,
                    -1.5934921503e+00,  3.7456196547e-01,  1.2232249975e-01},
                    {-2.4604837894e+00, -7.6448154449e-01,  7.1209603548e-01,
                        1.0618342161e+00,  3.1532841921e-01, -1.3785527945e+00,
                    -2.0559796691e-01,  5.9892934561e-01, -1.0338040590e+00},
                    {-4.3527457118e-01, -2.2242832184e+00,  7.4645686150e-01,
                    -1.2474944592e+00,  9.7446382046e-01,  8.6570835114e-01,
                    -7.6936386526e-02,  1.1415704489e+00,  1.3671324253e+00},
                    { 1.1514008045e-01, -4.2773807049e-01, -5.3324125707e-02,
                    -2.5783428550e-01, -1.0264251232e+00, -8.0453145504e-01,
                    -2.0028649271e-01, -8.4552615881e-03, -5.6716823578e-01},
                    { 4.6886390448e-01,  5.2331888676e-01,  1.3241011649e-02,
                        9.7727668285e-01,  3.6741080880e-01,  7.4033015966e-01,
                    -1.4176219702e-01, -1.7566013336e+00,  1.8248949945e-01},
                    { 3.4004274011e-01, -3.9557147026e-01, -1.8804949522e+00,
                        3.9474415779e-01, -7.9836857319e-01, -3.9796181023e-02,
                    -1.3347951174e+00,  1.0292435884e+00,  8.2486397028e-01}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {
                {-0.0256307721, -0.0979429483, -0.0857793465,  0.1046720892}
            });
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,3,5,5> {
                {{{{ 7.9161055386e-02,  1.0865640640e-01, -8.4078900516e-02,
                    -8.7769351900e-02,  5.5054081604e-03},
                    {-5.2150800824e-02, -8.6442008615e-02,  6.6387809813e-02,
                        7.4080288410e-02, -8.6096972227e-02},
                    { 6.5568581223e-02,  1.9050860777e-02, -6.8354956806e-02,
                    -1.1355084926e-01,  1.0479265451e-01},
                    { 8.2975491881e-02,  9.2087492347e-02,  2.0702988841e-03,
                        1.1016045511e-01, -1.8409077078e-02},
                    { 8.0884704366e-03, -6.1311736703e-02, -1.0344123840e-01,
                    -5.7280212641e-02,  9.0915616602e-03}},

                    {{-9.9937595427e-02, -7.4386410415e-02, -6.6606923938e-02,
                    -6.9912821054e-03, -5.1879696548e-02},
                    { 3.3732347190e-02,  7.3814824224e-02, -6.5518431365e-02,
                        1.1177737266e-01,  6.4293742180e-02},
                    { 1.7644548789e-02, -8.2394741476e-02, -9.5198350027e-03,
                    -2.8538773768e-03, -5.5996231735e-02},
                    {-9.7051627934e-02,  8.5470348597e-02, -5.2727516741e-02,
                        5.3426343948e-02, -1.0500780493e-01},
                    { 3.5254769027e-03,  2.5447849184e-02,  9.5501638949e-02,
                        7.0650175214e-02, -1.1365779489e-01}},

                    {{ 2.2010399334e-05,  1.1450178921e-01,  2.8901636600e-02,
                        5.7596616447e-02,  3.7809558213e-02},
                    {-1.1890708469e-02, -1.1361743510e-01, -5.3352948278e-02,
                    -7.3011368513e-02,  9.1052189469e-02},
                    { 1.1218705028e-01,  7.7470839024e-02, -3.3929269761e-02,
                        3.4500412643e-02,  9.8039925098e-02},
                    { 6.3609093428e-02, -2.8273850679e-02, -1.0159312189e-01,
                    -4.9110885710e-02, -8.1224292517e-02},
                    {-1.6401037574e-02,  2.3994818330e-02, -2.5938203558e-02,
                        7.3313489556e-03,  9.8718859255e-02}}},


                    {{{ 3.4335671808e-04,  3.6441650242e-02,  3.0187530443e-02,
                        6.0592081398e-02, -2.0527897403e-02},
                    { 1.1346739531e-01, -3.2229032367e-02, -2.2989841178e-02,
                        6.0040432960e-02,  4.8826828599e-02},
                    {-6.0838893056e-02, -6.0655072331e-02, -8.7084382772e-02,
                        1.0714148730e-01,  1.0812971741e-01},
                    {-4.2028987082e-04,  8.5651963949e-02,  8.0970667303e-02,
                    -2.3986544460e-02, -1.8859704724e-03},
                    {-6.6010192037e-02,  9.4307392836e-02,  1.1242634058e-01,
                    -8.5995316505e-02, -2.7027517557e-02}},

                    {{ 1.1397475004e-01,  6.3374929130e-02, -7.7009305358e-02,
                    -8.3151273429e-03,  8.2951277494e-02},
                    { 9.8664939404e-02, -8.1143163145e-02,  3.9020900149e-03,
                    -1.4049283229e-02, -8.3722546697e-02},
                    { 4.7419264913e-02, -1.6121247783e-02, -3.9537753910e-02,
                        3.4721549600e-02, -6.9126158953e-02},
                    {-2.2569455206e-02,  3.0018799007e-03, -1.1006606370e-01,
                        7.5041048229e-02,  1.0107534379e-01},
                    {-6.8557247519e-02, -3.2554015517e-02, -1.0497659445e-01,
                        6.0661323369e-02, -4.7699000686e-02}},

                    {{ 3.5086035728e-02, -4.3413480744e-03,  2.1944919601e-02,
                    -3.3144496381e-02, -8.7255813181e-02},
                    {-1.7425794154e-02,  6.0090426356e-02,  5.0702422857e-02,
                        1.0287687927e-01, -1.0552648455e-01},
                    { 7.2182372212e-02, -7.6782293618e-03,  1.6422374174e-02,
                    -5.1674857736e-02, -7.1635149419e-02},
                    { 7.0604115725e-02, -6.8249620497e-02, -8.9388333261e-02,
                    -5.1136296242e-02,  6.3407994807e-02},
                    { 6.1435334384e-02,  9.4730198383e-02,  8.3181746304e-02,
                        5.7519987226e-02,  7.0465311408e-02}}},


                    {{{ 1.5252918005e-02,  7.4633888900e-02,  9.0553842485e-02,
                        3.8993149996e-02,  6.3962623477e-02},
                    { 9.6443951130e-02, -5.6319754571e-02, -2.0676823333e-02,
                        1.0050912201e-01, -1.0454320349e-02},
                    { 7.1146171540e-03, -9.4763293862e-02, -1.1824182235e-02,
                    -8.2581162453e-02,  8.5433647037e-02},
                    { 1.5876146033e-02,  1.0734396428e-01, -1.1871671304e-02,
                        1.0982066393e-01,  1.5651857480e-02},
                    { 3.9369460195e-02, -6.0143850744e-02,  1.0505072027e-01,
                    -8.4761910141e-02,  5.0331920385e-02}},

                    {{ 1.0066681542e-02, -1.0608792305e-01,  3.1187359244e-02,
                    -1.1222343892e-02,  1.2503461912e-02},
                    {-9.5613434911e-02, -2.0962793380e-02,  6.2319990247e-02,
                        3.6858320236e-02,  1.0154407471e-01},
                    {-5.7882230729e-02,  4.0998354554e-02,  2.2802127525e-02,
                    -8.7465390563e-02, -7.8975915909e-02},
                    {-1.7631363124e-02, -7.0353029296e-03, -1.0995418578e-01,
                        2.3209381849e-02,  5.4516538978e-02},
                    {-6.7430905998e-02, -3.2775081694e-02, -7.1572959423e-02,
                        3.0015124008e-02, -8.2214266062e-02}},

                    {{ 7.8755617142e-02,  3.5310130566e-02, -2.2435920313e-02,
                        9.6409514546e-02,  1.0338477045e-01},
                    { 5.9903923422e-02,  1.8227061257e-02,  1.9898558035e-02,
                        1.7521159723e-02,  2.3488936946e-02},
                    {-9.0055637062e-02,  4.9969940446e-03,  5.6411098689e-02,
                    -4.5843422413e-02,  3.5857871175e-02},
                    {-1.9720340148e-02,  1.4090083539e-02, -7.7931761742e-02,
                        1.5021140687e-02,  6.3057549298e-02},
                    {-3.7665259093e-02,  7.1552298963e-02, -5.8394841850e-02,
                        8.0049857497e-02, -1.0659194738e-01}}},


                    {{{-7.7228933573e-02,  5.1064457744e-02, -5.6017566472e-02,
                        1.1644850485e-02,  1.6073303297e-02},
                    {-3.2181475312e-02,  8.2790434361e-02, -1.1385639757e-01,
                    -1.1087367684e-01,  2.1422423422e-02},
                    {-2.7187412605e-02,  7.9632617533e-02,  9.0410903096e-02,
                        2.9836246744e-02,  7.0767119527e-02},
                    { 4.1806723922e-02,  8.9509122074e-02, -7.6765663922e-02,
                    -4.1281420738e-02,  4.2842119932e-02},
                    { 8.1620868295e-03, -6.4203604124e-03,  1.9168345258e-02,
                        9.7423560917e-02,  8.7407097220e-02}},

                    {{ 5.9115942568e-02, -5.4798915982e-02,  7.6376385987e-02,
                        2.8852632269e-02, -3.4071631730e-02},
                    { 4.3502859771e-02,  6.9351151586e-02, -7.3672071099e-02,
                    -1.3623046689e-02,  9.9386192858e-02},
                    { 8.2298137248e-02,  3.1567070633e-02, -5.2173703909e-02,
                        1.0301522166e-01, -2.8413718566e-02},
                    {-4.0537059307e-02,  2.9644003138e-02,  3.0859379098e-02,
                        1.1227397621e-01, -5.6883804500e-02},
                    {-1.0370600969e-01, -2.3154499009e-02, -7.5694397092e-02,
                        7.3324322701e-02, -7.2433322668e-02}},

                    {{-9.0371906757e-02,  1.1529860646e-01, -6.1082314700e-02,
                        1.0215951502e-01,  1.1091886461e-01},
                    { 1.0044538975e-01, -8.9651204646e-02, -1.0203760862e-01,
                    -1.0518372059e-01,  1.0253529996e-01},
                    { 1.1435522884e-01, -7.3617205024e-02,  6.6930335015e-03,
                    -4.1806902736e-02, -4.3869618326e-02},
                    { 6.4512699842e-02, -6.9700092077e-02,  7.6211534441e-02,
                        4.5039333403e-02,  6.5938614309e-02},
                    {-8.8014036417e-02,  2.7664732188e-02, -3.6620914936e-02,
                        6.0766555369e-02, -1.6977010295e-02}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,2,4,3,3> {
                {{{{-0.3815447390,  0.5393404961,  1.2082489729},
                    {-0.7000376582,  0.5649882555,  0.6821430922},
                    { 0.7830977440,  0.2265645415, -0.0234967973}},

                    {{ 0.5304207802,  0.3521062732, -0.4996214509},
                    {-0.5668326020, -0.1662681103, -0.0246974919},
                    {-0.4764020443, -0.6305201650,  0.1680680513}},

                    {{ 0.3380682766,  0.2957296073,  0.2884519398},
                    {-0.6942415833,  0.2494415045, -0.1550499499},
                    {-0.6718355417,  0.3543845117, -0.4612499774}},

                    {{-0.0101785604, -0.0853718519, -0.0978565961},
                    {-0.4967738688, -1.4151864052,  0.4521864057},
                    { 0.6482952833, -0.4406591058,  0.5528392196}}},


                    {{{ 0.2647683024, -0.0899230987,  0.0251224432},
                    { 0.2060495466, -0.5142026544, -0.6169960499},
                    { 0.8739010096, -0.4528320730, -0.6620424986}},

                    {{ 0.4344040155, -0.3720431328,  0.3896343112},
                    {-0.3978653252, -0.9195920229, -0.1681326628},
                    {-1.1699988842, -0.5111203790, -0.2178955674}},

                    {{-0.2299620360,  0.0700573027, -0.2049577832},
                    { 0.3746963441, -0.9416288137,  0.0695008487},
                    { 0.5960077643, -0.2242757827,  0.0868191570}},

                    {{-0.4896622002,  0.5204730630,  0.3560196757},
                    { 0.3295116723,  0.6170410514,  0.1678314358},
                    { 0.1554045975,  0.2489441931, -0.3163521886}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-8f));
        }
        SECTION("stride [2,2], dilation [2,2]") {
            Conv_Op<2> conv_op = Conv_Op<2>({5,5}, {2,2}, {2,2});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array4D<float,2,3,9,9> {
                {{{{ 6.5991479158e-01, -5.3738802671e-01,  1.6696882248e+00,
                    -1.1178103685e+00, -2.0366449356e+00,  7.6323038340e-01,
                        8.8008441031e-02, -5.7445436716e-01, -1.1746516228e+00},
                    { 1.1325144768e+00,  1.8485877514e+00,  6.6733115911e-01,
                    -1.1192236841e-01, -7.6075768471e-01,  9.3889570236e-01,
                        1.1970713139e+00, -1.1396632344e-01,  3.7702596188e-01},
                    { 1.0176507235e+00, -3.2929670811e-01,  2.2804117203e+00,
                        2.6695098877e+00,  9.1759788990e-01, -7.7024054527e-01,
                    -1.1899918318e+00, -3.5644110292e-02,  1.4563606977e+00},
                    { 1.3222569227e-01,  1.7781604528e+00, -5.3485935926e-01,
                    -1.1690793559e-02, -3.1795254350e-01,  1.3168191910e+00,
                    -9.8241758347e-01, -1.9394657016e-01, -2.6476421952e-01},
                    { 1.7474385500e+00, -5.1397854090e-01,  4.1304263473e-01,
                    -2.2527515888e-01,  1.1935096979e+00, -1.8106347322e-01,
                        1.7698837519e+00, -1.1245247126e+00, -8.0709517002e-01},
                    {-6.6057407856e-01,  5.4547226429e-01, -4.9604213238e-01,
                    -1.5724540949e+00,  7.3046660423e-01, -1.8622325659e+00,
                    -1.7612577677e+00,  2.7059727907e-01,  2.0769243240e+00},
                    { 1.6489045620e+00, -9.1659523547e-02,  6.5829491615e-01,
                        9.5888656378e-01, -9.7225487232e-01,  9.9177438021e-01,
                        4.1120108962e-01,  1.7129123211e+00, -1.3064719737e-01},
                    { 5.6299138069e-01,  1.1197557449e+00,  1.0256431103e+00,
                    -1.0448687077e+00, -3.9633819461e-01,  2.1613819599e+00,
                    -2.9366123676e-01,  2.0935084820e+00, -3.1408703327e-01},
                    {-2.2087215912e-03, -5.5481916666e-01, -1.0586282015e+00,
                        1.6510184109e-01, -5.3518980742e-01, -1.5306407213e+00,
                    -1.4912575483e+00,  4.6741631627e-01,  1.6276098490e+00}},

                    {{-1.2711778879e+00, -6.6529834270e-01,  2.0430049896e+00,
                        1.3407748938e+00,  1.5101557970e+00,  3.0264301300e+00,
                        5.7267320156e-01,  1.9472989440e-01, -1.0449569672e-01},
                    {-4.3861621618e-01, -8.4084004164e-01,  1.8874751031e-01,
                    -3.0964607000e-01, -3.3041627407e+00,  4.0943336487e-01,
                    -5.3273528814e-01,  1.1388880014e+00,  4.4220641255e-01},
                    {-1.8995233774e+00,  2.4473433197e-01, -4.1401520371e-01,
                    -6.5818083286e-01, -1.1139613390e+00,  2.1693031490e-01,
                    -1.0517214537e+00, -2.3312714100e+00,  6.0954615474e-02},
                    { 1.3127720915e-02, -2.2521468997e-01, -2.5984519720e-01,
                        1.5528632700e-01, -4.9426975846e-01, -1.1347863674e+00,
                        1.1981898546e-01,  2.3249061108e+00, -4.2492222786e-01},
                    { 2.5971227884e-01,  6.9073438644e-02, -1.2523316145e+00,
                    -1.8091107905e-01, -1.7139790952e-01,  8.1327247620e-01,
                    -6.7866450548e-01,  2.2402961254e+00, -8.5352472961e-02},
                    { 7.1751189232e-01, -2.3494932055e-01, -1.3409119844e+00,
                        5.2470743656e-01,  7.6781928539e-01, -7.1144473553e-01,
                    -1.9754718542e+00,  2.6893837452e+00,  7.8437983990e-01},
                    {-4.0214532614e-01,  1.7369346619e+00, -1.7632387578e-01,
                    -1.5825942755e+00,  1.0516833067e+00,  2.0817482471e+00,
                    -9.7633296251e-01,  7.8872179985e-01, -4.3127769232e-01},
                    { 9.7235912085e-01, -5.8469034731e-02,  2.6687437296e-01,
                    -1.4018902779e+00,  1.2706108093e+00,  5.8360731602e-01,
                    -1.2217177153e+00, -8.2037007809e-01, -6.4826738834e-01},
                    { 3.8622283936e-01,  1.1064618826e+00, -1.5179945230e+00,
                        3.9867460728e-01,  8.5346035659e-02, -1.1623222828e+00,
                        9.5119558275e-02,  8.7334537506e-01,  1.0425381660e+00}},

                    {{ 7.8875219822e-01, -1.1821738482e+00,  3.8991808891e-01,
                    -1.0048108101e+00, -2.0707476139e-01, -7.3082458973e-01,
                        1.0729664564e+00, -1.2859574556e+00, -8.7584072351e-01},
                    { 3.7907764316e-01,  1.1199241877e+00,  1.9296696186e+00,
                        1.2730616331e-01,  6.0980606079e-01, -7.3303855956e-02,
                    -5.9152889252e-01, -1.2527221441e-01,  1.0408999920e+00},
                    {-4.4774639606e-01, -8.6148458719e-01,  1.4992856979e+00,
                        1.3516107798e+00, -1.1647412777e+00,  1.3260208368e+00,
                    -5.3640037775e-01,  8.5038894415e-01,  4.0011933446e-01},
                    {-7.2440391779e-01,  6.6310149431e-01, -9.2786878347e-01,
                        1.4332934618e+00, -1.2407248020e+00, -1.9271074235e-01,
                        5.4011422396e-01, -2.0801360905e-01,  1.1701456308e+00},
                    {-1.5601218939e+00, -1.3747200966e+00,  2.4702382088e-01,
                        4.4083452225e-01, -6.4500576258e-01, -7.6838213205e-01,
                        5.3242987394e-01,  2.4775697291e-01, -1.4102922678e+00},
                    {-9.1009324789e-01, -8.6870056391e-01, -7.6603555679e-01,
                    -1.4323582649e+00,  1.5076324344e-01, -6.6071575880e-01,
                        6.5643036366e-01,  7.0738911629e-01, -1.2404441833e+00},
                    { 6.5142494440e-01,  2.4921335280e-02,  5.0163829327e-01,
                        1.3338588476e+00,  1.7744785547e+00, -6.3132202625e-01,
                        8.1679749489e-01, -4.4332244992e-01, -1.3621041775e+00},
                    { 6.9176673889e-01, -6.8686300516e-01,  1.0556088686e+00,
                        1.1115324497e+00, -3.8817191124e-01, -6.3901716471e-01,
                        7.4065899849e-01,  5.2005118132e-01,  4.8783886433e-01},
                    {-4.8411735892e-01,  8.3703887463e-01,  5.9305649996e-01,
                        1.3562313318e+00, -7.5646054745e-01, -4.2536877096e-02,
                        1.7571094036e+00, -4.7270351648e-01,  2.2838380337e+00}}},


                    {{{-4.0447491407e-01,  6.2086683512e-01,  1.2934124470e+00,
                        1.0094794035e+00, -1.0171808004e+00, -2.6295968890e-01,
                    -4.9549680948e-01, -7.9358913004e-02,  9.4110012054e-02},
                    { 8.7407690287e-01,  4.2964717746e-01, -1.7619720697e+00,
                    -3.6411872506e-01, -1.7255870998e-01,  4.4035488367e-01,
                        1.6617731750e-01,  3.5132259130e-01, -4.4415783882e-01},
                    {-1.7608845234e+00, -2.3283758759e-01,  6.2204450369e-01,
                        4.5604482293e-01,  4.8442709446e-01, -1.2630403042e+00,
                    -1.0600868613e-02, -9.3579161167e-01,  7.2725065053e-02},
                    {-4.9674937129e-01,  2.7484998107e-01, -7.8241840005e-02,
                    -3.8036200404e-01, -1.1722209454e+00,  1.5537194014e+00,
                        3.3076366782e-01, -8.7499739602e-03,  2.0694589615e+00},
                    {-1.3299342394e+00, -1.5577025712e-01,  1.6106146574e+00,
                        5.3044539690e-01, -1.2436783314e+00, -7.8046637774e-01,
                    -9.2501389980e-01,  1.5277367830e+00,  3.6043179035e-01},
                    {-9.9188965559e-01, -1.2423185110e+00,  3.9069399238e-01,
                    -1.2723475695e+00, -1.7772109509e+00, -3.7175655365e-01,
                    -8.7014752626e-01,  1.3463658094e+00, -1.5951974392e+00},
                    {-7.8578054905e-01, -6.2972821295e-02, -4.8052150011e-01,
                    -1.2783004045e+00, -3.8468798995e-01,  4.7666281462e-02,
                        4.2015764117e-01, -4.4800898433e-01,  3.9750581980e-01},
                    { 1.2391144037e+00,  4.4924438000e-01, -5.7612675428e-01,
                    -1.2152553797e+00,  6.7230182886e-01,  1.3609430790e+00,
                    -4.2446309328e-01,  3.0986270308e-01,  3.6792102456e-01},
                    { 1.4776864648e-01, -9.7534912825e-01, -1.9648849964e-01,
                    -1.0378727913e+00,  2.5092484429e-02,  8.9258450270e-01,
                    -2.2762279212e-01, -2.3942720890e+00,  7.9677361250e-01}},

                    {{-4.4367963076e-01,  9.8137008026e-03,  1.6468089819e+00,
                        3.9348766208e-01,  3.7895750999e-01, -1.8910832405e+00,
                        1.6934220791e+00, -5.1142543554e-01,  2.1927893162e+00},
                    {-1.2872399092e+00,  5.1995629072e-01,  2.8462198377e-01,
                    -7.7300745249e-01,  6.1586141586e-02,  7.9627609253e-01,
                        5.2585881948e-01,  2.0059709251e-01, -1.0767682791e+00},
                    { 1.2913355827e+00, -5.2280706167e-01,  9.3896692991e-01,
                    -2.7119943500e-01, -1.3428537548e-01, -7.7558577061e-02,
                    -1.4985687733e+00,  1.5150824785e+00, -1.3824665546e+00},
                    {-3.4071408212e-02, -7.0768481493e-01,  3.9081773162e-01,
                    -1.0144554377e+00, -1.2199249268e+00,  9.7416710854e-01,
                    -2.1364924908e+00, -7.5508749485e-01, -1.3795818090e+00},
                    { 4.5370283723e-01, -1.7424255610e+00, -8.5776680708e-01,
                        3.9504718781e-01,  9.9192768335e-02,  7.1981537342e-01,
                        2.7460846305e-01, -8.1848166883e-02,  7.6311039925e-01},
                    { 1.6829998791e-01,  2.8629219532e-01, -1.3655959070e-01,
                    -1.2729966640e+00,  2.9406669736e-01, -3.6713847518e-01,
                        6.3521367311e-01,  9.0642973781e-02,  4.6122816205e-01},
                    {-5.9019500017e-01,  6.1684101820e-01,  5.7554990053e-01,
                    -7.1885848045e-01,  1.5339116752e-01,  7.2704249620e-01,
                    -1.1901499033e+00,  1.8046575785e-01, -3.2128947973e-01},
                    { 6.9699871540e-01, -1.5316461325e+00, -1.0008054972e+00,
                        1.8971544504e+00,  1.6860273480e-01,  4.6585604548e-01,
                    -7.4088859558e-01, -2.0486815274e-01,  2.4802033603e-01},
                    {-8.8578667492e-03, -8.0224162340e-01,  1.5357034206e+00,
                        1.2365963459e+00,  1.4597702026e+00, -5.4030877352e-01,
                        7.9093635082e-01,  1.1919885874e+00, -1.9415197372e+00}},

                    {{ 1.6230100393e-01,  1.7142108679e+00,  1.5414776802e+00,
                    -4.2192405462e-01,  4.9785825610e-01,  2.1395962238e+00,
                        9.2708784342e-01, -8.3940023184e-01, -8.0437123775e-01},
                    {-9.4176328182e-01,  2.6041597128e-01, -1.0130367279e+00,
                    -3.5772189498e-01,  1.6592922211e+00,  1.9243527651e+00,
                        1.4461495876e+00,  1.2969638109e+00,  2.9279315472e+00},
                    { 5.7384677231e-02, -6.6253073514e-02, -7.2724334896e-02,
                    -2.3743975163e-01,  9.5880138874e-01,  3.6361989379e-01,
                        1.2075768709e+00,  5.1945459843e-01, -2.4960200787e+00},
                    { 1.5223011971e+00,  6.6761517525e-01, -7.1185566485e-02,
                        1.3005328178e+00, -1.6010546684e+00, -8.3948358893e-02,
                        1.3929320872e-01,  5.7007002831e-01,  1.5402120352e+00},
                    {-1.0000891685e+00,  5.6669050455e-01,  1.1230304241e+00,
                    -9.2030251026e-01, -2.2001121044e+00,  7.8229683638e-01,
                    -3.0678564310e-01, -1.7156904936e-01, -8.6419695616e-01},
                    { 8.8148981333e-01, -1.7107343674e+00, -2.9174503684e-01,
                        7.9814374447e-01, -9.9373269081e-01, -5.2981477231e-02,
                    -1.0876508951e+00, -4.6575244516e-02,  8.7985855341e-01},
                    {-2.6840454340e-01,  7.7923542261e-01,  9.3854445219e-01,
                        4.0857732296e-01,  5.7850652933e-01,  1.4003618956e+00,
                    -7.1249789000e-01, -3.1672206521e-01,  1.6309374571e-01},
                    { 8.0395114422e-01,  5.8513361216e-01,  1.3350075483e+00,
                        7.1663349867e-01,  7.2658437490e-01,  4.9841433764e-01,
                    -1.7066024542e+00, -8.7015740573e-02, -3.9591795206e-01},
                    {-2.6701366901e-01, -2.9452782869e-01,  2.2669389844e-02,
                    -8.6450153589e-01, -4.1996881366e-01, -5.4280841351e-01,
                        1.0260531902e+00, -1.0119054317e+00, -5.9093588591e-01}}}}
            });
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<float,4> {
                { 0.0219541416, -0.0854099169,  0.0740336627,  0.0793448612}
            });
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<float,4,3,5,5> {
                {{{{-0.0879265219, -0.0899311453,  0.1075298190, -0.0439606048,
                        0.0142062334},
                    {-0.0589592122, -0.0045246030, -0.0316865370,  0.0141878976,
                        0.1153501272},
                    { 0.0643049031, -0.0598805100, -0.0832074434,  0.0077776825,
                        0.0623578280},
                    {-0.0301217884, -0.0073744338,  0.1121563762, -0.0863516033,
                        0.0386641659},
                    { 0.0234115086, -0.0829083994,  0.0418176949, -0.0570392683,
                    -0.0580074638}},

                    {{-0.0012715239,  0.0863351673, -0.1069950312,  0.0660321191,
                    -0.0725978017},
                    { 0.0047315061, -0.0222416110,  0.0019143816,  0.0152916117,
                    -0.0825890377},
                    { 0.1134350598,  0.0128993327, -0.0935105979,  0.0104216831,
                        0.0318991244},
                    {-0.0340792425,  0.1136758402, -0.0319192223,  0.0048904521,
                    -0.0254838988},
                    { 0.0453651547, -0.1142466366,  0.0613734871,  0.0306740720,
                        0.0760814846}},

                    {{ 0.0366517194,  0.0485932752, -0.0874269605, -0.0775868669,
                    -0.0356033146},
                    { 0.1102349758,  0.0213746168, -0.0696568191,  0.0301248170,
                        0.0069745299},
                    {-0.0293842964, -0.0122400951,  0.0375012197, -0.1118148938,
                    -0.0948114246},
                    {-0.1078331321, -0.0746667907, -0.0589634664, -0.0510230660,
                        0.0664016604},
                    {-0.0519216098,  0.0763967782,  0.0384426974, -0.0007680102,
                        0.1003586799}}},


                    {{{-0.0771117881, -0.0127118658, -0.1025340930, -0.0193717945,
                    -0.0718696415},
                    { 0.1134984195, -0.0469665602, -0.0328875706, -0.0890083611,
                        0.0217132103},
                    {-0.0783405602, -0.0964497253,  0.0073435861, -0.0290560126,
                    -0.0307252090},
                    { 0.0957401842,  0.0746279880, -0.0221166238, -0.1134141684,
                        0.0168936905},
                    { 0.0833431259,  0.0800427124, -0.0428234823,  0.0058708852,
                        0.0055020354}},

                    {{-0.0619548559,  0.0440408550, -0.0580734424, -0.0271147881,
                        0.0551881120},
                    {-0.1015950069, -0.0655148402, -0.0656934455,  0.0734511092,
                        0.0593257882},
                    {-0.0388379470,  0.0538616925,  0.0215578172,  0.1115155891,
                        0.0267907996},
                    {-0.0117263123,  0.1013097093, -0.0503486842, -0.0227387249,
                        0.0769604445},
                    {-0.0400040187, -0.0017201286,  0.0305580869, -0.1087302193,
                    -0.0778466389}},

                    {{ 0.0157795977,  0.0284815803, -0.0283647832, -0.0756585225,
                        0.0766027272},
                    { 0.0176657196, -0.1124993339, -0.0154858092, -0.0368758999,
                        0.0100407479},
                    {-0.0125693697,  0.0512169749, -0.0256510209, -0.0971343294,
                    -0.0872697607},
                    {-0.0426635183,  0.0547962859, -0.0496184044,  0.0890550837,
                        0.1007452309},
                    {-0.1043196693, -0.0133433538, -0.0131574012,  0.0442749150,
                        0.0401787795}}},


                    {{{-0.1098219156,  0.0145482961, -0.0767832026,  0.0287463516,
                        0.0936923251},
                    { 0.0129374759, -0.0915895551,  0.0694310442,  0.0978608951,
                    -0.0756938607},
                    { 0.0162203833, -0.0620732345, -0.0158150289, -0.0646957755,
                    -0.0085407924},
                    {-0.0168146919, -0.0887613446, -0.0721658245,  0.0921881124,
                        0.0079541644},
                    {-0.1055120081, -0.0643930957, -0.0260313656, -0.0003582919,
                        0.0954318866}},

                    {{ 0.0811082423,  0.1095830873, -0.0475429185, -0.0180855002,
                        0.0421846844},
                    {-0.0270713326, -0.0276994482, -0.0893911272, -0.0372085199,
                        0.0332398191},
                    {-0.0295267235,  0.1145598739,  0.0082155224,  0.0932523906,
                        0.0545750260},
                    { 0.0845445022,  0.0105949445,  0.0310290195, -0.0396258235,
                        0.0049864636},
                    {-0.0088399630,  0.0189545881,  0.0030256936, -0.1071743071,
                        0.0308798887}},

                    {{-0.0736748204,  0.1093047708,  0.0833086222,  0.0749989003,
                        0.0896537676},
                    { 0.0174796991, -0.0201664530, -0.1130384058,  0.0199203752,
                        0.0047632209},
                    {-0.0784958825,  0.0911915898, -0.1046215370,  0.0936999246,
                    -0.0141570913},
                    {-0.0699632838, -0.1020562500,  0.0693636611,  0.0441951603,
                    -0.1042146608},
                    {-0.0897461325, -0.0261580031,  0.0266584475, -0.0242327619,
                    -0.0091426708}}},


                    {{{-0.0294735078, -0.0490118340, -0.0102116829, -0.0089236405,
                        0.0638454780},
                    { 0.0318225212, -0.0750555843, -0.0006857774, -0.0149815530,
                        0.0697304457},
                    { 0.0654041991, -0.0882468224,  0.1105226800,  0.0033364408,
                    -0.0604107007},
                    {-0.0374080427,  0.0036495004,  0.0383783057, -0.0263143200,
                        0.1003912762},
                    { 0.0507353209,  0.0155910300,  0.1017192900,  0.0231544450,
                    -0.1084882244}},

                    {{-0.0090856012, -0.0697057769,  0.0872633979,  0.0332706533,
                    -0.0800112858},
                    {-0.0645161867, -0.0415929221, -0.0418168269, -0.0703653470,
                    -0.0586728156},
                    {-0.0439705029,  0.1082400829,  0.0359866321, -0.0057579288,
                        0.0186352655},
                    { 0.0343357958,  0.0320450775, -0.0980449989, -0.0822830945,
                    -0.0560547598},
                    {-0.0906760171, -0.0421256870,  0.0518113375,  0.0785672292,
                        0.0198612548}},

                    {{ 0.1009553447,  0.0794672221,  0.0007000518, -0.0483656302,
                        0.0281033702},
                    {-0.0185741764,  0.0940017775,  0.0855893716,  0.1091704220,
                    -0.0968915299},
                    { 0.0257219113, -0.1015173718, -0.1027367935, -0.0880665779,
                    -0.0726886243},
                    { 0.0171099678, -0.0688400492, -0.0629827529, -0.0427498519,
                        0.0592775978},
                    { 0.0118976049, -0.0184275638,  0.0676623732, -0.0120042292,
                        0.0371227749}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array4D<float,2,4,1,1> {
                {{{{-0.1854654104}},
                    {{ 0.0502447225}},
                    {{ 0.3770641685}},
                    {{-0.2695779204}}},

                    {{{-0.1893050075}},
                    {{-0.3626379371}},
                    {{ 0.7148165107}},
                    {{-0.0087520313}}}}
            });
            conv_op.associateInput(0, myInput);
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Float32);
            conv_op.forwardDims();
            conv_op.forward();
            REQUIRE(approxEq<float>(*(conv_op.getOutput(0)),*expectedOutput, 1e-5f, 1e-6f));
        }
    }

    SECTION("kernel size [7,7]") {
        SECTION("stride [2,2], no dilation, with padding (3,3,3,3)") {
            Conv_Op<2> conv_op = Conv_Op<2>({7,7}, {2,2});
            std::shared_ptr<Tensor> myInput = std::make_shared<Tensor>(Array1D<int32_t,3*4*4> {
               {
               54, 46, 32, 24, 18, 13, 13, 17, 22, 8, 34, 37,
               37, 36, 30, 31, 28, 32, 32, 29, 29, 24, 18, 16,
               57, 63, 57, 42, 30, 20, 17, 30, 41, 52, 46, 38,
               65, 52, 60, 60, 59, 61, 65, 70, 69, 69, 71, 67
               }
            });
            myInput->resize(std::vector<std::size_t>({1,4,4,3}));
            myInput->setDataFormat(DataFormat::NHWC);
            myInput->setDataFormat(DataFormat::NCHW);
            std::shared_ptr<Tensor> myBiases = std::make_shared<Tensor>(Array1D<int32_t,1> {
                {18300}
            });
            std::shared_ptr<Tensor> myWeights = std::make_shared<Tensor>(Array4D<int32_t,1,3,7,7> {
                {{{{   0,   0,  -1,   0,   1,   0,  -1},
                    {   0,   0,   0,   1,   1,   0,  -1},
                    {   0,   0,   0,   1,   1,   1,   0},
                    {   0,   1,   1,   0,   1,   1,   0},
                    {   0,   1,   1,   1,   1,   1,   0},
                    {   0,   1,   1,   1,   1,   0,  -1},
                    {  -1,   0,   1,   2,   2,   0,  -1}},

                   {{   0,   0,  -1,   0,   0,   0,  -1},
                    {   0,   0,   0,   1,   1,   0,   0},
                    {   0,   0,   1,   1,   1,   1,   0},
                    {   0,   1,   1,   1,   1,   1,   1},
                    {   0,   1,   1,   1,   1,   1,   0},
                    {   0,   1,   1,   0,   1,   0,   0},
                    {  -1,   0,   1,   1,   1,   0,  -1}},

                   {{   0,  -1,  -1,   0,   1,   0,  -1},
                    {   0,   1,   1,   2,   2,   1,   0},
                    {   0,   1,   1,   2,   2,   1,   1},
                    {   0,   1,   1,   1,   1,   1,   2},
                    {  -1,   1,   1,   0,   1,   1,   1},
                    {  -1,   1,   1,   0,   0,   0,   0},
                    {  -1,   0,   1,   1,   1,   0,   0}}}}
            });
            std::shared_ptr<Tensor> expectedOutput = std::make_shared<Tensor>(Array1D<int32_t,1> {
                {
                   19282
                }
            });
            Pad_Op<2> pad_op = Pad_Op<2>({3,3});
            pad_op.setBackend("cpu");
            pad_op.associateInput(0,myInput);
            pad_op.setDataType(DataType::Int32);
            pad_op.forwardDims();
            pad_op.forward();

            conv_op.associateInput(0, pad_op.getOutput(0));
            conv_op.associateInput(1, myWeights);
            conv_op.associateInput(2, myBiases);
            conv_op.setBackend("cpu");
            conv_op.setDataType(DataType::Int32);
            conv_op.forwardDims();
            conv_op.forward();
            conv_op.getOutput(0)->resize(std::vector<std::size_t>({1}));
            //conv_op.getOutput(0)->print();
            //fmt::print("{:.^20}\n", "truth");
            //(*expectedOutput).print();
            REQUIRE(*(conv_op.getOutput(0)) == *expectedOutput);
            }
        }
    }
    SECTION("3D") {
        constexpr DimSize_t DIM = 3;
        SECTION("minimal test, no stride, no dilation, 1 in/outChannel") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 1;
            constexpr std::array<DimSize_t, DIM> kernelSize = {2, 2, 2};
            constexpr std::array<DimSize_t, DIM> inDataSize = {3, 3, 3};

            constexpr std::array<DimSize_t, DIM> stride = {1, 1, 1};
            constexpr std::array<DimSize_t, DIM> dilation = {1, 1, 1};
            constexpr std::array<DimSize_t, 2 * DIM> padding({0, 0, 0});

            constexpr std::array<DimSize_t, DIM> outDataSize = {2, 2, 2};

            auto inputSize = std::vector<DimSize_t>(
                {batchSize, inChannels, inDataSize[0], inDataSize[1]});

            auto input = std::make_shared<Tensor>(Array5D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1],
                                                          inDataSize[2]>(
                {{{{{{1., 2., 3.}, {4., 5., 6.}, {7., 8., 9.}},

                    {{10., 11., 12.}, {13., 14., 15.}, {16., 17., 18.}},

                    {{19., 20., 21.}, {22., 23., 24.}, {25., 26., 27.}}}}}}));
            auto weights = std::make_shared<Tensor>(
                Array5D<float,
                        outChannels,
                        inChannels,
                        kernelSize[0],
                        kernelSize[1],
                        kernelSize[2]>({{{{{{0.1, 0.2}, {0.3, 0.4}},

                                           {{0.5, 0.6}, {0.7, 0.8}}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01}}));

            auto op = setupTestConv<DIM>(batchSize,
                                         inChannels,
                                         outChannels,
                                         kernelSize,
                                         inDataSize,
                                         stride,
                                         dilation,
                                         padding,
                                         input,
                                         weights,
                                         biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = Tensor(Array5D<float,
                                                 batchSize,
                                                 outChannels,
                                                 outDataSize[0],
                                                 outDataSize[1],
                                                 outDataSize[2]>(
                {{{{{{35.610001, 39.209999}, {46.410000, 50.010002}},

                    {{68.010002, 71.610001}, {78.809998, 82.410004}}}}}}));

            CHECK(approxEq<float, float>(*op->getOutput(0), expectedOutput));
        }
        SECTION("stride & dilation, multiple outChannels") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 2;
            constexpr std::array<DimSize_t, DIM> kernelSize = {2, 2, 2};
            constexpr std::array<DimSize_t, DIM> inDataSize = {8, 8, 8};

            constexpr std::array<DimSize_t, DIM> stride = {2, 3, 4};
            constexpr std::array<DimSize_t, DIM> dilation = {4, 3, 2};
            constexpr std::array<DimSize_t, 2 * DIM> padding({0, 0, 0});

            constexpr std::array<DimSize_t, DIM> outDataSize = {2, 2, 2};

            auto inputSize = std::vector<DimSize_t>(
                {batchSize, inChannels, inDataSize[0], inDataSize[1]});

            auto input = std::make_shared<Tensor>(Array5D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1],
                                                          inDataSize[2]>(
                {{{{{{1., 2., 3., 4., 5., 6., 7., 8.},
                     {9., 10., 11., 12., 13., 14., 15., 16.},
                     {17., 18., 19., 20., 21., 22., 23., 24.},
                     {25., 26., 27., 28., 29., 30., 31., 32.},
                     {33., 34., 35., 36., 37., 38., 39., 40.},
                     {41., 42., 43., 44., 45., 46., 47., 48.},
                     {49., 50., 51., 52., 53., 54., 55., 56.},
                     {57., 58., 59., 60., 61., 62., 63., 64.}},

                    {{65., 66., 67., 68., 69., 70., 71., 72.},
                     {73., 74., 75., 76., 77., 78., 79., 80.},
                     {81., 82., 83., 84., 85., 86., 87., 88.},
                     {89., 90., 91., 92., 93., 94., 95., 96.},
                     {97., 98., 99., 100., 101., 102., 103., 104.},
                     {105., 106., 107., 108., 109., 110., 111., 112.},
                     {113., 114., 115., 116., 117., 118., 119., 120.},
                     {121., 122., 123., 124., 125., 126., 127., 128.}},

                    {{129., 130., 131., 132., 133., 134., 135., 136.},
                     {137., 138., 139., 140., 141., 142., 143., 144.},
                     {145., 146., 147., 148., 149., 150., 151., 152.},
                     {153., 154., 155., 156., 157., 158., 159., 160.},
                     {161., 162., 163., 164., 165., 166., 167., 168.},
                     {169., 170., 171., 172., 173., 174., 175., 176.},
                     {177., 178., 179., 180., 181., 182., 183., 184.},
                     {185., 186., 187., 188., 189., 190., 191., 192.}},

                    {{193., 194., 195., 196., 197., 198., 199., 200.},
                     {201., 202., 203., 204., 205., 206., 207., 208.},
                     {209., 210., 211., 212., 213., 214., 215., 216.},
                     {217., 218., 219., 220., 221., 222., 223., 224.},
                     {225., 226., 227., 228., 229., 230., 231., 232.},
                     {233., 234., 235., 236., 237., 238., 239., 240.},
                     {241., 242., 243., 244., 245., 246., 247., 248.},
                     {249., 250., 251., 252., 253., 254., 255., 256.}},

                    {{257., 258., 259., 260., 261., 262., 263., 264.},
                     {265., 266., 267., 268., 269., 270., 271., 272.},
                     {273., 274., 275., 276., 277., 278., 279., 280.},
                     {281., 282., 283., 284., 285., 286., 287., 288.},
                     {289., 290., 291., 292., 293., 294., 295., 296.},
                     {297., 298., 299., 300., 301., 302., 303., 304.},
                     {305., 306., 307., 308., 309., 310., 311., 312.},
                     {313., 314., 315., 316., 317., 318., 319., 320.}},

                    {{321., 322., 323., 324., 325., 326., 327., 328.},
                     {329., 330., 331., 332., 333., 334., 335., 336.},
                     {337., 338., 339., 340., 341., 342., 343., 344.},
                     {345., 346., 347., 348., 349., 350., 351., 352.},
                     {353., 354., 355., 356., 357., 358., 359., 360.},
                     {361., 362., 363., 364., 365., 366., 367., 368.},
                     {369., 370., 371., 372., 373., 374., 375., 376.},
                     {377., 378., 379., 380., 381., 382., 383., 384.}},

                    {{385., 386., 387., 388., 389., 390., 391., 392.},
                     {393., 394., 395., 396., 397., 398., 399., 400.},
                     {401., 402., 403., 404., 405., 406., 407., 408.},
                     {409., 410., 411., 412., 413., 414., 415., 416.},
                     {417., 418., 419., 420., 421., 422., 423., 424.},
                     {425., 426., 427., 428., 429., 430., 431., 432.},
                     {433., 434., 435., 436., 437., 438., 439., 440.},
                     {441., 442., 443., 444., 445., 446., 447., 448.}},

                    {{449., 450., 451., 452., 453., 454., 455., 456.},
                     {457., 458., 459., 460., 461., 462., 463., 464.},
                     {465., 466., 467., 468., 469., 470., 471., 472.},
                     {473., 474., 475., 476., 477., 478., 479., 480.},
                     {481., 482., 483., 484., 485., 486., 487., 488.},
                     {489., 490., 491., 492., 493., 494., 495., 496.},
                     {497., 498., 499., 500., 501., 502., 503., 504.},
                     {505., 506., 507., 508., 509., 510., 511., 512.}}}}}}));

            auto weights = std::make_shared<Tensor>(Array5D<float,
                                                            outChannels,
                                                            inChannels,
                                                            kernelSize[0],
                                                            kernelSize[1],
                                                            kernelSize[2]>(
                {{{{{{0.1, 0.2}, {0.3, 0.4}}, {{0.5, 0.6}, {0.7, 0.8}}}},

                  {{{{0.9, 1.0}, {1.1, 1.2}}, {{1.3, 1.4}, {1.5, 1.6}}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01, 0.02}}));

            auto op = setupTestConv<DIM>(batchSize,
                                         inChannels,
                                         outChannels,
                                         kernelSize,
                                         inDataSize,
                                         stride,
                                         dilation,
                                         padding,
                                         input,
                                         weights,
                                         biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = Tensor(Array5D<float,
                                                 batchSize,
                                                 outChannels,
                                                 outDataSize[0],
                                                 outDataSize[1],
                                                 outDataSize[2]>(
                {{{{{{726.010010, 740.410034}, {812.409973, 826.809998}},

                    {{1186.810059, 1201.210083}, {1273.210083, 1287.609985}}},

                   {{{1634.820068, 1674.820068}, {1874.820068, 1914.819946}},

                    {{2914.820312, 2954.820068},
                     {3154.820068, 3194.819824}}}}}}));

            CHECK(approxEq<float, float>(*op->getOutput(0), expectedOutput));
        }
    }
}

TEST_CASE("[cpu/operator] Conv(backward)", "[Conv][CPU]") {
    SECTION("1D") {
        const std::size_t DIM = 1;
        SECTION("no stride & no dilation, outChannels > inChannels") {

            const DimSize_t batchSize = 1;
            const DimSize_t inChannels = 2;
            const DimSize_t outChannels = 3;
            const DimSize_t kernelSize = 4;
            const DimSize_t inDataSize = 12;

            const DimSize_t stride = 1;
            const DimSize_t dilation = 1;
            const std::array<DimSize_t, 2 * DIM> padding({0, 0});

            auto inputSize =
                std::vector<DimSize_t>({batchSize, inChannels, inDataSize});

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize>(
                    {{{{1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.}, 
                       {1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.}}}}));

            auto weights = std::make_shared<Tensor>(
                Array3D<float, outChannels, inChannels, kernelSize>(
                    {{{{0.1, 0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1, 0.1}},
                      {{0.1, 0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1, 0.1}},
                      {{0.1, 0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1, 0.1}}}

                    }));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({0.010000, 0.010000, 0.010000}));

            auto op = setupTestConv<DIM>(
                batchSize,
                inChannels,
                outChannels,
                std::array<DimSize_t, DIM>({kernelSize}),
                std::array<DimSize_t, DIM>({inDataSize}),
                std::array<DimSize_t, DIM>({stride}),
                std::array<DimSize_t, DIM>({dilation}),
                padding,
                input,
                weights,
                biases);

            ////////////////////////////////////
            // setup gradients for backward
            auto outputGrad =
                std::make_shared<Tensor>(op->getOutput(0)->dims());
            outputGrad->setDataType(DataType::Float32);
            outputGrad->setBackend("cpu");
            constantFiller(outputGrad, 1.f);
            op->getOutput(0)->setGrad(outputGrad);

            ////////////////////////////////////
            // setup gradients for backward
            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad = std::make_shared<Tensor>(
                    Array3D<float, batchSize, inChannels, inDataSize>(
                        {{{{0.3, 0.6, 0.9, 1.2, 1.2, 1.2, 1.2, 1.2, 1.2, 0.9, 0.6, 0.3}, 
                           {0.3, 0.6, 0.9, 1.2, 1.2, 1.2, 1.2, 1.2, 1.2, 0.9, 0.6, 0.3}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                std::vector<DimSize_t> weightsSize(
                    {outChannels, inChannels, kernelSize});
                auto expectedWeightsGrad =
                    std::make_shared<Tensor>(weightsSize);
                expectedWeightsGrad->setBackend("cpu");
                expectedWeightsGrad->setDataType(DataType::Float32);
                constantFiller<float>(expectedWeightsGrad, 9.);

                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                std::vector<DimSize_t> biasesSize({outChannels});
                auto expectedBiasGrad = std::make_shared<Tensor>(biasesSize);
                expectedBiasGrad->setBackend("cpu");
                expectedBiasGrad->setDataType(DataType::Float32);
                constantFiller<float>(expectedBiasGrad, 9.);
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasGrad));
            }
        }

        SECTION("stride and no dilation, inChannel > outChannels") {
            const DimSize_t batchSize = 2;
            const DimSize_t inChannels = 3;
            const DimSize_t outChannels = 1;
            const DimSize_t kernelSize = 2;
            const DimSize_t inDataSize = 8;
            const DimSize_t stride = 3;
            const DimSize_t dilation = 1;
            const std::array<DimSize_t, 2 * DIM> padding({0, 0});

            auto inputSize =
                std::vector<DimSize_t>({batchSize, inChannels, inDataSize});

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize>(
                    {{{{1., 1., 1., 1., 1., 1., 1., 1.},
                       {1., 1., 1., 1., 1., 1., 1., 1.},
                       {1., 1., 1., 1., 1., 1., 1., 1.}},

                      {{1., 1., 1., 1., 1., 1., 1., 1.},
                       {1., 1., 1., 1., 1., 1., 1., 1.},
                       {1., 1., 1., 1., 1., 1., 1., 1.}}}}));
            auto weights = std::make_shared<Tensor>(
                Array3D<float, outChannels, inChannels, kernelSize>(
                    {{{{0.1, 0.1},
                       {0.1, 0.1},
                       {0.1, 0.1}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({0.060000}));

            auto op = setupTestConv<DIM>(
                batchSize,
                inChannels,
                outChannels,
                std::array<DimSize_t, DIM>({kernelSize}),
                std::array<DimSize_t, DIM>({inDataSize}),
                std::array<DimSize_t, DIM>({stride}),
                std::array<DimSize_t, DIM>({dilation}),
                padding,
                input,
                weights,
                biases);

            ////////////////////////////////////
            // setup gradients for backward
            auto outputGrad =
                std::make_shared<Tensor>(op->getOutput(0)->dims());
            outputGrad->setDataType(DataType::Float32);
            outputGrad->setBackend("cpu");
            constantFiller(outputGrad, 1.f);
            op->getOutput(0)->setGrad(outputGrad);

            ////////////////////////////////////
            // setup gradients for backward
            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad = std::make_shared<Tensor>(
                    Array3D<float, batchSize, inChannels, inDataSize>(
                        {{{{0.1, 0.1, 0.0, 0.1, 0.1, 0.0, 0.1, 0.1}, 
                           {0.1, 0.1, 0.0, 0.1, 0.1, 0.0, 0.1, 0.1}, 
                           {0.1, 0.1, 0.0, 0.1, 0.1, 0.0, 0.1, 0.1}}, 

                          {{0.1, 0.1, 0.0, 0.1, 0.1, 0.0, 0.1, 0.1}, 
                           {0.1, 0.1, 0.0, 0.1, 0.1, 0.0, 0.1, 0.1}, 
                           {0.1, 0.1, 0.0, 0.1, 0.1, 0.0, 0.1, 0.1}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad = std::make_shared<Tensor>(
                    Array3D<float, outChannels, inChannels, kernelSize>(
                        {{{{6., 6.}, {6., 6.}, {6., 6.}}}}));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad = std::make_shared<Tensor>(
                    Array1D<float, outChannels>({6.}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }

        SECTION("dilation, no stride") {
            const DimSize_t batchSize = 2;
            const DimSize_t inChannels = 3;
            const DimSize_t outChannels = 1;
            const DimSize_t kernelSize = 2;
            const DimSize_t inDataSize = 8;

            const DimSize_t stride = 1;
            const DimSize_t dilation = 2;
            const std::array<DimSize_t, 2 * DIM> padding({0, 0});

            auto inputSize =
                std::vector<DimSize_t>({batchSize, inChannels, inDataSize});

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize>(
                    {{{{1., 1., 1., 1., 1., 1., 1., 1.},
                       {1., 1., 1., 1., 1., 1., 1., 1.},
                       {1., 1., 1., 1., 1., 1., 1., 1.}},

                      {{1., 1., 1., 1., 1., 1., 1., 1.},
                       {1., 1., 1., 1., 1., 1., 1., 1.},
                       {1., 1., 1., 1., 1., 1., 1., 1.}}}}));
            auto weights = std::make_shared<Tensor>(
                Array3D<float, outChannels, inChannels, kernelSize>(
                    {{{{0.1, 0.1},
                       {0.1, 0.1},
                       {0.1, 0.1}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({0.06}));

            auto op = setupTestConv<DIM>(
                batchSize,
                inChannels,
                outChannels,
                std::array<DimSize_t, DIM>({kernelSize}),
                std::array<DimSize_t, DIM>({inDataSize}),
                std::array<DimSize_t, DIM>({stride}),
                std::array<DimSize_t, DIM>({dilation}),
                padding,
                input,
                weights,
                biases);

            ////////////////////////////////////
            // setup gradients for backward
            auto outputGrad =
                std::make_shared<Tensor>(op->getOutput(0)->dims());
            outputGrad->setDataType(DataType::Float32);
            outputGrad->setBackend("cpu");
            constantFiller(outputGrad, 1.f);
            op->getOutput(0)->setGrad(outputGrad);

            ////////////////////////////////////
            // setup gradients for backward
            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad = std::make_shared<Tensor>(
                    Array3D<float, batchSize, inChannels, inDataSize>(
                        {{{{0.1, 0.1, 0.2, 0.2, 0.2, 0.2, 0.1, 0.1}, 
                           {0.1, 0.1, 0.2, 0.2, 0.2, 0.2, 0.1, 0.1}, 
                           {0.1, 0.1, 0.2, 0.2, 0.2, 0.2, 0.1, 0.1}}, 

                          {{0.1, 0.1, 0.2, 0.2, 0.2, 0.2, 0.1, 0.1}, 
                           {0.1, 0.1, 0.2, 0.2, 0.2, 0.2, 0.1, 0.1}, 
                           {0.1, 0.1, 0.2, 0.2, 0.2, 0.2, 0.1, 0.1}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad = std::make_shared<Tensor>(
                    Array3D<float, outChannels, inChannels, kernelSize>(
                        {{{{12., 12.}, {12., 12.}, {12., 12.}}}}));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad = std::make_shared<Tensor>(
                    Array1D<float, outChannels>({12.}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }
        SECTION("stride & dilation") {
            const DimSize_t batchSize = 1;
            const DimSize_t inChannels = 4;
            const DimSize_t outChannels = 4;
            const DimSize_t kernelSize = 3;
            const DimSize_t inDataSize = 13;

            const DimSize_t stride = 4;
            const DimSize_t dilation = 3;
            const std::array<DimSize_t, 2 * DIM> padding({0, 0});

            auto inputSize =
                std::vector<DimSize_t>({batchSize, inChannels, inDataSize});

            auto input = std::make_shared<
                Tensor>(Array3D<float, batchSize, inChannels, inDataSize>(
                {{{{1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.},
                   {1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.},
                   {1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.},
                   {1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1., 1.}}}}));
            auto weights = std::make_shared<Tensor>(
                Array3D<float, outChannels, inChannels, kernelSize>(
                    {{{{0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1}},

                      {{0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1}},

                      {{0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1}},

                      {{0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1},
                       {0.1, 0.1, 0.1}}}}));

            auto biases = std::make_shared<Tensor>(Array1D<float, outChannels>(
                {{0.01, 0.01, 0.01, 0.01}}));

            auto op = setupTestConv<DIM>(
                batchSize,
                inChannels,
                outChannels,
                std::array<DimSize_t, DIM>({kernelSize}),
                std::array<DimSize_t, DIM>({inDataSize}),
                std::array<DimSize_t, DIM>({stride}),
                std::array<DimSize_t, DIM>({dilation}),
                padding,
                input,
                weights,
                biases);

            ////////////////////////////////////
            // setup gradients for backward
            auto outputGrad =
                std::make_shared<Tensor>(op->getOutput(0)->dims());
            outputGrad->setDataType(DataType::Float32);
            outputGrad->setBackend("cpu");
            constantFiller(outputGrad, 1.f);
            op->getOutput(0)->setGrad(outputGrad);

            ////////////////////////////////////
            // setup gradients for backward
            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad = std::make_shared<Tensor>(
                    Array3D<float, batchSize, inChannels, inDataSize>(
                        {{{{0.4,0.,0.,0.4,0.4,0.,0.4,0.4,0.,0.,0.4,0.,0.},
                           {0.4,0.,0.,0.4,0.4,0.,0.4,0.4,0.,0.,0.4,0.,0.},
                           {0.4,0.,0.,0.4,0.4,0.,0.4,0.4,0.,0.,0.4,0.,0.},
                           {0.4,0.,0.,0.4,0.4,0.,0.4,0.4,0.,0.,0.4,0.,0.}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad = std::make_shared<Tensor>(
                    Array3D<float, outChannels, inChannels, kernelSize>(
                        {{{{2., 2., 2.},
                           {2., 2., 2.},
                           {2., 2., 2.},
                           {2., 2., 2.}},

                          {{2., 2., 2.},
                           {2., 2., 2.},
                           {2., 2., 2.},
                           {2., 2., 2.}},

                          {{2., 2., 2.},
                           {2., 2., 2.},
                           {2., 2., 2.},
                           {2., 2., 2.}},

                          {{2., 2., 2.},
                           {2., 2., 2.},
                           {2., 2., 2.},
                           {2., 2., 2.}}}}));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad = std::make_shared<Tensor>(
                    Array1D<float, outChannels>({{2., 2., 2., 2.}}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }

        // Harder to read, look at previous tests in case of issue
        SECTION("Sequential values") {
            const DimSize_t batchSize = 1;
            const DimSize_t inChannels = 2;
            const DimSize_t outChannels = 2;
            const DimSize_t kernelSize = 3;
            const DimSize_t inDataSize = 8;

            const DimSize_t stride = 2;
            const DimSize_t dilation = 2;
            const std::array<DimSize_t, 2 * DIM> padding({0, 0});

            const DimSize_t outDataSize = 2;

            auto inputSize =
                std::vector<DimSize_t>({batchSize, inChannels, inDataSize});

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize>(
                    {{{{1., 2., 3., 4., 5., 6., 7., 8.},
                       {9., 10., 11., 12., 13., 14., 15., 16.}}}}));
            auto weights = std::make_shared<Tensor>(
                Array3D<float, outChannels, inChannels, kernelSize>(
                    {{{{0.1, 0.2, 0.3}, {0.4, 0.5, 0.6}},

                      {{0.7, 0.8, 0.9}, {1.0, 1.1, 1.2}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.0100, 0.0200}}));

            auto outputGrad = std::make_shared<Tensor>(
                Array3D<float, batchSize, outChannels, outDataSize>(
                    {{{{1., 2.}, {3., 4.}}}}));

            auto op = setupTestConv<DIM>(
                batchSize,
                inChannels,
                outChannels,
                std::array<DimSize_t, DIM>({kernelSize}),
                std::array<DimSize_t, DIM>({inDataSize}),
                std::array<DimSize_t, DIM>({stride}),
                std::array<DimSize_t, DIM>({dilation}),
                padding,
                input,
                weights,
                biases);

            ////////////////////////////////////
            // setup gradients for backward
            op->getOutput(0)->setGrad(outputGrad);

            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad = std::make_shared<Tensor>(
                    Array3D<float, batchSize, inChannels, inDataSize>(
                        {{{{2.2,0.,5.6,0.,6.6,0.,4.2,0.},
                           {3.4,0.,8.6,0.,9.6,0.,6.,0.}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad = std::make_shared<Tensor>(
                    Array3D<float, outChannels, inChannels, kernelSize>(
                        {{{{7., 13., 19.}, {31., 37., 43.}},

                          {{15., 29., 43.}, {71., 85., 99.}}}}));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad = std::make_shared<Tensor>(
                    Array1D<float, outChannels>({{3., 7.}}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }
        SECTION("random values testing") {
            const DimSize_t batchSize = 1;
            const DimSize_t inChannels = 4;
            const DimSize_t outChannels = 4;
            const DimSize_t kernelSize = 3;
            const DimSize_t inDataSize = 13;
            const DimSize_t outDataSize = 2;

            const DimSize_t stride = 4;
            const DimSize_t dilation = 3;
            const std::array<DimSize_t, 2 * DIM> padding({0, 0});

            auto inputSize =
                std::vector<DimSize_t>({batchSize, inChannels, inDataSize});

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize>(
                    {{{{0.180772,
                        -0.069988,
                        -0.359623,
                        -0.915204,
                        0.625765,
                        0.025510,
                        0.954514,
                        0.064349,
                        0.361151,
                        1.167878,
                        -1.349893,
                        -0.510177,
                        0.235958},
                       {-0.239778,
                        -0.921115,
                        1.543297,
                        1.348826,
                        -0.139642,
                        0.285797,
                        0.965120,
                        -2.037150,
                        0.493136,
                        1.486999,
                        0.591033,
                        0.126030,
                        -1.562687},
                       {-1.160103,
                        -0.334841,
                        0.447772,
                        -0.801645,
                        1.523611,
                        2.508587,
                        -0.663096,
                        -0.251275,
                        1.010145,
                        0.121547,
                        -1.510835,
                        2.104773,
                        2.762959},
                       {-1.746529,
                        0.410919,
                        -0.242185,
                        0.420812,
                        0.277596,
                        0.778898,
                        1.533269,
                        1.609736,
                        -0.403228,
                        -0.274928,
                        1.473840,
                        0.068826,
                        1.332708}}}}));
            auto weights = std::make_shared<Tensor>(
                Array3D<float, outChannels, inChannels, kernelSize>(
                    {{{{0.587285, 0.286069, 0.008287},
                       {-0.252325, -1.324722, 0.189178},
                       {0.021100, 0.940420, -0.557690},
                       {-0.693927, -0.325247, 1.243933}},

                      {{-1.167186, -0.409124, 1.260062},
                       {-1.563006, 1.134614, -0.082384},
                       {0.289316, 0.835773, -0.244991},
                       {0.271223, 0.093636, -0.883432}},

                      {{-0.327417, 0.078394, -0.380766},
                       {0.377508, 0.111912, 2.314279},
                       {-0.798906, -0.564303, -1.134660},
                       {0.170527, 0.994665, 1.262572}},

                      {{1.621816, 1.077471, 0.594781},
                       {-1.529087, 2.043707, -0.165627},
                       {0.087070, -0.527656, -0.100288},
                       {1.053922, -0.623074, -1.590572}}}}));

            auto biases = std::make_shared<Tensor>(Array1D<float, outChannels>(
                {{1.285940, -0.051787, -0.968103, -0.586324}}));

            auto op = setupTestConv<DIM>(
                batchSize,
                inChannels,
                outChannels,
                std::array<DimSize_t, DIM>({kernelSize}),
                std::array<DimSize_t, DIM>({inDataSize}),
                std::array<DimSize_t, DIM>({stride}),
                std::array<DimSize_t, DIM>({dilation}),
                padding,
                input,
                weights,
                biases);

            ////////////////////////////////////
            // setup gradients for backward
            auto outputGrad = std::make_shared<Tensor>(
                Array3D<float, batchSize, outChannels, outDataSize>(
                    {{{{0.053156, 1.189073},
                       {0.100228, 1.042344},
                       {-1.468991, 0.581337},
                       {1.330418, 0.487802}}}}));
            op->getOutput(0)->setGrad(outputGrad);

            ////////////////////////////////////
            // setup gradients for backward
            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad = std::make_shared<Tensor>(
                    Array3D<float, batchSize, inChannels, inDataSize>(
                        {{{{2.552898,
                            0.000000,
                            0.000000,
                            1.292528,
                            0.082501,
                            0.000000,
                            1.477383,
                            0.484875,
                            0.000000,
                            0.000000,
                            1.392054,
                            0.000000,
                            0.000000},
                           {-2.758950,
                            0.000000,
                            0.000000,
                            2.597889,
                            -2.455656,
                            0.000000,
                            -3.618210,
                            0.669449,
                            0.000000,
                            0.000000,
                            1.403657,
                            0.000000,
                            0.000000},
                           {1.319545,
                            0.000000,
                            0.000000,
                            0.260710,
                            -0.095303,
                            0.000000,
                            1.479181,
                            1.403949,
                            0.000000,
                            0.000000,
                            -1.627040,
                            0.000000,
                            0.000000},
                           {1.141951,
                            0.000000,
                            0.000000,
                            -2.298007,
                            0.070817,
                            0.000000,
                            -3.993255,
                            -0.014843,
                            0.000000,
                            0.000000,
                            0.516383,
                            0.000000,
                            0.000000}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad,
                                             1e-5,
                                             1e-6));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad = std::make_shared<Tensor>(
                    Array3D<float, outChannels, inChannels, kernelSize>(
                        {{{{0.753690, 0.027866, -1.554383},
                           {-0.178790, -2.350622, 0.754084},
                           {1.750019, -0.341397, -1.831741},
                           {0.237243, 1.936463, 1.834007}},

                          {{0.670381, -0.024656, -1.311384},
                           {-0.169587, -1.988220, 0.712792},
                           {1.471852, -0.342263, -1.641270},
                           {0.114300, 1.720076, 1.689925}},

                          {{0.098228, 1.381835, -2.186914},
                           {0.271054, -3.165683, -1.074165},
                           {2.589912, 1.031534, 0.095779},
                           {2.727013, 0.317630, -1.395561}},

                          {{0.545751, -1.186215, 0.611421},
                           {-0.387123, 0.800776, 1.572321},
                           {-0.800201, -1.189095, -1.619183},
                           {-2.188202, 1.345088, 2.758830}}}

                        }));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad,
                                             1e-5,
                                             1e-6));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad =
                    std::make_shared<Tensor>(Array1D<float, outChannels>(
                        {{1.242230, 1.142572, -0.887655, 1.818220}}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }
    }
    SECTION("2D") {
        const DimSize_t DIM = 2;
        SECTION("Sequential values") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 2;
            constexpr std::array<DimSize_t, DIM> kernelSize = {1, 2};
            constexpr std::array<DimSize_t, DIM> inDataSize = {3, 4};

            constexpr std::array<DimSize_t, DIM> stride = {1, 2};
            constexpr std::array<DimSize_t, DIM> dilation = {1, 2};
            constexpr std::array<DimSize_t, 2 * DIM> padding({0, 0});

            constexpr std::array<DimSize_t, DIM> outDataSize = {3, 1};

            auto inputSize = std::vector<DimSize_t>(
                {batchSize, inChannels, inDataSize[0], inDataSize[1]});

            auto input = std::make_shared<Tensor>(
                Array4D<float,
                        batchSize,
                        inChannels,
                        inDataSize[0],
                        inDataSize[1]>({{{{{1., 2., 3., 4.},
                                           {5., 6., 7., 8.},
                                           {9., 10., 11., 12.}}}}}));
            auto weights = std::make_shared<Tensor>(
                Array4D<float,
                        outChannels,
                        inChannels,
                        kernelSize[0],
                        kernelSize[1]>({{{{{1., 2.}}}, {{{3., 4.}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{1., 2.}}));

            auto outputGrad = std::make_shared<Tensor>(Array4D<float,
                                                               batchSize,
                                                               outChannels,
                                                               outDataSize[0],
                                                               outDataSize[1]>(
                {{{{{1.}, {2.}, {3.}}, {{4.}, {5.}, {6.}}}}}));

            auto op = setupTestConv<DIM>(batchSize,
                                                 inChannels,
                                                 outChannels,
                                                 kernelSize,
                                                 inDataSize,
                                                 stride,
                                                 dilation,
                                                 padding,
                                                 input,
                                                 weights,
                                                 biases);

            ////////////////////////////////////
            // setup gradients for backward
            op->getOutput(0)->setGrad(outputGrad);

            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad = std::make_shared<Tensor>(
                    Array4D<float,
                            batchSize,
                            inChannels,
                            inDataSize[0],
                            inDataSize[1]>({{{{{13., 0., 18., 0.},
                                               {17., 0., 24., 0.},
                                               {21., 0., 30., 0.}}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad =
                    std::make_shared<Tensor>(Array4D<float,
                                                     outChannels,
                                                     inChannels,
                                                     kernelSize[0],
                                                     kernelSize[1]>(
                        {{{{{38., 50.}}}, {{{83., 113.}}}}}));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad = std::make_shared<Tensor>(
                    Array1D<float, outChannels>({{6., 15.}}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }
    }
    SECTION("3D") {
        constexpr DimSize_t DIM = 3;
        SECTION("basic test, square kernel, stride, dilation") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 1;
            constexpr std::array<DimSize_t, DIM> kernelSize = {2, 2, 2};
            constexpr std::array<DimSize_t, DIM> inDataSize = {4, 4, 4};

            constexpr std::array<DimSize_t, DIM> stride = {2, 2, 2};
            constexpr std::array<DimSize_t, DIM> dilation = {2, 2, 2};
            constexpr std::array<DimSize_t, 2 * DIM> padding({0, 0});

            constexpr std::array<DimSize_t, DIM> outDataSize = {1, 1, 1};

            auto inputSize = std::vector<DimSize_t>({batchSize,
                                                     inChannels,
                                                     inDataSize[0],
                                                     inDataSize[1],
                                                     inDataSize[2]});

            auto input = std::make_shared<Tensor>(
                Array5D<float,
                        batchSize,
                        inChannels,
                        inDataSize[0],
                        inDataSize[1],
                        inDataSize[2]>({{{{{{1., 2., 3., 4.},
                                            {5., 6., 7., 8.},
                                            {9., 10., 11., 12.},
                                            {13., 14., 15., 16.}},

                                           {{17., 18., 19., 20.},
                                            {21., 22., 23., 24.},
                                            {25., 26., 27., 28.},
                                            {29., 30., 31., 32.}},

                                           {{33., 34., 35., 36.},
                                            {37., 38., 39., 40.},
                                            {41., 42., 43., 44.},
                                            {45., 46., 47., 48.}},

                                           {{49., 50., 51., 52.},
                                            {53., 54., 55., 56.},
                                            {57., 58., 59., 60.},
                                            {61., 62., 63., 64.}}}}}}));

            auto weights = std::make_shared<Tensor>(
                Array5D<float,
                        outChannels,
                        inChannels,
                        kernelSize[0],
                        kernelSize[1],
                        kernelSize[2]>({{{{{{0.1, 0.2}, {0.3, 0.4}},

                                           {{0.5, 0.6}, {0.7, 0.8}}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01}}));

            auto outputGrad = std::make_shared<Tensor>(
                Array5D<float,
                        batchSize,
                        outChannels,
                        outDataSize[0],
                        outDataSize[1],
                        outDataSize[2]>({{{{{{1.}}}}}}));

            auto op = setupTestConv<DIM>(batchSize,
                                         inChannels,
                                         outChannels,
                                         kernelSize,
                                         inDataSize,
                                         stride,
                                         dilation,
                                         padding,
                                         input,
                                         weights,
                                         biases);

            ////////////////////////////////////
            // setup gradients for backward
            op->getOutput(0)->setGrad(outputGrad);

            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad = std::make_shared<Tensor>(
                    Array5D<float,
                            batchSize,
                            inChannels,
                            inDataSize[0],
                            inDataSize[1],
                            inDataSize[2]>({{{{{{0.1, 0.0, 0.2, 0.0},
                                                {0.0, 0.0, 0.0, 0.0},
                                                {0.3, 0.0, 0.4, 0.0},
                                                {0.0, 0.0, 0.0, 0.0}},

                                               {{0.0, 0.0, 0.0, 0.0},
                                                {0.0, 0.0, 0.0, 0.0},
                                                {0.0, 0.0, 0.0, 0.0},
                                                {0.0, 0.0, 0.0, 0.0}},

                                               {{0.5, 0.0, 0.6, 0.0},
                                                {0.0, 0.0, 0.0, 0.0},
                                                {0.7, 0.0, 0.8, 0.0},
                                                {0.0, 0.0, 0.0, 0.0}},

                                               {{0.0, 0.0, 0.0, 0.0},
                                                {0.0, 0.0, 0.0, 0.0},
                                                {0.0, 0.0, 0.0, 0.0},
                                                {0.0, 0.0, 0.0, 0.0}}}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad = std::make_shared<Tensor>(
                    Array5D<float,
                            outChannels,
                            inChannels,
                            kernelSize[0],
                            kernelSize[1],
                            kernelSize[2]>({{{{{{1., 3.}, {9., 11.}},

                                               {{33., 35.}, {41., 43.}}}}}}));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad = std::make_shared<Tensor>(
                    Array1D<float, outChannels>({{1.}}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }
        SECTION("square kernel, multiple in/out channels") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 2;
            constexpr DimSize_t outChannels = 1;
            constexpr std::array<DimSize_t, DIM> kernelSize = {2, 2, 2};
            constexpr std::array<DimSize_t, DIM> inDataSize = {2, 2, 2};

            constexpr std::array<DimSize_t, DIM> stride = {1, 1, 1};
            constexpr std::array<DimSize_t, DIM> dilation = {1, 1, 1};
            constexpr std::array<DimSize_t, 2 * DIM> padding({0, 0});

            constexpr std::array<DimSize_t, DIM> outDataSize = {1, 1, 1};

            auto inputSize = std::vector<DimSize_t>({batchSize,
                                                     inChannels,
                                                     inDataSize[0],
                                                     inDataSize[1],
                                                     inDataSize[2]});

            auto input = std::make_shared<Tensor>(Array5D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1],
                                                          inDataSize[2]>(
                {{{{{{1.000000, 2.000000}, {3.000000, 4.000000}},

                    {{5.000000, 6.000000}, {7.000000, 8.000000}}},

                   {{{9.000000, 10.000000}, {11.000000, 12.000000}},

                    {{13.000000, 14.000000}, {15.000000, 16.000000}}}}}}));

            auto weights = std::make_shared<Tensor>(Array5D<float,
                                                            outChannels,
                                                            inChannels,
                                                            kernelSize[0],
                                                            kernelSize[1],
                                                            kernelSize[2]>(
                {{{{{{0.100000, 0.200000}, {0.300000, 0.400000}},

                    {{0.500000, 0.600000}, {0.700000, 0.800000}}},

                   {{{0.900000, 1.000000}, {1.100000, 1.200000}},

                    {{1.300000, 1.400000}, {1.500000, 1.600000}}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.010000}}));

            auto outputGrad = std::make_shared<Tensor>(
                Array5D<float,
                        batchSize,
                        outChannels,
                        outDataSize[0],
                        outDataSize[1],
                        outDataSize[2]>({{{{{{1.000000}}}}}}));

            auto op = setupTestConv<DIM>(batchSize,
                                         inChannels,
                                         outChannels,
                                         kernelSize,
                                         inDataSize,
                                         stride,
                                         dilation,
                                         padding,
                                         input,
                                         weights,
                                         biases);

            ////////////////////////////////////
            // setup gradients for backward
            op->getOutput(0)->setGrad(outputGrad);

            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad =
                    std::make_shared<Tensor>(Array5D<float,
                                                     batchSize,
                                                     inChannels,
                                                     inDataSize[0],
                                                     inDataSize[1],
                                                     inDataSize[2]>(
                        {{{{{{0.100000, 0.200000}, {0.300000, 0.400000}},

                            {{0.500000, 0.600000}, {0.700000, 0.800000}}},

                           {{{0.900000, 1.000000}, {1.100000, 1.200000}},

                            {{1.300000, 1.400000}, {1.500000, 1.600000}}}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad =
                    std::make_shared<Tensor>(Array5D<float,
                                                     outChannels,
                                                     inChannels,
                                                     kernelSize[0],
                                                     kernelSize[1],
                                                     kernelSize[2]>(
                        {{{{{{1.000000, 2.000000}, {3.000000, 4.000000}},

                            {{5.000000, 6.000000}, {7.000000, 8.000000}}},

                           {{{9.000000, 10.000000}, {11.000000, 12.000000}},

                            {{13.000000, 14.000000},
                             {15.000000, 16.000000}}}}}}));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad = std::make_shared<Tensor>(
                    Array1D<float, outChannels>({{1.000000}}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }
        SECTION("non square kernel, stride, dilation, multiple "
                "in/outchannels") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 2;
            constexpr DimSize_t outChannels = 2;
            constexpr std::array<DimSize_t, DIM> kernelSize = {1, 2, 3};
            constexpr std::array<DimSize_t, DIM> inDataSize = {5, 5, 5};

            constexpr std::array<DimSize_t, DIM> stride = {1, 2, 3};
            constexpr std::array<DimSize_t, DIM> dilation = {3, 2, 1};
            constexpr std::array<DimSize_t, 2 * DIM> padding({0, 0});

            constexpr std::array<DimSize_t, DIM> outDataSize = {5, 2, 1};

            auto inputSize = std::vector<DimSize_t>({batchSize,
                                                     inChannels,
                                                     inDataSize[0],
                                                     inDataSize[1],
                                                     inDataSize[2]});

            auto input = std::make_shared<Tensor>(Array5D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1],
                                                          inDataSize[2]>(
                {{{{{{1., 2., 3., 4., 5.},
                     {6., 7., 8., 9., 10.},
                     {11., 12., 13., 14., 15.},
                     {16., 17., 18., 19., 20.},
                     {21., 22., 23., 24., 25.}},

                    {{26., 27., 28., 29., 30.},
                     {31., 32., 33., 34., 35.},
                     {36., 37., 38., 39., 40.},
                     {41., 42., 43., 44., 45.},
                     {46., 47., 48., 49., 50.}},

                    {{51., 52., 53., 54., 55.},
                     {56., 57., 58., 59., 60.},
                     {61., 62., 63., 64., 65.},
                     {66., 67., 68., 69., 70.},
                     {71., 72., 73., 74., 75.}},

                    {{76., 77., 78., 79., 80.},
                     {81., 82., 83., 84., 85.},
                     {86., 87., 88., 89., 90.},
                     {91., 92., 93., 94., 95.},
                     {96., 97., 98., 99., 100.}},

                    {{101., 102., 103., 104., 105.},
                     {106., 107., 108., 109., 110.},
                     {111., 112., 113., 114., 115.},
                     {116., 117., 118., 119., 120.},
                     {121., 122., 123., 124., 125.}}},

                   {{{126., 127., 128., 129., 130.},
                     {131., 132., 133., 134., 135.},
                     {136., 137., 138., 139., 140.},
                     {141., 142., 143., 144., 145.},
                     {146., 147., 148., 149., 150.}},

                    {{151., 152., 153., 154., 155.},
                     {156., 157., 158., 159., 160.},
                     {161., 162., 163., 164., 165.},
                     {166., 167., 168., 169., 170.},
                     {171., 172., 173., 174., 175.}},

                    {{176., 177., 178., 179., 180.},
                     {181., 182., 183., 184., 185.},
                     {186., 187., 188., 189., 190.},
                     {191., 192., 193., 194., 195.},
                     {196., 197., 198., 199., 200.}},

                    {{201., 202., 203., 204., 205.},
                     {206., 207., 208., 209., 210.},
                     {211., 212., 213., 214., 215.},
                     {216., 217., 218., 219., 220.},
                     {221., 222., 223., 224., 225.}},

                    {{226., 227., 228., 229., 230.},
                     {231., 232., 233., 234., 235.},
                     {236., 237., 238., 239., 240.},
                     {241., 242., 243., 244., 245.},
                     {246., 247., 248., 249., 250.}}}}}}));

            auto weights = std::make_shared<Tensor>(Array5D<float,
                                                            outChannels,
                                                            inChannels,
                                                            kernelSize[0],
                                                            kernelSize[1],
                                                            kernelSize[2]>(
                {{{{{{0.1, 0.2, 0.3}, {0.4, 0.5, 0.6}}},

                   {{{0.7, 0.8, 0.9}, {1.0, 1.1, 1.2}}}},

                  {{{{1.3, 1.4, 1.5}, {1.6, 1.7, 1.8}}},

                   {{{1.9, 2.0, 2.1}, {2.2, 2.3, 2.4}}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01, 0.02}}));

            auto outputGrad = std::make_shared<Tensor>(
                Array5D<float,
                        batchSize,
                        outChannels,
                        outDataSize[0],
                        outDataSize[1],
                        outDataSize[2]>({{{{{{1.}, {2.}},

                                            {{3.}, {4.}},

                                            {{5.}, {6.}},

                                            {{7.}, {8.}},

                                            {{9.}, {10.}}},

                                           {{{11.}, {12.}},

                                            {{13.}, {14.}},

                                            {{15.}, {16.}},

                                            {{17.}, {18.}},

                                            {{19.}, {20.}}}}}}));

            auto op = setupTestConv<DIM>(batchSize,
                                         inChannels,
                                         outChannels,
                                         kernelSize,
                                         inDataSize,
                                         stride,
                                         dilation,
                                         padding,
                                         input,
                                         weights,
                                         biases);

            ////////////////////////////////////
            // setup gradients for backward
            op->getOutput(0)->setGrad(outputGrad);

            REQUIRE_NOTHROW(op->backward());

            SECTION("Input Grad") {
                auto expectedInputGrad =
                    std::make_shared<Tensor>(Array5D<float,
                                                     batchSize,
                                                     inChannels,
                                                     inDataSize[0],
                                                     inDataSize[1],
                                                     inDataSize[2]>(
                        {{{{{{14.400001, 15.599999, 16.799999, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {33.800003, 36.400002, 39., 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {20., 21.400002, 22.800001, 0., 0.}},

                            {{17.200001, 18.799999, 20.400000, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {40.599998, 44., 47.400002, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {24., 25.800001, 27.600000, 0., 0.}},

                            {{20.000002, 22., 24., 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {47.400002, 51.599998, 55.800003, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {28., 30.200001, 32.400002, 0., 0.}},

                            {{22.800001, 25.199999, 27.600000, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {54.200001, 59.200001, 64.200005, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {32., 34.600002, 37.200001, 0., 0.}},

                            {{25.600002, 28.400000, 31.200001, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {61., 66.800003, 72.600006, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {36., 39., 42., 0., 0.}}},

                           {{{21.600000, 22.799999, 24.000002, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {49.400002, 52., 54.600002, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {28.400002, 29.799999, 31.200001, 0., 0.}},

                            {{26.799999, 28.400000, 30.000002, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {61., 64.400002, 67.800003, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {34.799999, 36.599998, 38.400002, 0., 0.}},

                            {{32., 34., 36.000004, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {72.599998, 76.800003, 81., 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {41.200001, 43.400002, 45.600002, 0., 0.}},

                            {{37.200001, 39.599998, 42.000004, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {84.199997, 89.199997, 94.200005, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {47.600002, 50.200001, 52.800003, 0., 0.}},

                            {{42.399998, 45.200001, 48.000004, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {95.800003, 101.599998, 107.400009, 0., 0.},
                             {0., 0., 0., 0., 0.},
                             {54., 57., 60., 0., 0.}}}}}}));
                CHECK(approxEq<float, float>(*op->getInput(0)->grad(),
                                             *expectedInputGrad));
            }
            SECTION("Weight grad") {
                auto expectedWeightsGrad = std::make_shared<
                    Tensor>(Array5D<float,
                                    outChannels,
                                    inChannels,
                                    kernelSize[0],
                                    kernelSize[1],
                                    kernelSize[2]>(
                    {{{{{{4105., 4160., 4215.}, {4655., 4710., 4765.}}},

                       {{{10980., 11035., 11090.}, {11530., 11585., 11640.}}}},

                      {{{{9705., 9860., 10015.}, {11255., 11410., 11565.}}},

                       {{{29080., 29235., 29390.},
                         {30630., 30785., 30940.}}}}}}));
                CHECK(approxEq<float, float>(*op->getInput(1)->grad(),
                                             *expectedWeightsGrad));
            }
            SECTION("Bias Grad") {
                auto expectedBiasesGrad = std::make_shared<Tensor>(
                    Array1D<float, outChannels>({{55., 155.}}));
                CHECK(approxEq<float, float>(*op->getInput(2)->grad(),
                                             *expectedBiasesGrad));
            }
        }
    }
}

} // namespace Aidge
