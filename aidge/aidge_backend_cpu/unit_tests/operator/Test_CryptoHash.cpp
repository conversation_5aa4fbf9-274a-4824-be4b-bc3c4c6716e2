/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <cmath>    // std::abs
#include <cstddef>  // std::size_t
#include <memory>

#include <catch2/catch_test_macros.hpp>

#include "aidge/backend/cpu/operator/CryptoHashImpl.hpp"
#include "aidge/data/Data.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/graph/Node.hpp"
#include "aidge/operator/CryptoHash.hpp"
#include "aidge/utils/ArrayHelpers.hpp"

using namespace Aidge;

#ifdef WITH_OPENSSL
TEST_CASE("[cpu/operator] CryptoHash(forward)") {
  SECTION("1D Tensor") {
    std::shared_ptr<Tensor> input0 =
        std::make_shared<Tensor>(Array1D<uint8_t, 5>{
            {'a', 'b', 'c', 'd', 'e'}});
    std::shared_ptr<Tensor> expectedOutput =
        std::make_shared<Tensor>(Array1D<uint8_t, 32>{
            {0x36, 0xbb, 0xe5, 0x0e, 0xd9, 0x68, 0x41, 0xd1,
             0x04, 0x43, 0xbc, 0xb6, 0x70, 0xd6, 0x55, 0x4f,
             0x0a, 0x34, 0xb7, 0x61, 0xbe, 0x67, 0xec, 0x9c,
             0x4a, 0x8a, 0xd2, 0xc0, 0xc4, 0x4c, 0xa4, 0x2c}});

    std::shared_ptr<Node> myCryptoHash = CryptoHash();
    auto op = std::static_pointer_cast<CryptoHash_Op>(myCryptoHash->getOperator());
    op->associateInput(0, input0);
    op->setDataType(DataType::UInt8);
    op->setBackend("cpu");
    myCryptoHash->forward();

    REQUIRE(op->getOutput(0)->size() == 32);

    uint8_t* resPtr = static_cast<uint8_t*>(op->getOutput(0)->getImpl()->rawPtr());
    uint8_t* expectedPtr = static_cast<uint8_t*>(expectedOutput->getImpl()->rawPtr());
    for (std::size_t i = 0; i < expectedOutput->size(); ++i) {
      REQUIRE(resPtr[i] == expectedPtr[i]);
    }
  }
}
#endif
