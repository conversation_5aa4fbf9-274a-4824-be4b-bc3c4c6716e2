/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <aidge/utils/Types.h>
#include <memory>

#include <catch2/catch_test_macros.hpp>
#include <fmt/core.h>

#include "aidge/backend/cpu/operator/ConvTransposeImpl.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/operator/ConvTranspose.hpp"
#include "aidge/utils/TensorUtils.hpp"

namespace Aidge {

template <DimSize_t DIM>
static std::shared_ptr<OperatorTensor>
setupTestConvTranspose(const DimSize_t batchSize,
                       const DimSize_t inChannels,
                       const DimSize_t outChannels,
                       const std::array<DimSize_t, DIM> kernelSize,
                       const std::array<DimSize_t, DIM> dataSize,
                       const std::array<DimSize_t, DIM> stride,
                       const std::array<DimSize_t, DIM> dilation,
                       const std::shared_ptr<Tensor> input,
                       const std::shared_ptr<Tensor> weights,
                       const std::shared_ptr<Tensor> biases) {
    std::shared_ptr<Node> convTransposeNode;
    convTransposeNode = ConvTranspose(inChannels,
                                      outChannels,
                                      kernelSize,
                                      stride,
                                      dilation,
                                      false,
                                      "myconv");
    auto op = std::static_pointer_cast<OperatorTensor>(
        convTransposeNode->getOperator());

    op->associateInput(0, input);
    op->setDataType(DataType::Float32);

    input->setBackend("cpu");
    op->setBackend("cpu");

    weights->setBackend("cpu");
    op->associateInput(1, weights);

    biases->setBackend("cpu");
    op->associateInput(2, biases);

    REQUIRE_NOTHROW(op->forwardDims(true));

    return op;
}

TEST_CASE("[cpu/operator] ConvTranspose(forward)", "[ConvTranspose][CPU]") {
    constexpr DimSize_t DIM = 1;
    SECTION("1D") {
        SECTION("kernel = 2 , in/outChannels = 1") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 1;

            constexpr std::array<DimSize_t, DIM> kernelSize{2};

            constexpr std::array<DimSize_t, DIM> inDataSize{4};
            constexpr std::array<DimSize_t, DIM> outDataSize{5};

            constexpr std::array<DimSize_t, DIM> stride{1};
            constexpr std::array<DimSize_t, DIM> dilation{1};

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize[0]>(
                    {{{{1.000000, 2.000000, 3.000000, 4.000000}}}}));

            auto weights = std::make_shared<Tensor>(
                Array3D<float, inChannels, outChannels, kernelSize[0]>(
                    {{{{0.100000, 0.200000}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.010000}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array3D<float, batchSize, outChannels, outDataSize[0]>(
                    {{{{0.110000, 0.410000, 0.710000, 1.010000, 0.810000}}}}));
            CHECK(approxEq<float, float>(*op->getOutput(0), *expectedOutput));
        }
        SECTION("kernel = 2, inChannel = 2, outChannels = 1") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 2;
            constexpr DimSize_t outChannels = 1;

            constexpr std::array<DimSize_t, DIM> kernelSize{2};

            constexpr std::array<DimSize_t, DIM> inDataSize{4};
            constexpr std::array<DimSize_t, DIM> outDataSize{5};

            constexpr std::array<DimSize_t, DIM> stride{1};
            constexpr std::array<DimSize_t, DIM> dilation{1};

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize[0]>(
                    {{{{1.000000, 2.000000, 3.000000, 4.000000},
                       {5.000000, 6.000000, 7.000000, 8.000000}}}}));

            auto weights = std::make_shared<Tensor>(
                Array3D<float, inChannels, outChannels, kernelSize[0]>(
                    {{{{0.100000, 0.200000}}, {{0.300000, 0.400000}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.010000}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array3D<float, batchSize, outChannels, outDataSize[0]>(
                    {{{{1.610000, 4.210000, 5.210000, 6.210001, 4.010000}}}}));
            CHECK(approxEq<float, float>(*op->getOutput(0), *expectedOutput));
        }
        SECTION("kernel = 2, inChannel = 1, outChannels = 2") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 2;

            constexpr std::array<DimSize_t, DIM> kernelSize{2};

            constexpr std::array<DimSize_t, DIM> inDataSize{4};
            constexpr std::array<DimSize_t, DIM> outDataSize{5};

            constexpr std::array<DimSize_t, DIM> stride{1};
            constexpr std::array<DimSize_t, DIM> dilation{1};

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize[0]>(
                    {{{{1., 2., 3., 4.}}}}));

            auto weights = std::make_shared<Tensor>(
                Array3D<float, inChannels, outChannels, kernelSize[0]>(
                    {{{{0.1, 0.2}, {0.3, 0.4}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01, 0.02}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array3D<float, batchSize, outChannels, outDataSize[0]>(
                    {{{{0.11, 0.41, 0.71, 1.01, 0.81},
                       {0.32, 1.02, 1.72, 2.42, 1.62}}}}));

            CHECK(approxEq<float, float>(*op->getOutput(0), *expectedOutput));
        }
        SECTION("kernel = 1, inChannel = 2, outChannels = 2") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 2;
            constexpr DimSize_t outChannels = 2;

            constexpr std::array<DimSize_t, DIM> kernelSize{1};

            constexpr std::array<DimSize_t, DIM> inDataSize{4};
            constexpr std::array<DimSize_t, DIM> outDataSize{4};

            constexpr std::array<DimSize_t, DIM> stride{1};
            constexpr std::array<DimSize_t, DIM> dilation{1};

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize[0]>(
                    {{{{1.000000, 2.000000, 3.000000, 4.000000},
                       {5.000000, 6.000000, 7.000000, 8.000000}}}}));

            auto weights = std::make_shared<Tensor>(
                Array3D<float, inChannels, outChannels, kernelSize[0]>(
                    {{{{0.100000}, {0.200000}},

                      {{0.300000}, {0.400000}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.010000, 0.020000}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array3D<float, batchSize, outChannels, outDataSize[0]>(
                    {{{{1.610000, 2.010000, 2.410000, 2.810000},
                       {2.220000, 2.820000, 3.420000, 4.020000}}}}));

            CHECK(approxEq<float, float>(*op->getOutput(0), *expectedOutput));
        }
        SECTION("kernel = 2, inChannels = 2, outChannels = 3") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 2;
            constexpr DimSize_t outChannels = 3;

            constexpr std::array<DimSize_t, DIM> kernelSize{2};

            constexpr std::array<DimSize_t, DIM> inDataSize{4};
            constexpr std::array<DimSize_t, DIM> outDataSize{5};

            constexpr std::array<DimSize_t, DIM> stride{1};
            constexpr std::array<DimSize_t, DIM> dilation{1};

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize[0]>(
                    {{{{1., 2., 3., 4.}, {5., 6., 7., 8.}}}}));

            auto weights = std::make_shared<Tensor>(
                Array3D<float, inChannels, outChannels, kernelSize[0]>(
                    {{{{0.10, 0.20}, {0.30, 0.40}, {0.50, 0.60}},

                      {{0.70, 0.80}, {0.90, 1.}, {1.10, 1.20}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.010000, 0.020000, 0.030000}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<
                Tensor>(Array3D<float, batchSize, outChannels, outDataSize[0]>(
                {{{{3.610000, 8.610001, 10.410000, 12.210001, 7.210001},
                   {4.820000, 11.420000, 14.020000, 16.620001, 9.620001},
                   {6.030000, 14.230000, 17.630001, 21.030001, 12.030000}}}}));

            CHECK(approxEq<float, float>(*op->getOutput(0), *expectedOutput));
        }

        SECTION("Big test to ensure kernel capabilities") {
            constexpr DimSize_t batchSize = 2;
            constexpr DimSize_t inChannels = 3;
            constexpr DimSize_t outChannels = 4;

            constexpr std::array<DimSize_t, DIM> kernelSize{6};

            constexpr std::array<DimSize_t, DIM> inDataSize{6};
            constexpr std::array<DimSize_t, DIM> outDataSize{11};

            constexpr std::array<DimSize_t, DIM> stride{1};
            constexpr std::array<DimSize_t, DIM> dilation{1};

            auto input = std::make_shared<Tensor>(
                Array3D<float, batchSize, inChannels, inDataSize[0]>(
                    {{{{1., 2., 3., 4., 5., 6.},
                       {7., 8., 9., 10., 11., 12.},
                       {13., 14., 15., 16., 17., 18.}},

                      {{19., 20., 21., 22., 23., 24.},
                       {25., 26., 27., 28., 29., 30.},
                       {31., 32., 33., 34., 35., 36.}}}}));

            auto weights = std::make_shared<Tensor>(
                Array3D<float, inChannels, outChannels, kernelSize[0]>(
                    {{{{0.1, 0.2, 0.3, 0.4, 0.5, 0.6},
                       {0.7, 0.8, 0.9, 1., 1.1, 1.2},
                       {1.3, 1.4, 1.5, 1.6, 1.7, 1.8},
                       {1.9, 2., 2.1, 2.2, 2.3, 2.4}},

                      {{2.5, 2.6, 2.7, 2.8, 2.9, 3.},
                       {3.1, 3.2, 3.3, 3.4, 3.5, 3.6},
                       {3.7, 3.8, 3.9, 4., 4.1, 4.2},
                       {4.3, 4.4, 4.5, 4.6, 4.7, 4.8}},

                      {{4.9, 5., 5.1, 5.2, 5.3, 5.4},
                       {5.5, 5.6, 5.7, 5.8, 5.9, 6.},
                       {6.1, 6.2, 6.3, 6.4, 6.5, 6.6},
                       {6.7, 6.8, 6.9, 7., 7.1, 7.2}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01, 0.02, 0.03, 0.04}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array3D<float, batchSize, outChannels, outDataSize[0]>(
                    {{{{81.310005,
                        172.210007,
                        273.010010,
                        384.010040,
                        505.509979,
                        637.810059,
                        561.010010,
                        472.809998,
                        372.910004,
                        261.010010,
                        136.809998},
                       {93.919998,
                        199.220001,
                        316.219971,
                        445.220001,
                        586.520081,
                        740.420044,
                        651.020020,
                        548.420044,
                        432.319977,
                        302.420013,
                        158.419998},
                       {106.529999,
                        226.230011,
                        359.429993,
                        506.430054,
                        667.530090,
                        843.030029,
                        741.030029,
                        624.030029,
                        491.730042,
                        343.829987,
                        180.029999},
                       {119.140007,
                        253.240005,
                        402.640045,
                        567.640076,
                        748.539978,
                        945.639954,
                        831.039978,
                        699.640015,
                        551.140015,
                        385.239990,
                        201.639999}},

                      {{216.309998,
                        447.610016,
                        694.210022,
                        956.410034,
                        1234.510132,
                        1528.810059,
                        1317.010010,
                        1088.410034,
                        842.710022,
                        579.610046,
                        298.810028},
                       {261.319977,
                        539.420044,
                        834.619995,
                        1147.220093,
                        1477.520142,
                        1825.820068,
                        1569.019897,
                        1293.619995,
                        999.320068,
                        685.820007,
                        352.819977},
                       {306.329987,
                        631.230042,
                        975.030029,
                        1338.030151,
                        1720.530029,
                        2122.829834,
                        1821.029785,
                        1498.830200,
                        1155.930054,
                        792.030029,
                        406.830017},
                       {351.340027,
                        723.039978,
                        1115.440063,
                        1528.840210,
                        1963.539917,
                        2419.839844,
                        2073.040283,
                        1704.040039,
                        1312.540039,
                        898.239990,
                        460.840027}}}}));
            CHECK(approxEq<float, float>(*op->getOutput(0), *expectedOutput));
        }
    }

    SECTION("2D") {
        constexpr DimSize_t DIM = 2;
        SECTION("inChannels = 1, outChannels = 2, kernelSize = {1,2}, "
                "inDataSize = {2,3}") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 2;

            constexpr std::array<DimSize_t, DIM> kernelSize{1, 2};

            constexpr std::array<DimSize_t, DIM> inDataSize{2, 3};
            constexpr std::array<DimSize_t, DIM> outDataSize{2, 4};

            constexpr std::array<DimSize_t, DIM> stride{1, 1};
            constexpr std::array<DimSize_t, DIM> dilation{1, 1};

            auto input = std::make_shared<Tensor>(Array4D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1]>(
                {{{{{1.000000, 2.000000, 3.000000},
                    {4.000000, 5.000000, 6.000000}}}}}));

            auto weights = std::make_shared<Tensor>(
                Array4D<float,
                        inChannels,
                        outChannels,
                        kernelSize[0],
                        kernelSize[1]>({{{{{0.100000, 0.200000}},

                                          {{0.300000, 0.400000}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.010000}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput =
                std::make_shared<Tensor>(Array4D<float,
                                                 batchSize,
                                                 outChannels,
                                                 outDataSize[0],
                                                 outDataSize[1]>(
                    {{{{{0.110000, 0.410000, 0.710000, 0.610000},
                        {0.410000, 1.310000, 1.610000, 1.210000}},

                       {{0.320000, 1.020000, 1.720000, 1.220000},
                        {1.220000, 3.120000, 3.820000, 2.420000}}}}}));
        }
        SECTION("inChannels = 1, outChannels = 2, kernelSize = {2,3}, "
                "inDataSize = {2,3}") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 2;

            constexpr std::array<DimSize_t, DIM> kernelSize{2, 3};

            constexpr std::array<DimSize_t, DIM> inDataSize{2, 3};
            constexpr std::array<DimSize_t, DIM> outDataSize{3, 5};

            constexpr std::array<DimSize_t, DIM> stride{1, 1};
            constexpr std::array<DimSize_t, DIM> dilation{1, 1};

            auto input = std::make_shared<Tensor>(Array4D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1]>(
                {{{{{1.000000, 2.000000, 3.000000},
                    {4.000000, 5.000000, 6.000000}}}}}));

            auto weights = std::make_shared<Tensor>(Array4D<float,
                                                            inChannels,
                                                            outChannels,
                                                            kernelSize[0],
                                                            kernelSize[1]>(
                {{{{{0.100000, 0.200000, 0.300000},
                    {0.400000, 0.500000, 0.600000}},

                   {{0.700000, 0.800000, 0.900000},
                    {1.000000, 1.100000, 1.200000}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.010000, 0.020000}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<
                Tensor>(Array4D<float,
                                batchSize,
                                outChannels,
                                outDataSize[0],
                                outDataSize[1]>(
                {{{{{0.110000, 0.410000, 1.010000, 1.210000, 0.910000},
                    {0.810000, 2.610000, 5.610000, 5.410000, 3.610000},
                    {1.610000, 4.010000, 7.310000, 6.010000, 3.610000}},

                   {{0.720000, 2.220000, 4.620000, 4.220000, 2.720000},
                    {3.820000, 9.820001, 18.220001, 15.020000, 9.020000},
                    {4.020000, 9.420000, 16.320000, 12.620001, 7.220000}}}}}));
        }
        SECTION("inChannels = 1, outChannels = 2, kernelSize = {2,3}, "
                "inDataSize = {6,6}, stride = {2,  2}, dilation = {2,  2}") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 1;
            constexpr DimSize_t outChannels = 2;

            constexpr std::array<DimSize_t, DIM> kernelSize{2, 3};

            constexpr std::array<DimSize_t, DIM> inDataSize{4, 4};
            constexpr std::array<DimSize_t, DIM> outDataSize{9, 11};

            constexpr std::array<DimSize_t, DIM> stride{2, 2};
            constexpr std::array<DimSize_t, DIM> dilation{2, 2};

            auto input = std::make_shared<Tensor>(Array4D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1]>(
                {{{{{1.00, 2.00, 3.00, 4.000000},
                    {5.00, 6.00, 7.00, 8.000000},
                    {9.00, 10.00, 11.00, 12.000000},
                    {13.00, 14.00, 15.00, 16.000000}}}}}));

            auto weights = std::make_shared<Tensor>(Array4D<float,
                                                            inChannels,
                                                            outChannels,
                                                            kernelSize[0],
                                                            kernelSize[1]>(
                {{{{{0.10, 0.20, 0.300000}, {0.40, 0.50, 0.600000}},

                   {{0.70, 0.80, 0.900000}, {1.00, 1.10, 1.200000}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01, 0.020000}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array4D<float,
                        batchSize,
                        outChannels,
                        outDataSize[0],
                        outDataSize[1]>({{{{{0.11,
                                             0.01,
                                             0.41,
                                             0.01,
                                             1.01,
                                             0.01,
                                             1.61,
                                             0.01,
                                             1.71,
                                             0.01,
                                             1.210000},
                                            {0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.010000},
                                            {0.91,
                                             0.01,
                                             2.91,
                                             0.01,
                                             6.210001,
                                             0.01,
                                             8.31,
                                             0.01,
                                             7.510001,
                                             0.01,
                                             4.810000},
                                            {0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.010000},
                                            {2.91,
                                             0.01,
                                             7.710001,
                                             0.01,
                                             14.610001,
                                             0.01,
                                             16.710001,
                                             0.01,
                                             13.910002,
                                             0.01,
                                             8.410001},
                                            {0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.010000},
                                            {4.91,
                                             0.01,
                                             12.51,
                                             0.01,
                                             23.01,
                                             0.01,
                                             25.110001,
                                             0.01,
                                             20.309999,
                                             0.01,
                                             12.010000},
                                            {0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.01,
                                             0.010000},
                                            {5.210001,
                                             0.01,
                                             12.110001,
                                             0.01,
                                             20.809999,
                                             0.01,
                                             22.309999,
                                             0.01,
                                             17.01,
                                             0.01,
                                             9.610001}},

                                           {{0.72,
                                             0.02,
                                             2.22,
                                             0.02,
                                             4.62,
                                             0.02,
                                             7.02,
                                             0.02,
                                             5.92,
                                             0.02,
                                             3.620000},
                                            {0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.020000},
                                            {4.52,
                                             0.02,
                                             11.320001,
                                             0.02,
                                             20.620003,
                                             0.02,
                                             26.320002,
                                             0.02,
                                             20.720001,
                                             0.02,
                                             12.020000},
                                            {0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.020000},
                                            {11.32,
                                             0.02,
                                             25.720001,
                                             0.02,
                                             43.420002,
                                             0.02,
                                             49.120003,
                                             0.02,
                                             36.720001,
                                             0.02,
                                             20.420002},
                                            {0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.020000},
                                            {18.119999,
                                             0.02,
                                             40.120003,
                                             0.02,
                                             66.220001,
                                             0.02,
                                             71.919998,
                                             0.02,
                                             52.720001,
                                             0.02,
                                             28.820002},
                                            {0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.02,
                                             0.020000},
                                            {13.02,
                                             0.02,
                                             28.32,
                                             0.02,
                                             46.02,
                                             0.02,
                                             49.320004,
                                             0.02,
                                             35.619999,
                                             0.02,
                                             19.220001}}}}}));
        }
        SECTION("inChannels = 4, outChannels = 3, kernelSize = {2,2}, "
                "inDataSize = {3,3}, stride = {2,  2}, dilation = {2,  2}") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 4;
            constexpr DimSize_t outChannels = 3;

            constexpr std::array<DimSize_t, DIM> kernelSize{2, 2};

            constexpr std::array<DimSize_t, DIM> inDataSize{4, 4};
            constexpr std::array<DimSize_t, DIM> outDataSize{7, 7};

            constexpr std::array<DimSize_t, DIM> stride{2, 2};
            constexpr std::array<DimSize_t, DIM> dilation{2, 2};

            auto input = std::make_shared<Tensor>(Array4D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1]>(
                {{{{{1.0, 2.0, 3.0}, {4.0, 5.0, 6.0}, {7.0, 8.0, 9.0}},

                   {{10.0, 11.0, 12.0},
                    {13.0, 14.0, 15.0},
                    {16.0, 17.0, 18.0}},

                   {{19.0, 20.0, 21.0},
                    {22.0, 23.0, 24.0},
                    {25.0, 26.0, 27.0}},

                   {{28.0, 29.0, 30.0},
                    {31.0, 32.0, 33.0},
                    {34.0, 35.0, 36.0}}}}}));

            auto weights = std::make_shared<Tensor>(
                Array4D<float,
                        inChannels,
                        outChannels,
                        kernelSize[0],
                        kernelSize[1]>({{{{{0.1, 0.2}, {0.3, 0.4}},

                                          {{0.5, 0.6}, {0.7, 0.8}},

                                          {{0.9, 1.0}, {1.1, 1.2}}},

                                         {{{1.3, 1.4}, {1.5, 1.6}},

                                          {{1.7, 1.8}, {1.9, 2.0}},

                                          {{2.1, 2.2}, {2.3, 2.4}}},

                                         {{{2.5, 2.6}, {2.7, 2.8}},

                                          {{2.9, 3.0}, {3.1, 3.2}},

                                          {{3.3, 3.4}, {3.5, 3.6}}},

                                         {{{3.7, 3.8}, {3.9, 4.0}},

                                          {{4.1, 4.2}, {4.3, 4.4}},

                                          {{4.5, 4.6}, {4.7, 4.8}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.010000, 0.020000, 0.030000}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array4D<float,
                        batchSize,
                        outChannels,
                        outDataSize[0],
                        outDataSize[1]>({{{{{164.209991,
                                             0.010000,
                                             341.809998,
                                             0.010000,
                                             357.410034,
                                             0.010000,
                                             186.009995},
                                            {0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000},
                                            {362.809998,
                                             0.010000,
                                             754.410034,
                                             0.010000,
                                             787.210083,
                                             0.010000,
                                             409.210022},
                                            {0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000},
                                            {410.809998,
                                             0.010000,
                                             852.810059,
                                             0.010000,
                                             885.609985,
                                             0.010000,
                                             459.610016},
                                            {0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000,
                                             0.010000},
                                            {226.209991,
                                             0.010000,
                                             469.010010,
                                             0.010000,
                                             486.210022,
                                             0.010000,
                                             252.009995}},

                                           {{187.419998,
                                             0.020000,
                                             389.820007,
                                             0.020000,
                                             408.619995,
                                             0.020000,
                                             212.420013},
                                            {0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000},
                                            {414.019989,
                                             0.020000,
                                             860.020020,
                                             0.020000,
                                             899.220032,
                                             0.020000,
                                             466.820007},
                                            {0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000},
                                            {471.620026,
                                             0.020000,
                                             977.619995,
                                             0.020000,
                                             1016.820068,
                                             0.020000,
                                             526.820007},
                                            {0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000,
                                             0.020000},
                                            {259.019989,
                                             0.020000,
                                             536.220032,
                                             0.020000,
                                             556.619995,
                                             0.020000,
                                             288.019989}},

                                           {{210.630005,
                                             0.030000,
                                             437.829987,
                                             0.030000,
                                             459.829987,
                                             0.030000,
                                             238.830002},
                                            {0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000},
                                            {465.230011,
                                             0.030000,
                                             965.630005,
                                             0.030000,
                                             1011.230103,
                                             0.030000,
                                             524.430054},
                                            {0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000},
                                            {532.430054,
                                             0.030000,
                                             1102.430054,
                                             0.030000,
                                             1148.030029,
                                             0.030000,
                                             594.030029},
                                            {0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000,
                                             0.030000},
                                            {291.830017,
                                             0.030000,
                                             603.430054,
                                             0.030000,
                                             627.030029,
                                             0.030000,
                                             324.029999}}}}}));
        }
        SECTION("Big test to ensure kernel capabilities 1") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 3;
            constexpr DimSize_t outChannels = 4;

            constexpr std::array<DimSize_t, DIM> kernelSize{2, 2};

            constexpr std::array<DimSize_t, DIM> inDataSize{6, 5};
            constexpr std::array<DimSize_t, DIM> outDataSize{8, 17};

            constexpr std::array<DimSize_t, DIM> stride{1, 3};
            constexpr std::array<DimSize_t, DIM> dilation{2, 4};

            auto input = std::make_shared<Tensor>(
                Array4D<float,
                        batchSize,
                        inChannels,
                        inDataSize[0],
                        inDataSize[1]>({{{{{1., 2., 3., 4., 5.},
                                           {6., 7., 8., 9., 10.},
                                           {11., 12., 13., 14., 15.},
                                           {16., 17., 18., 19., 20.},
                                           {21., 22., 23., 24., 25.},
                                           {26., 27., 28., 29., 30.}},

                                          {{31., 32., 33., 34., 35.},
                                           {36., 37., 38., 39., 40.},
                                           {41., 42., 43., 44., 45.},
                                           {46., 47., 48., 49., 50.},
                                           {51., 52., 53., 54., 55.},
                                           {56., 57., 58., 59., 60.}},

                                          {{61., 62., 63., 64., 65.},
                                           {66., 67., 68., 69., 70.},
                                           {71., 72., 73., 74., 75.},
                                           {76., 77., 78., 79., 80.},
                                           {81., 82., 83., 84., 85.},
                                           {86., 87., 88., 89., 90.}}}}}));

            auto weights = std::make_shared<Tensor>(Array4D<float,
                                                            inChannels,
                                                            outChannels,
                                                            kernelSize[0],
                                                            kernelSize[1]>(
                {{{{{0.100000, 0.200000}, {0.300000, 0.400000}},

                   {{0.500000, 0.600000}, {0.700000, 0.800000}},

                   {{0.900000, 1.000000}, {1.100000, 1.200000}},

                   {{1.300000, 1.400000}, {1.500000, 1.600000}}},

                  {{{1.700000, 1.800000}, {1.900000, 2.000000}},

                   {{2.100000, 2.200000}, {2.300000, 2.400000}},

                   {{2.500000, 2.600000}, {2.700000, 2.800000}},

                   {{2.900000, 3.000000}, {3.100000, 3.200000}}},

                  {{{3.300000, 3.400000}, {3.500000, 3.600000}},

                   {{3.700000, 3.800000}, {3.900000, 4.000000}},

                   {{4.100000, 4.200000}, {4.300000, 4.400000}},

                   {{4.500000, 4.600000}, {4.700000, 4.800000}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01, 0.02, 0.03, 0.04}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array4D<float,
                        batchSize,
                        outChannels,
                        outDataSize[0],
                        outDataSize[1]>({{{{{254.110001,
                                             0.010000,
                                             0.010000,
                                             259.210022,
                                             263.410034,
                                             0.010000,
                                             264.309998,
                                             268.810028,
                                             0.010000,
                                             269.410004,
                                             274.210022,
                                             0.010000,
                                             274.510010,
                                             279.610016,
                                             0.010000,
                                             0.010000,
                                             285.010010},
                                            {279.610016,
                                             0.010000,
                                             0.010000,
                                             284.710022,
                                             290.410004,
                                             0.010000,
                                             289.809998,
                                             295.810028,
                                             0.010000,
                                             294.910004,
                                             301.210022,
                                             0.010000,
                                             300.010010,
                                             306.610016,
                                             0.010000,
                                             0.010000,
                                             312.010010},
                                            {577.810059,
                                             0.010000,
                                             0.010000,
                                             588.609985,
                                             599.410034,
                                             0.010000,
                                             599.410034,
                                             610.810059,
                                             0.010000,
                                             610.209961,
                                             622.210022,
                                             0.010000,
                                             621.010010,
                                             633.609985,
                                             0.010000,
                                             0.010000,
                                             645.010010},
                                            {631.810059,
                                             0.010000,
                                             0.010000,
                                             642.609985,
                                             656.410034,
                                             0.010000,
                                             653.410034,
                                             667.810059,
                                             0.010000,
                                             664.209961,
                                             679.210022,
                                             0.010000,
                                             675.010010,
                                             690.609985,
                                             0.010000,
                                             0.010000,
                                             702.010010},
                                            {685.810059,
                                             0.010000,
                                             0.010000,
                                             696.609985,
                                             713.410034,
                                             0.010000,
                                             707.410034,
                                             724.810059,
                                             0.010000,
                                             718.209961,
                                             736.210022,
                                             0.010000,
                                             729.010010,
                                             747.609985,
                                             0.010000,
                                             0.010000,
                                             759.010010},
                                            {739.810059,
                                             0.010000,
                                             0.010000,
                                             750.609985,
                                             770.410034,
                                             0.010000,
                                             761.410034,
                                             781.810059,
                                             0.010000,
                                             772.209961,
                                             793.210022,
                                             0.010000,
                                             783.010010,
                                             804.609985,
                                             0.010000,
                                             0.010000,
                                             816.010010},
                                            {386.710022,
                                             0.010000,
                                             0.010000,
                                             392.410004,
                                             402.010010,
                                             0.010000,
                                             398.110016,
                                             408.010010,
                                             0.010000,
                                             403.809998,
                                             414.010010,
                                             0.010000,
                                             409.510010,
                                             420.010010,
                                             0.010000,
                                             0.010000,
                                             426.010010},
                                            {415.210022,
                                             0.010000,
                                             0.010000,
                                             420.910004,
                                             432.010010,
                                             0.010000,
                                             426.610016,
                                             438.010040,
                                             0.010000,
                                             432.309998,
                                             444.010010,
                                             0.010000,
                                             438.010010,
                                             450.010040,
                                             0.010000,
                                             0.010000,
                                             456.010010}},

                                           {{291.320007,
                                             0.020000,
                                             0.020000,
                                             297.619995,
                                             300.619995,
                                             0.020000,
                                             303.919983,
                                             307.219971,
                                             0.020000,
                                             310.220001,
                                             313.819977,
                                             0.020000,
                                             316.519989,
                                             320.419983,
                                             0.020000,
                                             0.020000,
                                             327.019989},
                                            {322.820007,
                                             0.020000,
                                             0.020000,
                                             329.119995,
                                             333.619995,
                                             0.020000,
                                             335.419983,
                                             340.219971,
                                             0.020000,
                                             341.720001,
                                             346.819977,
                                             0.020000,
                                             348.019989,
                                             353.419983,
                                             0.020000,
                                             0.020000,
                                             360.019989},
                                            {664.220032,
                                             0.020000,
                                             0.020000,
                                             677.420044,
                                             685.820068,
                                             0.020000,
                                             690.619995,
                                             699.619995,
                                             0.020000,
                                             703.820068,
                                             713.420044,
                                             0.020000,
                                             717.020020,
                                             727.219971,
                                             0.020000,
                                             0.020000,
                                             741.020020},
                                            {730.220032,
                                             0.020000,
                                             0.020000,
                                             743.420044,
                                             754.820068,
                                             0.020000,
                                             756.619995,
                                             768.619995,
                                             0.020000,
                                             769.820068,
                                             782.420044,
                                             0.020000,
                                             783.020020,
                                             796.219971,
                                             0.020000,
                                             0.020000,
                                             810.020020},
                                            {796.220032,
                                             0.020000,
                                             0.020000,
                                             809.420044,
                                             823.820068,
                                             0.020000,
                                             822.620056,
                                             837.619995,
                                             0.020000,
                                             835.820068,
                                             851.420044,
                                             0.020000,
                                             849.020020,
                                             865.219971,
                                             0.020000,
                                             0.020000,
                                             879.020020},
                                            {862.220032,
                                             0.020000,
                                             0.020000,
                                             875.420044,
                                             892.820068,
                                             0.020000,
                                             888.619995,
                                             906.619995,
                                             0.020000,
                                             901.820068,
                                             920.420044,
                                             0.020000,
                                             915.020020,
                                             934.219971,
                                             0.020000,
                                             0.020000,
                                             948.020020},
                                            {447.919983,
                                             0.020000,
                                             0.020000,
                                             454.820007,
                                             463.220001,
                                             0.020000,
                                             461.720001,
                                             470.420013,
                                             0.020000,
                                             468.619995,
                                             477.619995,
                                             0.020000,
                                             475.519989,
                                             484.819977,
                                             0.020000,
                                             0.020000,
                                             492.019989},
                                            {482.419983,
                                             0.020000,
                                             0.020000,
                                             489.320007,
                                             499.220001,
                                             0.020000,
                                             496.220001,
                                             506.420013,
                                             0.020000,
                                             503.119995,
                                             513.619995,
                                             0.020000,
                                             510.019989,
                                             520.820007,
                                             0.020000,
                                             0.020000,
                                             528.020020}},

                                           {{328.529999,
                                             0.030000,
                                             0.030000,
                                             336.029999,
                                             337.830017,
                                             0.030000,
                                             343.529999,
                                             345.630035,
                                             0.030000,
                                             351.029999,
                                             353.430023,
                                             0.030000,
                                             358.529999,
                                             361.230011,
                                             0.030000,
                                             0.030000,
                                             369.030029},
                                            {366.029999,
                                             0.030000,
                                             0.030000,
                                             373.529999,
                                             376.830017,
                                             0.030000,
                                             381.029999,
                                             384.630035,
                                             0.030000,
                                             388.529999,
                                             392.430023,
                                             0.030000,
                                             396.029999,
                                             400.230042,
                                             0.030000,
                                             0.030000,
                                             408.030029},
                                            {750.630005,
                                             0.030000,
                                             0.030000,
                                             766.230042,
                                             772.230042,
                                             0.030000,
                                             781.830078,
                                             788.430054,
                                             0.030000,
                                             797.430054,
                                             804.630066,
                                             0.030000,
                                             813.030029,
                                             820.830078,
                                             0.030000,
                                             0.030000,
                                             837.030029},
                                            {828.630005,
                                             0.030000,
                                             0.030000,
                                             844.230042,
                                             853.230042,
                                             0.030000,
                                             859.830078,
                                             869.430054,
                                             0.030000,
                                             875.430054,
                                             885.630066,
                                             0.030000,
                                             891.030029,
                                             901.830078,
                                             0.030000,
                                             0.030000,
                                             918.030029},
                                            {906.630005,
                                             0.030000,
                                             0.030000,
                                             922.230042,
                                             934.230042,
                                             0.030000,
                                             937.830078,
                                             950.430054,
                                             0.030000,
                                             953.430054,
                                             966.630066,
                                             0.030000,
                                             969.030029,
                                             982.830078,
                                             0.030000,
                                             0.030000,
                                             999.030090},
                                            {984.630005,
                                             0.030000,
                                             0.030000,
                                             1000.230042,
                                             1015.230103,
                                             0.030000,
                                             1015.830078,
                                             1031.430054,
                                             0.030000,
                                             1031.430054,
                                             1047.630127,
                                             0.030000,
                                             1047.030029,
                                             1063.830078,
                                             0.030000,
                                             0.030000,
                                             1080.030029},
                                            {509.130005,
                                             0.030000,
                                             0.030000,
                                             517.230042,
                                             524.430054,
                                             0.030000,
                                             525.330078,
                                             532.830017,
                                             0.030000,
                                             533.430054,
                                             541.230042,
                                             0.030000,
                                             541.530029,
                                             549.630066,
                                             0.030000,
                                             0.030000,
                                             558.030029},
                                            {549.630066,
                                             0.030000,
                                             0.030000,
                                             557.730042,
                                             566.430054,
                                             0.030000,
                                             565.830078,
                                             574.830017,
                                             0.030000,
                                             573.930054,
                                             583.230042,
                                             0.030000,
                                             582.030029,
                                             591.630066,
                                             0.030000,
                                             0.030000,
                                             600.030029}},

                                           {{365.740021,
                                             0.040000,
                                             0.040000,
                                             374.440002,
                                             375.040009,
                                             0.040000,
                                             383.140015,
                                             384.040009,
                                             0.040000,
                                             391.839996,
                                             393.040009,
                                             0.040000,
                                             400.540009,
                                             402.040009,
                                             0.040000,
                                             0.040000,
                                             411.040009},
                                            {409.240021,
                                             0.040000,
                                             0.040000,
                                             417.940002,
                                             420.040009,
                                             0.040000,
                                             426.640015,
                                             429.040009,
                                             0.040000,
                                             435.339996,
                                             438.040009,
                                             0.040000,
                                             444.040009,
                                             447.040009,
                                             0.040000,
                                             0.040000,
                                             456.040009},
                                            {837.039978,
                                             0.040000,
                                             0.040000,
                                             855.040039,
                                             858.639954,
                                             0.040000,
                                             873.039978,
                                             877.239990,
                                             0.040000,
                                             891.039978,
                                             895.840027,
                                             0.040000,
                                             909.039978,
                                             914.440002,
                                             0.040000,
                                             0.040000,
                                             933.039978},
                                            {927.039978,
                                             0.040000,
                                             0.040000,
                                             945.040039,
                                             951.639954,
                                             0.040000,
                                             963.039978,
                                             970.239990,
                                             0.040000,
                                             981.039978,
                                             988.840027,
                                             0.040000,
                                             999.039978,
                                             1007.440002,
                                             0.040000,
                                             0.040000,
                                             1026.040039},
                                            {1017.039978,
                                             0.040000,
                                             0.040000,
                                             1035.040039,
                                             1044.640015,
                                             0.040000,
                                             1053.040039,
                                             1063.239990,
                                             0.040000,
                                             1071.040039,
                                             1081.840088,
                                             0.040000,
                                             1089.040039,
                                             1100.440063,
                                             0.040000,
                                             0.040000,
                                             1119.040039},
                                            {1107.040039,
                                             0.040000,
                                             0.040000,
                                             1125.040039,
                                             1137.640137,
                                             0.040000,
                                             1143.040039,
                                             1156.239990,
                                             0.040000,
                                             1161.040039,
                                             1174.840088,
                                             0.040000,
                                             1179.040039,
                                             1193.440063,
                                             0.040000,
                                             0.040000,
                                             1212.040039},
                                            {570.340027,
                                             0.040000,
                                             0.040000,
                                             579.640015,
                                             585.640015,
                                             0.040000,
                                             588.940002,
                                             595.239990,
                                             0.040000,
                                             598.239990,
                                             604.840027,
                                             0.040000,
                                             607.540039,
                                             614.440002,
                                             0.040000,
                                             0.040000,
                                             624.039978},
                                            {616.840027,
                                             0.040000,
                                             0.040000,
                                             626.140015,
                                             633.640015,
                                             0.040000,
                                             635.440002,
                                             643.239990,
                                             0.040000,
                                             644.739990,
                                             652.840027,
                                             0.040000,
                                             654.040039,
                                             662.440002,
                                             0.040000,
                                             0.040000,
                                             672.039978}}}}}));
            CHECK(approxEq<float, float>(*op->getOutput(0), *expectedOutput));
        }
        SECTION("Big test to ensure kernel capabilities") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 3;
            constexpr DimSize_t outChannels = 4;

            constexpr std::array<DimSize_t, DIM> kernelSize{6, 4};

            constexpr std::array<DimSize_t, DIM> inDataSize{6, 5};
            constexpr std::array<DimSize_t, DIM> outDataSize{16, 25};

            constexpr std::array<DimSize_t, DIM> stride{1, 3};
            constexpr std::array<DimSize_t, DIM> dilation{2, 4};

            auto input = std::make_shared<Tensor>(
                Array4D<float,
                        batchSize,
                        inChannels,
                        inDataSize[0],
                        inDataSize[1]>({{{{{1., 2., 3., 4., 5.},
                                           {6., 7., 8., 9., 10.},
                                           {11., 12., 13., 14., 15.},
                                           {16., 17., 18., 19., 20.},
                                           {21., 22., 23., 24., 25.},
                                           {26., 27., 28., 29., 30.}},

                                          {{31., 32., 33., 34., 35.},
                                           {36., 37., 38., 39., 40.},
                                           {41., 42., 43., 44., 45.},
                                           {46., 47., 48., 49., 50.},
                                           {51., 52., 53., 54., 55.},
                                           {56., 57., 58., 59., 60.}},

                                          {{61., 62., 63., 64., 65.},
                                           {66., 67., 68., 69., 70.},
                                           {71., 72., 73., 74., 75.},
                                           {76., 77., 78., 79., 80.},
                                           {81., 82., 83., 84., 85.},
                                           {86., 87., 88., 89., 90.}}}}}));

            auto weights = std::make_shared<Tensor>(Array4D<float,
                                                            inChannels,
                                                            outChannels,
                                                            kernelSize[0],
                                                            kernelSize[1]>(
                {{{{{0.100000, 0.200000, 0.300000, 0.400000},
                    {0.500000, 0.600000, 0.700000, 0.800000},
                    {0.900000, 1.000000, 1.100000, 1.200000},
                    {1.300000, 1.400000, 1.500000, 1.600000},
                    {1.700000, 1.800000, 1.900000, 2.000000},
                    {2.100000, 2.200000, 2.300000, 2.400000}},

                   {{2.500000, 2.600000, 2.700000, 2.800000},
                    {2.900000, 3.000000, 3.100000, 3.200000},
                    {3.300000, 3.400000, 3.500000, 3.600000},
                    {3.700000, 3.800000, 3.900000, 4.000000},
                    {4.100000, 4.200000, 4.300000, 4.400000},
                    {4.500000, 4.600000, 4.700000, 4.800000}},

                   {{4.900000, 5.000000, 5.100000, 5.200000},
                    {5.300000, 5.400000, 5.500000, 5.600000},
                    {5.700000, 5.800000, 5.900000, 6.000000},
                    {6.100000, 6.200000, 6.300000, 6.400000},
                    {6.500000, 6.600000, 6.700000, 6.800000},
                    {6.900000, 7.000000, 7.100000, 7.200000}},

                   {{7.300000, 7.400000, 7.500000, 7.600000},
                    {7.700000, 7.800000, 7.900000, 8.000000},
                    {8.100000, 8.200000, 8.300000, 8.400001},
                    {8.500000, 8.600000, 8.700000, 8.800000},
                    {8.900001, 9.000000, 9.100000, 9.200000},
                    {9.300000, 9.400001, 9.500000, 9.600000}}},

                  {{{9.700000, 9.800000, 9.900001, 10.000000},
                    {10.100000, 10.200000, 10.300000, 10.400001},
                    {10.500000, 10.600000, 10.700000, 10.800000},
                    {10.900001, 11.000000, 11.100000, 11.200000},
                    {11.300000, 11.400001, 11.500000, 11.600000},
                    {11.700000, 11.800000, 11.900001, 12.000000}},

                   {{12.100000, 12.200000, 12.300000, 12.400001},
                    {12.500000, 12.600000, 12.700000, 12.800000},
                    {12.900001, 13.000000, 13.100000, 13.200000},
                    {13.300000, 13.400001, 13.500000, 13.600000},
                    {13.700000, 13.800000, 13.900001, 14.000000},
                    {14.100000, 14.200000, 14.300000, 14.400001}},

                   {{14.500000, 14.600000, 14.700000, 14.800000},
                    {14.900001, 15.000000, 15.100000, 15.200000},
                    {15.300000, 15.400001, 15.500000, 15.600000},
                    {15.700000, 15.800000, 15.900001, 16.000000},
                    {16.100000, 16.200001, 16.300001, 16.400000},
                    {16.500000, 16.600000, 16.700001, 16.800001}},

                   {{16.900000, 17.000000, 17.100000, 17.200001},
                    {17.300001, 17.400000, 17.500000, 17.600000},
                    {17.700001, 17.800001, 17.900000, 18.000000},
                    {18.100000, 18.200001, 18.300001, 18.400000},
                    {18.500000, 18.600000, 18.700001, 18.800001},
                    {18.900000, 19.000000, 19.100000, 19.200001}}},

                  {{{19.300001, 19.400000, 19.500000, 19.600000},
                    {19.700001, 19.800001, 19.900000, 20.000000},
                    {20.100000, 20.200001, 20.300001, 20.400000},
                    {20.500000, 20.600000, 20.700001, 20.800001},
                    {20.900000, 21.000000, 21.100000, 21.200001},
                    {21.300001, 21.400000, 21.500000, 21.600000}},

                   {{21.700001, 21.800001, 21.900000, 22.000000},
                    {22.100000, 22.200001, 22.300001, 22.400000},
                    {22.500000, 22.600000, 22.700001, 22.800001},
                    {22.900000, 23.000000, 23.100000, 23.200001},
                    {23.300001, 23.400000, 23.500000, 23.600000},
                    {23.700001, 23.800001, 23.900000, 24.000000}},

                   {{24.100000, 24.200001, 24.300001, 24.400000},
                    {24.500000, 24.600000, 24.700001, 24.800001},
                    {24.900000, 25.000000, 25.100000, 25.200001},
                    {25.300001, 25.400000, 25.500000, 25.600000},
                    {25.700001, 25.800001, 25.900000, 26.000000},
                    {26.100000, 26.200001, 26.300001, 26.400000}},

                   {{26.500000, 26.600000, 26.700001, 26.800001},
                    {26.900000, 27.000000, 27.100000, 27.200001},
                    {27.300001, 27.400000, 27.500000, 27.600000},
                    {27.700001, 27.800001, 27.900000, 28.000000},
                    {28.100000, 28.200001, 28.300001, 28.400000},
                    {28.500000, 28.600000, 28.700001, 28.800001}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01, 0.02, 0.03, 0.04}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput =
                std::make_shared<Tensor>(Array4D<float,
                                                 batchSize,
                                                 outChannels,
                                                 outDataSize[0],
                                                 outDataSize[1]>(
                    {{{{{1478.110107, 0.010000,    0.010000,    1507.210083,
                         1487.410034, 0.010000,    1536.310059, 1516.809937,
                         1496.709961, 1565.410034, 1546.209961, 1526.410034,
                         3100.510010, 1575.609985, 1556.109985, 1536.010010,
                         1605.010010, 1585.810059, 1566.010010, 0.010000,
                         1615.510010, 1596.010010, 0.010000,    0.010000,
                         1626.010010},
                        {1623.610107, 0.010000,    0.010000,    1652.710083,
                         1634.410034, 0.010000,    1681.810059, 1663.809937,
                         1645.209961, 1710.910034, 1693.209961, 1674.910034,
                         3396.010010, 1722.609985, 1704.610107, 1686.010010,
                         1752.010010, 1734.310059, 1716.010010, 0.010000,
                         1764.010010, 1746.010010, 0.010000,    0.010000,
                         1776.010010},
                        {3284.410156, 0.010000,    0.010000,    3343.810303,
                         3306.010010, 0.010000,    3403.210205, 3366.010010,
                         3327.610107, 3462.610107, 3426.010010, 3388.209961,
                         6871.209961, 3486.010010, 3448.810059, 3410.409912,
                         3546.010010, 3509.409912, 3471.610107, 0.010000,
                         3570.010010, 3532.810059, 0.010000,    0.010000,
                         3594.010010},
                        {3581.410156, 0.010000,    0.010000,    3640.810303,
                         3606.010010, 0.010000,    3700.210205, 3666.010010,
                         3630.610107, 3759.610107, 3726.010010, 3691.209961,
                         7474.209961, 3786.010010, 3751.810059, 3716.409912,
                         3846.010010, 3812.409912, 3777.610107, 0.010000,
                         3873.010010, 3838.810059, 0.010000,    0.010000,
                         3900.010010},
                        {5430.910156,  0.010000,    0.010000,    5521.809570,
                         5467.809570,  0.010000,    5612.709961, 5559.609863,
                         5504.709961,  5703.609863, 5651.409668, 5597.409668,
                         11336.110352, 5743.209961, 5690.109863, 5635.209473,
                         5835.009766,  5782.809570, 5728.809570, 0.010000,
                         5875.509766,  5822.409668, 0.010000,    0.010000,
                         5916.009766},
                        {5885.410156,  0.010000,    0.010000,    5976.310059,
                         5926.809570,  0.010000,    6067.209961, 6018.609863,
                         5968.209961,  6158.110352, 6110.409668, 6060.909668,
                         12258.610352, 6202.209961, 6153.609375, 6103.209473,
                         6294.009766,  6246.309570, 6196.809570, 0.010000,
                         6339.009766,  6290.409668, 0.010000,    0.010000,
                         6384.009766},
                        {5578.509766,  0.010000,    0.010000,    5673.009766,
                         5615.410156,  0.010000,    5767.510254, 5710.809570,
                         5652.309570,  5862.009766, 5806.209961, 5748.609863,
                         11645.710938, 5901.609863, 5844.909668, 5786.409668,
                         5997.009766,  5941.209961, 5883.609375, 0.010000,
                         6037.509766,  5980.809570, 0.010000,    0.010000,
                         6078.009766},
                        {6051.009766,  0.010000,    0.010000,    6145.509766,
                         6092.410156,  0.010000,    6240.010254, 6187.810059,
                         6133.809570,  6334.509766, 6283.209961, 6230.109863,
                         12604.208984, 6378.610352, 6326.409668, 6272.410156,
                         6474.009766,  6422.709961, 6369.609375, 0.010000,
                         6519.009766,  6466.809570, 0.010000,    0.010000,
                         6564.009766},
                        {5726.109863,  0.010000,    0.010000,    5824.209473,
                         5763.009766,  0.010000,    5922.309570, 5862.009766,
                         5799.910156,  6020.409668, 5961.010254, 5899.809570,
                         11955.309570, 6060.009766, 5999.709961, 5937.609863,
                         6159.009766,  6099.609863, 6038.409668, 0.010000,
                         6199.509766,  6139.209961, 0.010000,    0.010000,
                         6240.009766},
                        {6216.609863,  0.010000,    0.010000,    6314.709473,
                         6258.009766,  0.010000,    6412.809570, 6357.009766,
                         6299.410156,  6510.909668, 6456.010254, 6399.310059,
                         12949.809570, 6555.009766, 6499.209961, 6441.609863,
                         6654.009766,  6599.110352, 6542.409668, 0.010000,
                         6699.009766,  6643.209961, 0.010000,    0.010000,
                         6744.009766},
                        {5873.709961,  0.010000,    0.010000,    5975.409668,
                         5910.609863,  0.010000,    6077.109375, 6013.209473,
                         5947.509766,  6178.809570, 6115.809570, 6051.009766,
                         12264.910156, 6218.409668, 6154.510254, 6088.809570,
                         6321.009766,  6258.009766, 6193.209961, 0.010000,
                         6361.509766,  6297.610352, 0.010000,    0.010000,
                         6402.009766},
                        {6382.209473,  0.010000,    0.010000,    6483.910156,
                         6423.609863,  0.010000,    6585.609375, 6526.209473,
                         6465.009766,  6687.309570, 6628.809570, 6568.509766,
                         13295.410156, 6731.409668, 6672.010254, 6610.810059,
                         6834.009766,  6775.509766, 6715.209961, 0.010000,
                         6879.009766,  6819.610352, 0.010000,    0.010000,
                         6924.009766},
                        {4320.009766, 0.010000,    0.010000,    4389.009766,
                         4347.609863, 0.010000,    4458.009766, 4417.209961,
                         4375.209961, 4527.009766, 4486.809570, 4445.409668,
                         8998.809570, 4556.409668, 4515.609863, 4473.609863,
                         4626.009766, 4585.809570, 4544.410156, 0.010000,
                         4656.009766, 4615.209961, 0.010000,    0.010000,
                         4686.009766},
                        {4665.009766, 0.010000,    0.010000,    4734.009766,
                         4695.609375, 0.010000,    4803.009766, 4765.209961,
                         4726.209961, 4872.009766, 4834.809570, 4796.409668,
                         9697.809570, 4904.409668, 4866.609863, 4827.609863,
                         4974.009766, 4936.809570, 4898.410156, 0.010000,
                         5007.009766, 4969.209961, 0.010000,    0.010000,
                         5040.009766},
                        {2366.110107, 0.010000,    0.010000,    2401.209961,
                         2381.409912, 0.010000,    2436.310059, 2416.810059,
                         2396.709961, 2471.410156, 2452.209961, 2432.409912,
                         4918.509766, 2487.609863, 2468.110107, 2448.010010,
                         2523.010010, 2503.810059, 2484.010010, 0.010000,
                         2539.510010, 2520.010010, 0.010000,    0.010000,
                         2556.010010},
                        {2541.610107, 0.010000,    0.010000,    2576.710205,
                         2558.409912, 0.010000,    2611.810059, 2593.810059,
                         2575.209961, 2646.910156, 2629.209961, 2610.909912,
                         5274.009766, 2664.609863, 2646.610107, 2628.010010,
                         2700.010010, 2682.310059, 2664.010010, 0.010000,
                         2718.010010, 2700.010010, 0.010000,    0.010000,
                         2736.010010}},

                       {{1701.320068, 0.020000,    0.020000,    1737.620117,
                         1710.620117, 0.020000,    1773.920044, 1747.220093,
                         1719.920044, 1810.220093, 1783.820068, 1756.819946,
                         3575.719971, 1820.420044, 1793.719971, 1766.420044,
                         1857.020142, 1830.619995, 1803.619995, 0.020000,
                         1867.520020, 1840.820068, 0.020000,    0.020000,
                         1878.020020},
                        {1882.820068, 0.020000,    0.020000,    1919.120117,
                         1893.620117, 0.020000,    1955.420044, 1930.220093,
                         1904.420044, 1991.720093, 1966.820068, 1941.319946,
                         3943.219971, 2003.420044, 1978.219971, 1952.420044,
                         2040.020142, 2015.119995, 1989.620117, 0.020000,
                         2052.020020, 2026.820068, 0.020000,    0.020000,
                         2064.020020},
                        {3802.820068, 0.020000,    0.020000,    3876.620117,
                         3824.420166, 0.020000,    3950.420166, 3898.820068,
                         3846.020020, 4024.220215, 3973.220215, 3921.020020,
                         7965.620117, 4047.620117, 3996.020020, 3943.219727,
                         4122.020020, 4071.020020, 4018.820068, 0.020000,
                         4146.020020, 4094.419922, 0.020000,    0.020000,
                         4170.020020},
                        {4171.819824, 0.020000,    0.020000,    4245.620117,
                         4196.420410, 0.020000,    4319.420410, 4270.819824,
                         4221.020020, 4393.220215, 4345.220215, 4296.020020,
                         8712.620117, 4419.620605, 4371.020020, 4321.219727,
                         4494.020020, 4446.020020, 4396.819824, 0.020000,
                         4521.020020, 4472.419922, 0.020000,    0.020000,
                         4548.020020},
                        {6316.520020,  0.020000,    0.020000,    6429.020020,
                         6353.420410,  0.020000,    6541.520508, 6466.819824,
                         6390.319824,  6654.020020, 6580.220215, 6504.620117,
                         13193.718750, 6693.620605, 6618.919922, 6542.420410,
                         6807.020020,  6733.220215, 6657.619629, 0.020000,
                         6847.520020,  6772.819824, 0.020000,    0.020000,
                         6888.020020},
                        {6879.020020,  0.020000,    0.020000,    6991.520020,
                         6920.420410,  0.020000,    7104.020508, 7033.820312,
                         6961.819824,  7216.520020, 7147.220215, 7076.120117,
                         14332.218750, 7260.620605, 7190.420410, 7118.420410,
                         7374.020020,  7304.720215, 7233.619629, 0.020000,
                         7419.020020,  7348.819824, 0.020000,    0.020000,
                         7464.020020},
                        {6464.120117,  0.020000,    0.020000,    6580.219727,
                         6501.020020,  0.020000,    6696.319824, 6618.020020,
                         6537.920410,  6812.419922, 6735.020508, 6655.819824,
                         13503.319336, 6852.020020, 6773.720215, 6693.620117,
                         6969.020020,  6891.620605, 6812.419922, 0.020000,
                         7009.520020,  6931.220215, 0.020000,    0.020000,
                         7050.020020},
                        {7044.620117,  0.020000,    0.020000,    7160.720215,
                         7086.020020,  0.020000,    7276.819824, 7203.020020,
                         7127.420410,  7392.919434, 7320.020508, 7245.320312,
                         14677.819336, 7437.020020, 7363.220215, 7287.620117,
                         7554.020020,  7481.120605, 7406.420410, 0.020000,
                         7599.020020,  7525.220215, 0.020000,    0.020000,
                         7644.020020},
                        {6611.719727,  0.020000,    0.020000,    6731.420410,
                         6648.620117,  0.020000,    6851.119629, 6769.219727,
                         6685.520020,  6970.819824, 6889.819824, 6807.020020,
                         13812.919922, 7010.419922, 6928.520508, 6844.819824,
                         7131.020020,  7050.020020, 6967.220215, 0.020000,
                         7171.520020,  7089.620605, 0.020000,    0.020000,
                         7212.020020},
                        {7210.219727,  0.020000,    0.020000,    7329.920410,
                         7251.620117,  0.020000,    7449.619629, 7372.220215,
                         7293.020020,  7569.319824, 7492.819824, 7414.520020,
                         15023.418945, 7613.419434, 7536.020508, 7456.820312,
                         7734.020020,  7657.520020, 7579.220215, 0.020000,
                         7779.020020,  7701.620605, 0.020000,    0.020000,
                         7824.020020},
                        {6759.319824,  0.020000,    0.020000,    6882.620117,
                         6796.219727,  0.020000,    7005.919922, 6920.420410,
                         6833.120117,  7129.220215, 7044.619629, 6958.219727,
                         14122.519531, 7168.819824, 7083.319824, 6996.020020,
                         7293.020020,  7208.419922, 7122.020508, 0.020000,
                         7333.520020,  7248.020020, 0.020000,    0.020000,
                         7374.020020},
                        {7375.819824,  0.020000,    0.020000,    7499.120117,
                         7417.219727,  0.020000,    7622.420410, 7541.420410,
                         7458.620117,  7745.720215, 7665.619629, 7583.720215,
                         15369.019531, 7789.819824, 7708.819824, 7626.020020,
                         7914.020020,  7833.919434, 7752.020508, 0.020000,
                         7959.020020,  7878.020020, 0.020000,    0.020000,
                         8004.020020},
                        {4982.420410,  0.020000,    0.020000,    5065.819824,
                         5010.020020,  0.020000,    5149.220215, 5094.020020,
                         5037.619629,  5232.620605, 5178.020020, 5122.220215,
                         10381.219727, 5262.020020, 5206.819824, 5150.419922,
                         5346.020020,  5291.419922, 5235.620117, 0.020000,
                         5376.020020,  5320.819824, 0.020000,    0.020000,
                         5406.020020},
                        {5399.420410,  0.020000,    0.020000,    5482.820312,
                         5430.020020,  0.020000,    5566.220215, 5514.020020,
                         5460.619629,  5649.620605, 5598.020020, 5545.220215,
                         11224.219727, 5682.020020, 5629.819824, 5576.419922,
                         5766.020020,  5714.419922, 5661.620117, 0.020000,
                         5799.020020,  5746.819824, 0.020000,    0.020000,
                         5832.020020},
                        {2733.320068, 0.020000,    0.020000,    2775.620117,
                         2748.620117, 0.020000,    2817.920166, 2791.219971,
                         2763.919922, 2860.220215, 2833.820068, 2806.820068,
                         5681.720215, 2876.420166, 2849.719971, 2822.419922,
                         2919.020020, 2892.619873, 2865.620117, 0.020000,
                         2935.520020, 2908.820068, 0.020000,    0.020000,
                         2952.020020},
                        {2944.820068, 0.020000,    0.020000,    2987.120117,
                         2961.620117, 0.020000,    3029.420166, 3004.220215,
                         2978.419922, 3071.720215, 3046.820068, 3021.320068,
                         6109.220215, 3089.420166, 3064.219971, 3038.419922,
                         3132.020020, 3107.119873, 3081.620117, 0.020000,
                         3150.020020, 3124.820068, 0.020000,    0.020000,
                         3168.020020}},

                       {{1924.530029, 0.030000,    0.030000,    1968.030029,
                         1933.830078, 0.030000,    2011.530029, 1977.630127,
                         1943.130127, 2055.030029, 2021.430054, 1987.230103,
                         4050.929932, 2065.230225, 2031.330078, 1996.829956,
                         2109.030029, 2075.430176, 2041.229980, 0.030000,
                         2119.530029, 2085.630127, 0.030000,    0.030000,
                         2130.030029},
                        {2142.030029, 0.030000,    0.030000,    2185.530029,
                         2152.830078, 0.030000,    2229.030029, 2196.630127,
                         2163.630127, 2272.530029, 2240.430176, 2207.729980,
                         4490.429688, 2284.230225, 2251.830078, 2218.830078,
                         2328.030029, 2295.930176, 2263.229980, 0.030000,
                         2340.030029, 2307.629883, 0.030000,    0.030000,
                         2352.030029},
                        {4321.229980, 0.030000,    0.030000,    4409.429688,
                         4342.829590, 0.030000,    4497.629883, 4431.629883,
                         4364.430176, 4585.829590, 4520.430176, 4453.829590,
                         9060.030273, 4609.229980, 4543.229980, 4476.029785,
                         4698.029785, 4632.630371, 4566.029785, 0.030000,
                         4722.029785, 4656.029785, 0.030000,    0.030000,
                         4746.029785},
                        {4762.229980, 0.030000,    0.030000,    4850.429688,
                         4786.829590, 0.030000,    4938.629883, 4875.629883,
                         4811.430176, 5026.829590, 4964.430176, 4900.829590,
                         9951.030273, 5053.229980, 4990.229980, 4926.029785,
                         5142.029785, 5079.630371, 5016.029785, 0.030000,
                         5169.029785, 5106.029785, 0.030000,    0.030000,
                         5196.029785},
                        {7202.129883,  0.030000,    0.030000,    7336.229492,
                         7239.029785,  0.030000,    7470.329590, 7374.029785,
                         7275.930176,  7604.429688, 7509.030273, 7411.829590,
                         15051.330078, 7644.029785, 7547.729980, 7449.629883,
                         7779.029785,  7683.630371, 7586.430176, 0.030000,
                         7819.529785,  7723.229980, 0.030000,    0.030000,
                         7860.029785},
                        {7872.629883,  0.030000,    0.030000,    8006.729980,
                         7914.029785,  0.030000,    8140.829590, 8049.029785,
                         7955.430176,  8274.929688, 8184.030273, 8091.330078,
                         16405.830078, 8319.030273, 8227.230469, 8133.629883,
                         8454.030273,  8363.130859, 8270.430664, 0.030000,
                         8499.030273,  8407.230469, 0.030000,    0.030000,
                         8544.030273},
                        {7349.729492,  0.030000,    0.030000,    7487.430176,
                         7386.629883,  0.030000,    7625.129395, 7525.229980,
                         7423.529785,  7762.829590, 7663.829590, 7563.029785,
                         15360.929688, 7802.429688, 7702.530273, 7600.829590,
                         7941.029785,  7842.029785, 7741.229980, 0.030000,
                         7981.529785,  7881.630371, 0.030000,    0.030000,
                         8022.029785},
                        {8038.229492,  0.030000,    0.030000,    8175.930176,
                         8079.629883,  0.030000,    8313.629883, 8218.230469,
                         8121.029785,  8451.330078, 8356.830078, 8260.530273,
                         16751.427734, 8495.429688, 8400.030273, 8302.831055,
                         8634.030273,  8539.530273, 8443.230469, 0.030000,
                         8679.030273,  8583.630859, 0.030000,    0.030000,
                         8724.030273},
                        {7497.329590,  0.030000,    0.030000,    7638.629883,
                         7534.229492,  0.030000,    7779.930176, 7676.430176,
                         7571.130371,  7921.229980, 7818.629395, 7714.229980,
                         15670.530273, 7960.829590, 7857.329590, 7752.029785,
                         8103.029785,  8000.429688, 7896.030273, 0.030000,
                         8143.529785,  8040.029785, 0.030000,    0.030000,
                         8184.029785},
                        {8203.830078,  0.030000,    0.030000,    8345.129883,
                         8245.229492,  0.030000,    8486.430664, 8387.430664,
                         8286.630859,  8627.730469, 8529.629883, 8429.730469,
                         17097.029297, 8671.830078, 8572.830078, 8472.030273,
                         8814.030273,  8715.930664, 8616.030273, 0.030000,
                         8859.030273,  8760.030273, 0.030000,    0.030000,
                         8904.030273},
                        {7644.930176,  0.030000,    0.030000,    7789.829590,
                         7681.829590,  0.030000,    7934.729980, 7827.629883,
                         7718.729980,  8079.630371, 7973.430176, 7865.430176,
                         15980.130859, 8119.229980, 8012.129395, 7903.229980,
                         8265.030273,  8158.830566, 8050.829590, 0.030000,
                         8305.530273,  8198.430664, 0.030000,    0.030000,
                         8346.030273},
                        {8369.430664,  0.030000,    0.030000,    8514.331055,
                         8410.830078,  0.030000,    8659.230469, 8556.629883,
                         8452.231445,  8804.130859, 8702.430664, 8598.930664,
                         17442.628906, 8848.230469, 8745.629883, 8641.230469,
                         8994.030273,  8892.331055, 8788.830078, 0.030000,
                         9039.030273,  8936.430664, 0.030000,    0.030000,
                         9084.030273},
                        {5644.829590,  0.030000,    0.030000,    5742.629883,
                         5672.430176,  0.030000,    5840.430176, 5770.830078,
                         5700.029785,  5938.229980, 5869.229980, 5799.029785,
                         11763.630859, 5967.630371, 5898.029785, 5827.229980,
                         6066.029785,  5997.029785, 5926.829590, 0.030000,
                         6096.029785,  6026.430176, 0.030000,    0.030000,
                         6126.029785},
                        {6133.829590,  0.030000,    0.030000,    6231.629883,
                         6164.430176,  0.030000,    6329.430176, 6262.830078,
                         6195.029785,  6427.229980, 6361.229980, 6294.029785,
                         12750.630859, 6459.630371, 6393.029785, 6325.229980,
                         6558.029785,  6492.029785, 6424.829590, 0.030000,
                         6591.029785,  6524.430176, 0.030000,    0.030000,
                         6624.029785},
                        {3100.530029, 0.030000,    0.030000,    3150.030029,
                         3115.830078, 0.030000,    3199.530029, 3165.630127,
                         3131.130127, 3249.030029, 3215.430176, 3181.230225,
                         6444.930176, 3265.230225, 3231.330078, 3196.830078,
                         3315.030029, 3281.430176, 3247.230225, 0.030000,
                         3331.530029, 3297.630127, 0.030000,    0.030000,
                         3348.030029},
                        {3348.030029, 0.030000,    0.030000,    3397.530029,
                         3364.830078, 0.030000,    3447.030029, 3414.630127,
                         3381.630127, 3496.530029, 3464.430176, 3431.730225,
                         6944.430176, 3514.230225, 3481.830078, 3448.830078,
                         3564.030029, 3531.930176, 3499.230225, 0.030000,
                         3582.030029, 3549.630127, 0.030000,    0.030000,
                         3600.030029}},

                       {{2147.739990, 0.040000,    0.040000,    2198.439941,
                         2157.040039, 0.040000,    2249.140137, 2208.040039,
                         2166.340088, 2299.840088, 2259.040039, 2217.640137,
                         4526.140137, 2310.040039, 2268.940186, 2227.240234,
                         2361.040039, 2320.240234, 2278.840088, 0.040000,
                         2371.540039, 2330.440186, 0.040000,    0.040000,
                         2382.040039},
                        {2401.239990, 0.040000,    0.040000,    2451.939941,
                         2412.040039, 0.040000,    2502.640137, 2463.040039,
                         2422.840088, 2553.340088, 2514.040039, 2474.140137,
                         5037.640137, 2565.040039, 2525.440186, 2485.240234,
                         2616.040039, 2576.740234, 2536.840088, 0.040000,
                         2628.040039, 2588.440186, 0.040000,    0.040000,
                         2640.040039},
                        {4839.640137,  0.040000,    0.040000,    4942.240234,
                         4861.240234,  0.040000,    5044.839844, 4964.439941,
                         4882.839844,  5147.440430, 5067.640137, 4986.640137,
                         10154.440430, 5170.839844, 5090.440430, 5008.840332,
                         5274.040039,  5194.240234, 5113.240234, 0.040000,
                         5298.040039,  5217.640625, 0.040000,    0.040000,
                         5322.040039},
                        {5352.640137,  0.040000,    0.040000,    5455.240234,
                         5377.240234,  0.040000,    5557.839844, 5480.439941,
                         5401.839844,  5660.440430, 5583.640137, 5505.640137,
                         11189.439453, 5686.839844, 5609.440430, 5530.840332,
                         5790.040039,  5713.240234, 5635.240234, 0.040000,
                         5817.040039,  5739.640625, 0.040000,    0.040000,
                         5844.040039},
                        {8087.740234,  0.040000,    0.040000,    8243.440430,
                         8124.640625,  0.040000,    8399.139648, 8281.240234,
                         8161.540039,  8554.840820, 8437.839844, 8319.040039,
                         16908.937500, 8594.440430, 8476.540039, 8356.840820,
                         8751.040039,  8634.040039, 8515.240234, 0.040000,
                         8791.540039,  8673.640625, 0.040000,    0.040000,
                         8832.040039},
                        {8866.240234,  0.040000,    0.040000,    9021.940430,
                         8907.640625,  0.040000,    9177.639648, 9064.240234,
                         8949.040039,  9333.340820, 9220.839844, 9106.540039,
                         18479.437500, 9377.440430, 9264.040039, 9148.840820,
                         9534.040039,  9421.540039, 9307.240234, 0.040000,
                         9579.040039,  9465.640625, 0.040000,    0.040000,
                         9624.040039},
                        {8235.339844,  0.040000,    0.040000,    8394.639648,
                         8272.240234,  0.040000,    8553.940430, 8432.440430,
                         8309.140625,  8713.240234, 8592.639648, 8470.240234,
                         17218.539062, 8752.840820, 8631.339844, 8508.040039,
                         8913.040039,  8792.440430, 8670.040039, 0.040000,
                         8953.540039,  8832.040039, 0.040000,    0.040000,
                         8994.040039},
                        {9031.839844,  0.040000,    0.040000,    9191.139648,
                         9073.240234,  0.040000,    9350.440430, 9233.440430,
                         9114.640625,  9509.740234, 9393.639648, 9275.740234,
                         18825.039062, 9553.839844, 9436.839844, 9318.040039,
                         9714.040039,  9597.940430, 9480.040039, 0.040000,
                         9759.040039,  9642.040039, 0.040000,    0.040000,
                         9804.040039},
                        {8382.940430,  0.040000,    0.040000,    8545.840820,
                         8419.839844,  0.040000,    8708.740234, 8583.639648,
                         8456.740234,  8871.640625, 8747.440430, 8621.440430,
                         17528.138672, 8911.240234, 8786.139648, 8659.240234,
                         9075.040039,  8950.840820, 8824.839844, 0.040000,
                         9115.540039,  8990.440430, 0.040000,    0.040000,
                         9156.040039},
                        {9197.440430,  0.040000,    0.040000,    9360.340820,
                         9238.839844,  0.040000,    9523.240234, 9402.639648,
                         9280.240234,  9686.140625, 9566.440430, 9444.940430,
                         19170.638672, 9730.240234, 9609.639648, 9487.240234,
                         9894.040039,  9774.339844, 9652.839844, 0.040000,
                         9939.040039,  9818.440430, 0.040000,    0.040000,
                         9984.040039},
                        {8530.540039,  0.040000,    0.040000,    8697.040039,
                         8567.440430,  0.040000,    8863.540039, 8734.840820,
                         8604.339844,  9030.040039, 8902.240234, 8772.639648,
                         17837.740234, 9069.640625, 8940.940430, 8810.440430,
                         9237.040039,  9109.240234, 8979.639648, 0.040000,
                         9277.540039,  9148.840820, 0.040000,    0.040000,
                         9318.040039},
                        {9363.040039,  0.040000,    0.040000,    9529.540039,
                         9404.440430,  0.040000,    9696.040039, 9571.840820,
                         9445.839844,  9862.540039, 9739.240234, 9614.139648,
                         19516.240234, 9906.640625, 9782.440430, 9656.440430,
                         10074.040039, 9950.740234, 9825.639648, 0.040000,
                         10119.040039, 9994.839844, 0.040000,    0.040000,
                         10164.040039},
                        {6307.240234,  0.040000,    0.040000,    6419.439941,
                         6334.839844,  0.040000,    6531.640137, 6447.640137,
                         6362.440430,  6643.839844, 6560.440430, 6475.840332,
                         13146.040039, 6673.240234, 6589.240234, 6504.040039,
                         6786.040039,  6702.640625, 6618.040039, 0.040000,
                         6816.040039,  6732.040039, 0.040000,    0.040000,
                         6846.040039},
                        {6868.240234,  0.040000,    0.040000,    6980.439941,
                         6898.839844,  0.040000,    7092.640137, 7011.640137,
                         6929.440430,  7204.839844, 7124.440430, 7042.840332,
                         14277.040039, 7237.240234, 7156.240234, 7074.040039,
                         7350.040039,  7269.640625, 7188.040039, 0.040000,
                         7383.040039,  7302.040039, 0.040000,    0.040000,
                         7416.040039},
                        {3467.739990, 0.040000,    0.040000,    3524.439941,
                         3483.040039, 0.040000,    3581.140137, 3540.040039,
                         3498.340088, 3637.840088, 3597.040039, 3555.640137,
                         7208.140137, 3654.040039, 3612.940186, 3571.240234,
                         3711.040039, 3670.240234, 3628.840088, 0.040000,
                         3727.540039, 3686.440186, 0.040000,    0.040000,
                         3744.040039},
                        {3751.239990, 0.040000,    0.040000,    3807.939941,
                         3768.040039, 0.040000,    3864.640137, 3825.040039,
                         3784.840088, 3921.340088, 3882.040039, 3842.140137,
                         7779.640137, 3939.040039, 3899.440186, 3859.240234,
                         3996.040039, 3956.740234, 3916.840088, 0.040000,
                         4014.040039, 3974.440186, 0.040000,    0.040000,
                         4032.040039}}}}}));
            CHECK(approxEq<float, float>(*op->getOutput(0), *expectedOutput));
        }
    }
    SECTION("3D") {
        constexpr DimSize_t DIM = 3;
        SECTION("Big test to ensure kernel capabilities") {
            constexpr DimSize_t batchSize = 1;
            constexpr DimSize_t inChannels = 3;
            constexpr DimSize_t outChannels = 2;

            constexpr std::array<DimSize_t, DIM> kernelSize{1, 2, 3};

            constexpr std::array<DimSize_t, DIM> inDataSize{4, 4, 5};
            constexpr std::array<DimSize_t, DIM> outDataSize{4, 10, 15};

            constexpr std::array<DimSize_t, DIM> stride{1, 2, 3};
            constexpr std::array<DimSize_t, DIM> dilation{2, 3, 1};

            auto input = std::make_shared<Tensor>(Array5D<float,
                                                          batchSize,
                                                          inChannels,
                                                          inDataSize[0],
                                                          inDataSize[1],
                                                          inDataSize[2]>(
                {{{{{{1., 2., 3., 4., 5.},
                     {6., 7., 8., 9., 10.},
                     {11., 12., 13., 14., 15.},
                     {16., 17., 18., 19., 20.}},

                    {{21., 22., 23., 24., 25.},
                     {26., 27., 28., 29., 30.},
                     {31., 32., 33., 34., 35.},
                     {36., 37., 38., 39., 40.}},

                    {{41., 42., 43., 44., 45.},
                     {46., 47., 48., 49., 50.},
                     {51., 52., 53., 54., 55.},
                     {56., 57., 58., 59., 60.}},

                    {{61., 62., 63., 64., 65.},
                     {66., 67., 68., 69., 70.},
                     {71., 72., 73., 74., 75.},
                     {76., 77., 78., 79., 80.}}},

                   {{{81., 82., 83., 84., 85.},
                     {86., 87., 88., 89., 90.},
                     {91., 92., 93., 94., 95.},
                     {96., 97., 98., 99., 100.}},

                    {{101., 102., 103., 104., 105.},
                     {106., 107., 108., 109., 110.},
                     {111., 112., 113., 114., 115.},
                     {116., 117., 118., 119., 120.}},

                    {{121., 122., 123., 124., 125.},
                     {126., 127., 128., 129., 130.},
                     {131., 132., 133., 134., 135.},
                     {136., 137., 138., 139., 140.}},

                    {{141., 142., 143., 144., 145.},
                     {146., 147., 148., 149., 150.},
                     {151., 152., 153., 154., 155.},
                     {156., 157., 158., 159., 160.}}},

                   {{{161., 162., 163., 164., 165.},
                     {166., 167., 168., 169., 170.},
                     {171., 172., 173., 174., 175.},
                     {176., 177., 178., 179., 180.}},

                    {{181., 182., 183., 184., 185.},
                     {186., 187., 188., 189., 190.},
                     {191., 192., 193., 194., 195.},
                     {196., 197., 198., 199., 200.}},

                    {{201., 202., 203., 204., 205.},
                     {206., 207., 208., 209., 210.},
                     {211., 212., 213., 214., 215.},
                     {216., 217., 218., 219., 220.}},

                    {{221., 222., 223., 224., 225.},
                     {226., 227., 228., 229., 230.},
                     {231., 232., 233., 234., 235.},
                     {236., 237., 238., 239., 240.}}}}}}));

            auto weights = std::make_shared<Tensor>(Array5D<float,
                                                            inChannels,
                                                            outChannels,
                                                            kernelSize[0],
                                                            kernelSize[1],
                                                            kernelSize[2]>(
                {{{{{{0.1, 0.2, 0.3}, {0.4, 0.5, 0.6}}},

                   {{{0.7, 0.8, 0.9}, {1.0, 1.1, 1.2}}}},

                  {{{{1.3, 1.4, 1.5}, {1.6, 1.7, 1.8}}},

                   {{{1.9, 2.0, 2.1}, {2.2, 2.3, 2.4}}}}}}));

            auto biases = std::make_shared<Tensor>(
                Array1D<float, outChannels>({{0.01, 0.02}}));

            auto op = setupTestConvTranspose<DIM>(batchSize,
                                                  inChannels,
                                                  outChannels,
                                                  kernelSize,
                                                  inDataSize,
                                                  stride,
                                                  dilation,
                                                  input,
                                                  weights,
                                                  biases);

            REQUIRE_NOTHROW(op->forward());

            auto expectedOutput = std::make_shared<Tensor>(
                Array5D<float,
                        batchSize,
                        outChannels,
                        outDataSize[0],
                        outDataSize[1],
                        outDataSize[2]>({{{{{{507.910034,
                                              532.210022,
                                              556.510010,
                                              511.809998,
                                              536.410034,
                                              561.010010,
                                              515.710022,
                                              540.610046,
                                              565.510010,
                                              519.609985,
                                              544.810059,
                                              570.010010,
                                              523.510010,
                                              549.010010,
                                              574.510010},
                                             {0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000},
                                             {527.410034,
                                              553.210022,
                                              579.010010,
                                              531.309998,
                                              557.410034,
                                              583.510010,
                                              535.210022,
                                              561.610046,
                                              588.010010,
                                              539.109985,
                                              565.810059,
                                              592.510010,
                                              543.010010,
                                              570.010010,
                                              597.010010},
                                             {580.809998,
                                              605.110046,
                                              629.410034,
                                              585.609985,
                                              610.210022,
                                              634.809998,
                                              590.409973,
                                              615.310059,
                                              640.210022,
                                              595.210022,
                                              620.410034,
                                              645.609985,
                                              600.010010,
                                              625.510010,
                                              651.010010},
                                             {546.910034,
                                              574.210022,
                                              601.510010,
                                              550.809998,
                                              578.410034,
                                              606.010010,
                                              554.710022,
                                              582.610046,
                                              610.510010,
                                              558.609985,
                                              586.810059,
                                              615.010010,
                                              562.510010,
                                              591.010010,
                                              619.510010},
                                             {604.809998,
                                              630.610046,
                                              656.410034,
                                              609.609985,
                                              635.710022,
                                              661.809998,
                                              614.409973,
                                              640.810059,
                                              667.210022,
                                              619.210022,
                                              645.910034,
                                              672.609985,
                                              624.010010,
                                              651.010010,
                                              678.010010},
                                             {566.410034,
                                              595.210022,
                                              624.010010,
                                              570.309998,
                                              599.410034,
                                              628.510010,
                                              574.210022,
                                              603.610046,
                                              633.010010,
                                              578.109985,
                                              607.810059,
                                              637.510010,
                                              582.010010,
                                              612.010010,
                                              642.010010},
                                             {628.809998,
                                              656.110046,
                                              683.410034,
                                              633.609985,
                                              661.210022,
                                              688.809998,
                                              638.409973,
                                              666.310059,
                                              694.210022,
                                              643.210022,
                                              671.410034,
                                              699.609985,
                                              648.010010,
                                              676.510010,
                                              705.010010},
                                             {0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000},
                                             {652.809998,
                                              681.610046,
                                              710.410034,
                                              657.609985,
                                              686.710022,
                                              715.809998,
                                              662.409973,
                                              691.810059,
                                              721.210022,
                                              667.210022,
                                              696.910034,
                                              726.609985,
                                              672.010010,
                                              702.010010,
                                              732.010010}},

                                            {{585.910034,
                                              616.210022,
                                              646.510010,
                                              589.809998,
                                              620.410034,
                                              651.010010,
                                              593.710022,
                                              624.610046,
                                              655.510010,
                                              597.609985,
                                              628.810059,
                                              660.010010,
                                              601.510010,
                                              633.010010,
                                              664.510010},
                                             {0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000},
                                             {605.410034,
                                              637.210022,
                                              669.010010,
                                              609.309998,
                                              641.410034,
                                              673.510010,
                                              613.210022,
                                              645.610046,
                                              678.010010,
                                              617.109985,
                                              649.810059,
                                              682.510010,
                                              621.010010,
                                              654.010010,
                                              687.010010},
                                             {676.809998,
                                              707.110046,
                                              737.410034,
                                              681.609985,
                                              712.210022,
                                              742.809998,
                                              686.409973,
                                              717.310059,
                                              748.210022,
                                              691.210022,
                                              722.410034,
                                              753.609985,
                                              696.010010,
                                              727.510010,
                                              759.010010},
                                             {624.910034,
                                              658.210022,
                                              691.510010,
                                              628.809998,
                                              662.410034,
                                              696.010010,
                                              632.710022,
                                              666.610046,
                                              700.510010,
                                              636.609985,
                                              670.810059,
                                              705.010010,
                                              640.510010,
                                              675.010010,
                                              709.510010},
                                             {700.809998,
                                              732.610046,
                                              764.410034,
                                              705.609985,
                                              737.710022,
                                              769.809998,
                                              710.409973,
                                              742.810059,
                                              775.210022,
                                              715.210022,
                                              747.910034,
                                              780.609985,
                                              720.010010,
                                              753.010010,
                                              786.010010},
                                             {644.410034,
                                              679.210022,
                                              714.010010,
                                              648.309998,
                                              683.410034,
                                              718.510010,
                                              652.210022,
                                              687.610046,
                                              723.010010,
                                              656.109985,
                                              691.810059,
                                              727.510010,
                                              660.010010,
                                              696.010010,
                                              732.010010},
                                             {724.809998,
                                              758.110046,
                                              791.410034,
                                              729.609985,
                                              763.210022,
                                              796.809998,
                                              734.409973,
                                              768.310059,
                                              802.210022,
                                              739.210022,
                                              773.410034,
                                              807.609985,
                                              744.010010,
                                              778.510010,
                                              813.010010},
                                             {0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000},
                                             {748.809998,
                                              783.610046,
                                              818.410034,
                                              753.609985,
                                              788.710022,
                                              823.809998,
                                              758.409973,
                                              793.810059,
                                              829.210022,
                                              763.210022,
                                              798.910034,
                                              834.609985,
                                              768.010010,
                                              804.010010,
                                              840.010010}},

                                            {{663.910034,
                                              700.210022,
                                              736.510010,
                                              667.809998,
                                              704.410034,
                                              741.010010,
                                              671.710022,
                                              708.610046,
                                              745.510010,
                                              675.609985,
                                              712.810059,
                                              750.010010,
                                              679.510010,
                                              717.010010,
                                              754.510010},
                                             {0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000},
                                             {683.410034,
                                              721.210022,
                                              759.010010,
                                              687.309998,
                                              725.410034,
                                              763.510010,
                                              691.210022,
                                              729.610046,
                                              768.010010,
                                              695.109985,
                                              733.810059,
                                              772.510010,
                                              699.010010,
                                              738.010010,
                                              777.010010},
                                             {772.809998,
                                              809.110046,
                                              845.410034,
                                              777.609985,
                                              814.210022,
                                              850.809998,
                                              782.409973,
                                              819.310059,
                                              856.210022,
                                              787.210022,
                                              824.410034,
                                              861.609985,
                                              792.010010,
                                              829.510010,
                                              867.010010},
                                             {702.910034,
                                              742.210022,
                                              781.510010,
                                              706.809998,
                                              746.410034,
                                              786.010010,
                                              710.710022,
                                              750.610046,
                                              790.510010,
                                              714.609985,
                                              754.810059,
                                              795.010010,
                                              718.510010,
                                              759.010071,
                                              799.510010},
                                             {796.809998,
                                              834.610046,
                                              872.410034,
                                              801.609985,
                                              839.710022,
                                              877.810059,
                                              806.409973,
                                              844.810059,
                                              883.210022,
                                              811.210022,
                                              849.910034,
                                              888.609985,
                                              816.010010,
                                              855.010010,
                                              894.010010},
                                             {722.410034,
                                              763.210022,
                                              804.010010,
                                              726.309998,
                                              767.410034,
                                              808.510010,
                                              730.210022,
                                              771.610046,
                                              813.010010,
                                              734.109985,
                                              775.810059,
                                              817.510010,
                                              738.010010,
                                              780.010071,
                                              822.010010},
                                             {820.809998,
                                              860.110046,
                                              899.410034,
                                              825.609985,
                                              865.210022,
                                              904.810059,
                                              830.409973,
                                              870.310059,
                                              910.210022,
                                              835.210022,
                                              875.410034,
                                              915.609985,
                                              840.010010,
                                              880.510010,
                                              921.010010},
                                             {0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000},
                                             {844.809998,
                                              885.610046,
                                              926.410034,
                                              849.609985,
                                              890.710022,
                                              931.810059,
                                              854.409973,
                                              895.810059,
                                              937.210022,
                                              859.210022,
                                              900.910034,
                                              942.609985,
                                              864.010010,
                                              906.010010,
                                              948.010010}},

                                            {{741.910034,
                                              784.210022,
                                              826.510010,
                                              745.809998,
                                              788.410034,
                                              831.010010,
                                              749.710022,
                                              792.610046,
                                              835.510010,
                                              753.609985,
                                              796.810059,
                                              840.010010,
                                              757.510010,
                                              801.010071,
                                              844.510010},
                                             {0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000},
                                             {761.410034,
                                              805.210022,
                                              849.010010,
                                              765.310059,
                                              809.410034,
                                              853.510010,
                                              769.210022,
                                              813.610046,
                                              858.010010,
                                              773.109985,
                                              817.810059,
                                              862.510010,
                                              777.010010,
                                              822.010071,
                                              867.010010},
                                             {868.809998,
                                              911.110046,
                                              953.410034,
                                              873.609985,
                                              916.210022,
                                              958.810059,
                                              878.409973,
                                              921.310059,
                                              964.210022,
                                              883.210022,
                                              926.410034,
                                              969.609985,
                                              888.010010,
                                              931.510010,
                                              975.010010},
                                             {780.910034,
                                              826.210022,
                                              871.510010,
                                              784.810059,
                                              830.410034,
                                              876.010010,
                                              788.710022,
                                              834.610046,
                                              880.510010,
                                              792.609985,
                                              838.810059,
                                              885.010010,
                                              796.510010,
                                              843.010071,
                                              889.510010},
                                             {892.809998,
                                              936.610046,
                                              980.410034,
                                              897.609985,
                                              941.710022,
                                              985.810059,
                                              902.409973,
                                              946.810059,
                                              991.210022,
                                              907.210022,
                                              951.910034,
                                              996.609985,
                                              912.010010,
                                              957.010010,
                                              1002.010010},
                                             {800.410034,
                                              847.210022,
                                              894.010010,
                                              804.310059,
                                              851.410034,
                                              898.510010,
                                              808.210022,
                                              855.610046,
                                              903.010010,
                                              812.109985,
                                              859.810059,
                                              907.510010,
                                              816.010010,
                                              864.010071,
                                              912.010010},
                                             {916.809998,
                                              962.110046,
                                              1007.410034,
                                              921.609985,
                                              967.210022,
                                              1012.810059,
                                              926.409973,
                                              972.310059,
                                              1018.210022,
                                              931.210022,
                                              977.410034,
                                              1023.609985,
                                              936.010010,
                                              982.510010,
                                              1029.010010},
                                             {0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000,
                                              0.010000},
                                             {940.809998,
                                              987.610046,
                                              1034.410034,
                                              945.609985,
                                              992.710022,
                                              1039.810059,
                                              950.409973,
                                              997.810059,
                                              1045.209961,
                                              955.210022,
                                              1002.910034,
                                              1050.609985,
                                              960.010010,
                                              1008.010010,
                                              1056.010010}}},

                                           {{{653.720032,
                                              678.020020,
                                              702.320007,
                                              659.420044,
                                              684.020020,
                                              708.620056,
                                              665.120056,
                                              690.020020,
                                              714.920044,
                                              670.820007,
                                              696.020020,
                                              721.220032,
                                              676.520020,
                                              702.020020,
                                              727.520020},
                                             {0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000},
                                             {682.220032,
                                              708.020020,
                                              733.820007,
                                              687.920044,
                                              714.020020,
                                              740.120056,
                                              693.620056,
                                              720.020020,
                                              746.420044,
                                              699.320007,
                                              726.020020,
                                              752.720032,
                                              705.020020,
                                              732.020020,
                                              759.020020},
                                             {726.620056,
                                              750.920044,
                                              775.220032,
                                              733.220032,
                                              757.820007,
                                              782.420044,
                                              739.820068,
                                              764.720032,
                                              789.620056,
                                              746.420044,
                                              771.619995,
                                              796.820068,
                                              753.020020,
                                              778.520020,
                                              804.020081},
                                             {710.720032,
                                              738.020020,
                                              765.320007,
                                              716.420044,
                                              744.020020,
                                              771.620056,
                                              722.120056,
                                              750.020020,
                                              777.920044,
                                              727.820068,
                                              756.020020,
                                              784.220032,
                                              733.520020,
                                              762.020020,
                                              790.520020},
                                             {759.620056,
                                              785.420044,
                                              811.220032,
                                              766.220032,
                                              792.320007,
                                              818.420044,
                                              772.820068,
                                              799.220032,
                                              825.620056,
                                              779.420044,
                                              806.119995,
                                              832.820068,
                                              786.020020,
                                              813.020020,
                                              840.020081},
                                             {739.220032,
                                              768.020020,
                                              796.820007,
                                              744.920044,
                                              774.020020,
                                              803.120056,
                                              750.620056,
                                              780.020020,
                                              809.420044,
                                              756.320068,
                                              786.020020,
                                              815.720032,
                                              762.020020,
                                              792.020020,
                                              822.020020},
                                             {792.620056,
                                              819.920044,
                                              847.220032,
                                              799.220032,
                                              826.820007,
                                              854.420044,
                                              805.820068,
                                              833.720032,
                                              861.620056,
                                              812.420044,
                                              840.619995,
                                              868.820068,
                                              819.020020,
                                              847.520020,
                                              876.020081},
                                             {0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000},
                                             {825.620056,
                                              854.420044,
                                              883.220032,
                                              832.220032,
                                              861.320007,
                                              890.420044,
                                              838.820068,
                                              868.220032,
                                              897.620056,
                                              845.420044,
                                              875.119995,
                                              904.820068,
                                              852.020020,
                                              882.020020,
                                              912.020020}},

                                            {{767.720032,
                                              798.020020,
                                              828.320007,
                                              773.420044,
                                              804.020020,
                                              834.620056,
                                              779.120056,
                                              810.020020,
                                              840.920044,
                                              784.820068,
                                              816.020020,
                                              847.220032,
                                              790.520020,
                                              822.020020,
                                              853.520020},
                                             {0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000},
                                             {796.220032,
                                              828.020020,
                                              859.820007,
                                              801.920044,
                                              834.020020,
                                              866.120056,
                                              807.620056,
                                              840.020020,
                                              872.420044,
                                              813.320068,
                                              846.020020,
                                              878.720032,
                                              819.020020,
                                              852.020020,
                                              885.020020},
                                             {858.620056,
                                              888.920044,
                                              919.220032,
                                              865.220032,
                                              895.820007,
                                              926.420044,
                                              871.820068,
                                              902.719971,
                                              933.620056,
                                              878.420044,
                                              909.619995,
                                              940.820068,
                                              885.020020,
                                              916.520020,
                                              948.020020},
                                             {824.720032,
                                              858.020020,
                                              891.320068,
                                              830.420044,
                                              864.020020,
                                              897.620056,
                                              836.120056,
                                              870.020020,
                                              903.920044,
                                              841.820068,
                                              876.020020,
                                              910.220032,
                                              847.520020,
                                              882.020020,
                                              916.520020},
                                             {891.620056,
                                              923.420044,
                                              955.220032,
                                              898.220032,
                                              930.320007,
                                              962.420044,
                                              904.820068,
                                              937.219971,
                                              969.620056,
                                              911.420044,
                                              944.119995,
                                              976.820068,
                                              918.020020,
                                              951.020020,
                                              984.020020},
                                             {853.220032,
                                              888.020020,
                                              922.820068,
                                              858.920044,
                                              894.020020,
                                              929.120056,
                                              864.620056,
                                              900.020020,
                                              935.420044,
                                              870.320068,
                                              906.020020,
                                              941.720032,
                                              876.020020,
                                              912.020020,
                                              948.020020},
                                             {924.620056,
                                              957.920044,
                                              991.220032,
                                              931.220032,
                                              964.820007,
                                              998.420044,
                                              937.820068,
                                              971.719971,
                                              1005.620056,
                                              944.420044,
                                              978.619995,
                                              1012.820068,
                                              951.020020,
                                              985.520020,
                                              1020.020020},
                                             {0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000},
                                             {957.620056,
                                              992.420044,
                                              1027.220093,
                                              964.220032,
                                              999.320007,
                                              1034.420044,
                                              970.820068,
                                              1006.219971,
                                              1041.620117,
                                              977.420044,
                                              1013.119995,
                                              1048.820068,
                                              984.020020,
                                              1020.020020,
                                              1056.020020}},

                                            {{881.720032,
                                              918.020020,
                                              954.320068,
                                              887.420044,
                                              924.020020,
                                              960.620056,
                                              893.120056,
                                              930.020020,
                                              966.920044,
                                              898.820068,
                                              936.020020,
                                              973.220032,
                                              904.520020,
                                              942.020020,
                                              979.520020},
                                             {0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000},
                                             {910.220032,
                                              948.020020,
                                              985.820068,
                                              915.920044,
                                              954.020020,
                                              992.120056,
                                              921.620056,
                                              960.020020,
                                              998.420044,
                                              927.320068,
                                              966.020020,
                                              1004.720032,
                                              933.020020,
                                              972.020020,
                                              1011.020020},
                                             {990.620056,
                                              1026.920044,
                                              1063.220093,
                                              997.220032,
                                              1033.820068,
                                              1070.420044,
                                              1003.820068,
                                              1040.719971,
                                              1077.620117,
                                              1010.420044,
                                              1047.619995,
                                              1084.820068,
                                              1017.020020,
                                              1054.520020,
                                              1092.020020},
                                             {938.720032,
                                              978.020020,
                                              1017.320068,
                                              944.420044,
                                              984.020020,
                                              1023.620056,
                                              950.120056,
                                              990.020020,
                                              1029.920044,
                                              955.820068,
                                              996.020020,
                                              1036.220093,
                                              961.520081,
                                              1002.020020,
                                              1042.520020},
                                             {1023.620056,
                                              1061.420044,
                                              1099.220093,
                                              1030.220093,
                                              1068.320068,
                                              1106.420044,
                                              1036.820068,
                                              1075.219971,
                                              1113.620117,
                                              1043.420044,
                                              1082.119995,
                                              1120.820068,
                                              1050.020020,
                                              1089.020020,
                                              1128.020020},
                                             {967.220032,
                                              1008.020020,
                                              1048.820068,
                                              972.920044,
                                              1014.020020,
                                              1055.119995,
                                              978.620056,
                                              1020.020020,
                                              1061.420044,
                                              984.320068,
                                              1026.020020,
                                              1067.720093,
                                              990.020081,
                                              1032.020020,
                                              1074.020020},
                                             {1056.619995,
                                              1095.920044,
                                              1135.220093,
                                              1063.220093,
                                              1102.820068,
                                              1142.420044,
                                              1069.820068,
                                              1109.719971,
                                              1149.620117,
                                              1076.420044,
                                              1116.619995,
                                              1156.820068,
                                              1083.020020,
                                              1123.520020,
                                              1164.020020},
                                             {0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000},
                                             {1089.619995,
                                              1130.420044,
                                              1171.220093,
                                              1096.220093,
                                              1137.320068,
                                              1178.420044,
                                              1102.820068,
                                              1144.219971,
                                              1185.620117,
                                              1109.420044,
                                              1151.119995,
                                              1192.820068,
                                              1116.020020,
                                              1158.020020,
                                              1200.020020}},

                                            {{995.720032,
                                              1038.020020,
                                              1080.320068,
                                              1001.420044,
                                              1044.020020,
                                              1086.619995,
                                              1007.120056,
                                              1050.020020,
                                              1092.920044,
                                              1012.820068,
                                              1056.020020,
                                              1099.220093,
                                              1018.520081,
                                              1062.020020,
                                              1105.520020},
                                             {0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000},
                                             {1024.220093,
                                              1068.020020,
                                              1111.820068,
                                              1029.920044,
                                              1074.020020,
                                              1118.119995,
                                              1035.619995,
                                              1080.020020,
                                              1124.420044,
                                              1041.320068,
                                              1086.020020,
                                              1130.720093,
                                              1047.020020,
                                              1092.020020,
                                              1137.020020},
                                             {1122.619995,
                                              1164.920044,
                                              1207.220093,
                                              1129.220093,
                                              1171.820068,
                                              1214.420044,
                                              1135.820068,
                                              1178.719971,
                                              1221.620117,
                                              1142.420044,
                                              1185.619995,
                                              1228.820068,
                                              1149.020020,
                                              1192.520020,
                                              1236.020020},
                                             {1052.720093,
                                              1098.020020,
                                              1143.320068,
                                              1058.420044,
                                              1104.020020,
                                              1149.619995,
                                              1064.119995,
                                              1110.020020,
                                              1155.920044,
                                              1069.820068,
                                              1116.020020,
                                              1162.220093,
                                              1075.520020,
                                              1122.020020,
                                              1168.520020},
                                             {1155.619995,
                                              1199.420044,
                                              1243.220093,
                                              1162.220093,
                                              1206.320068,
                                              1250.420044,
                                              1168.820068,
                                              1213.219971,
                                              1257.620117,
                                              1175.420044,
                                              1220.119995,
                                              1264.820068,
                                              1182.020020,
                                              1227.020020,
                                              1272.020020},
                                             {1081.220093,
                                              1128.020020,
                                              1174.820068,
                                              1086.920044,
                                              1134.020020,
                                              1181.119995,
                                              1092.619995,
                                              1140.020020,
                                              1187.420044,
                                              1098.320068,
                                              1146.020020,
                                              1193.720093,
                                              1104.020020,
                                              1152.020020,
                                              1200.020020},
                                             {1188.619995,
                                              1233.920044,
                                              1279.220093,
                                              1195.220093,
                                              1240.820068,
                                              1286.420044,
                                              1201.820068,
                                              1247.719971,
                                              1293.620117,
                                              1208.420044,
                                              1254.619995,
                                              1300.820068,
                                              1215.020020,
                                              1261.520020,
                                              1308.020020},
                                             {0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000,
                                              0.020000},
                                             {1221.619995,
                                              1268.420044,
                                              1315.220093,
                                              1228.220093,
                                              1275.320068,
                                              1322.420044,
                                              1234.820068,
                                              1282.219971,
                                              1329.620117,
                                              1241.420044,
                                              1289.119995,
                                              1336.820068,
                                              1248.020020,
                                              1296.020020,
                                              1344.020020}}}}}}));
        }
    }
}

} // namespace Aidge
