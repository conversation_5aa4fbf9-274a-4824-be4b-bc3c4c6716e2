set(CATCH2_MIN_VERSION 3.3.0)

find_package(Catch2 ${CATCH2_MIN_VERSION} QUIET)

if(NOT Catch2_FOUND)
    message(STATUS "Catch2 not found in system, retrieving from git")
    Include(FetchContent)

    FetchContent_Declare(
      Catch2
      GIT_REPOSITORY https://github.com/catchorg/Catch2.git
      GIT_TAG        devel # or a later release
    )

    FetchContent_MakeAvailable(Catch2)
else()
    message(STATUS "Found system Catch2 version ${Catch2_VERSION}")
endif()

file(GLOB_RECURSE src_files "*.cpp")

add_executable(tests${module_name} ${src_files})

if (WIN32)
    target_compile_definitions(tests${module_name} PRIVATE _USE_MATH_DEFINES)
endif()

target_link_libraries(tests${module_name} PRIVATE ${module_name})

target_link_libraries(tests${module_name} PRIVATE Catch2::Catch2WithMain)

target_compile_options(tests${module_name} PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:
    /bigobj>)

list(APPEND CMAKE_MODULE_PATH ${catch2_SOURCE_DIR}/extras)
include(CTest)
include(Catch)
catch_discover_tests(tests${module_name})
