#ifndef VERSION_H
#define VERSION_H

namespace Aidge {
static constexpr const int PROJECT_VERSION_MAJOR = @PROJECT_VERSION_MAJOR@;
static constexpr const int PROJECT_VERSION_MINOR = @PROJECT_VERSION_MINOR@;
static constexpr const int PROJECT_VERSION_PATCH = @PROJECT_VERSION_PATCH@;
static constexpr const char * PROJECT_VERSION = "@PROJECT_VERSION_MAJOR@.@PROJECT_VERSION_MINOR@.@PROJECT_VERSION_PATCH@";
static constexpr const char * PROJECT_GIT_HASH = "@GIT_COMMIT_HASH@";
}
#endif // VERSION_H
