/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_CONVIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_CONVIMPL_KERNELS_H_

#include <algorithm>
#include <array>
#include <cstddef>
#include <cstdint>

#include "aidge/backend/cpu/operator/ConvImpl.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {
using std::array;

/**
 * @brief Forward kernel for 1D Convolution on CPU backend.
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param params tuple of Attributes from the Operator
 * @param inputDims Array of input dimensions.
 * @param input_ const input Tensor.
 * @param weights_ const weight Tensor.
 * @param biases_ const Biais Tensor.
 * @param output_ Output Tensor.
 */
template <class I, class W, class B, class O>
void ConvImpl1D_cpu_forward_kernel(const array<DimSize_t, 1> &strideDim,
                                   const array<DimSize_t, 1> &dilationDim,
                                   const array<DimSize_t, 1> &kernelDim,
                                   const std::array<DimSize_t, 3> &inputDims,
                                   DimSize_t outChannels,
                                   const void *input_,
                                   const void *weights_,
                                   const void *biases_,
                                   void *output_) {
    // FIXME: missing convolution attributes as arguments
    const I *input = static_cast<const I *>(input_);
    const W *weights = static_cast<const W *>(weights_);
    const B *biases = static_cast<const B *>(biases_);
    O *output = static_cast<O *>(output_);

    // output H size
    const std::size_t oxSize = static_cast<std::size_t>(std::floor(
        static_cast<float>(inputDims[2] - dilationDim[0] * (kernelDim[0] - 1) -
                           1 + strideDim[0]) /
        static_cast<float>(strideDim[0])));
    const DimSize_t dilated_kernel_x = dilationDim[0] * (kernelDim[0] - 1) + 1;

    using signedsize = std::make_signed<std::size_t>::type;
#ifdef _OPENMP
    #pragma omp parallel for collapse(2) if (inputDims[0] * outChannels >= 16)
#endif
    for (int batch = 0; batch < static_cast<int>(inputDims[0]); ++batch) {
        for (int outCh = 0; outCh < static_cast<int>(outChannels); ++outCh) {
            const std::size_t oIndex = (outCh + batch * outChannels) * oxSize;
            // If bias = nullptr, set B(0)
            B biasVal = (biases != nullptr) ? biases[outCh] : B(0);
            std::fill(output + oIndex, output + (oIndex + oxSize), biasVal);
            for (std::size_t inCh = 0; inCh < inputDims[1]; ++inCh) {
                const std::size_t iIndex =
                    (inCh + batch * inputDims[1]) * inputDims[2];
                const std::size_t wIndex =
                    (inCh + outCh * inputDims[1]) * kernelDim[0];
                for (std::size_t ox = 0; ox < oxSize; ++ox) {
                    const std::size_t sxMin = 0;
                    const std::size_t sxMax = dilated_kernel_x;
                    const std::size_t oIndexFull = oIndex + ox;
                    const signedsize ix =
                        static_cast<signedsize>(ox * strideDim[0]);

                    for (std::size_t sx = sxMin; sx * dilationDim[0] < sxMax;
                         ++sx) {
                        output[oIndexFull] +=
                            weights[wIndex + sx] *
                            input[iIndex + static_cast<std::size_t>(
                                               ix + static_cast<signedsize>(
                                                        sx * dilationDim[0]))];
                    }
                }
            }
        }
    }
}

/**
 * @brief perform 1D backpropagation for the data input
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * @note formula :
 * for i in 0..input_size:
 *  for n in 0..weight_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *    dXi    dXi  Yn
 * with : dYn / dXi = w_k
 * for each input value
 * for each weight
 * for each output
 * multiply the weight with the associated value
 * @note kernel & stride are passed as single integers as they are just arrays
 * of length 1
 * @note reminder that kernel dimensions are
 * {outChannels, inChannels, {kernelDims}}
 * <=> {oDims[1], iDims[1], kernelDim}
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam O Output data type.
 * @param[in] stride stride parameter of the convolution operator
 * @param[in] dilation dilation parameter of the convolution operator
 * @param[in] kDims dimension of the kernel
 * @param[in] kStrides nb of elements contained per dimension of the kernel
 * @param[in] weights kernel weights
 * @param[in] oDims dimensions of the output
 * @param[in] oStrides nb of elements contained per dimension of the output
 * @param[in] oGrad output gradient
 * @param[in] iDims input dimensions
 * @param[in] iStrides nb of elements contained per dimension of the input
 * @param[inout] iGrad gradients of the input to update
 */
template <class I, class W, class O>
void conv1DBackwardInput(const array<DimSize_t, 1> &stride,
                         const array<DimSize_t, 1> &dilation,
                         const array<DimSize_t, 1> &kDim,
                         const array<DimSize_t, 2> &kStrides,
                         const W *weights,
                         const array<DimSize_t, 3> &oDims,
                         const array<DimSize_t, 2> &oStrides,
                         const O *oGrad,
                         const array<DimSize_t, 3> &iDims,
                         const array<DimSize_t, 2> &iStrides,
                         I *iGrad) {

    array<DimSize_t, 2> iOffsets{0, 0};
    array<DimSize_t, 2> oOffsets{0, 0};
    array<DimSize_t, 2> kOffsets{0, 0};

    for (std::size_t batch = 0; batch < iDims[0]; ++batch) {
        iOffsets[0] = batch * iStrides[0];
        oOffsets[0] = batch * oStrides[0];

        for (DimSize_t oChannel = 0; oChannel < oDims[1]; oChannel++) {
            oOffsets[1] = (oChannel * oStrides[1]) + oOffsets[0];
            kOffsets[0] = oChannel * kStrides[0];

            for (std::size_t iChannel = 0; iChannel < iDims[1]; ++iChannel) {
                iOffsets[1] = (iChannel * iStrides[1]) + iOffsets[0];
                kOffsets[1] = iChannel * kStrides[1] + kOffsets[0];

                for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                    auto iX = oX * stride[0];
                    auto inIdx = iX + iOffsets[1];

                    for (DimSize_t kX = 0; kX < kDim[0]; ++kX) {
                        auto dilatedKernelIdx = kX * dilation[0];

                        iGrad[inIdx + dilatedKernelIdx] +=
                            weights[kOffsets[1] + kX] *
                            oGrad[oOffsets[1] + oX];
                    }
                }
            }
        }
    }
}

/**
 * @brief computes weight backpropagation for conv1D
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * weight grad
 * for i in 0..weight_size:
 *  for n in 0..output_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *   dwi     dwi  Yn
 * with : dYn / dwi = x_k
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam O Output data type.
 * @param[in] stride stride parameter of the convolution operator
 * @param[in] dilation dilation parameter of the convolution operator
 * @param[in] iDims input dimensions
 * @param[in] iStrides nb of elements contained per dimension of the input
 * @param[inout] iGrad gradients of the input to update
 * @param[in] oDims dimensions of the output
 * @param[in] oStrides nb of elements contained per dimension of the output
 * @param[in] oGrad output gradient
 * @param[in] kDims dimension of the kernel
 * @param[in] kStrides nb of elements contained per dimension of the kernel
 * @param[in] weights kernel weights
 */
template <class I, class W, class O>
static void conv1DBackwardWeights(const array<DimSize_t, 1> &stride,
                                  const array<DimSize_t, 1> &dilation,
                                  const array<DimSize_t, 3> &iDims,
                                  const array<DimSize_t, 2> iStrides,
                                  const I *input,
                                  const array<DimSize_t, 3> &oDims,
                                  const array<DimSize_t, 2> oStrides,
                                  const O *oGrad,
                                  const array<DimSize_t, 1> &kDim,
                                  const array<DimSize_t, 2> kStrides,
                                  W *weightsGrad) {

    array<DimSize_t, 2> iOffsets{0, 0};
    array<DimSize_t, 2> oOffsets{0, 0};
    array<DimSize_t, 2> kOffsets{0, 0};

    for (DimSize_t batch = 0; batch < oDims[0]; ++batch) {
        iOffsets[0] = batch * iStrides[0];
        oOffsets[0] = batch * oStrides[0];

        for (DimSize_t oChannel = 0; oChannel < oDims[1]; ++oChannel) {
            oOffsets[1] = oChannel * oStrides[1] + oOffsets[0];
            kOffsets[0] = oChannel * kStrides[0];

            for (DimSize_t iChannel = 0; iChannel < iDims[1]; ++iChannel) {
                kOffsets[1] = iChannel * kStrides[1] + kOffsets[0];
                iOffsets[1] = iChannel * iStrides[1] + iOffsets[0];
                oOffsets[1] = oChannel * oStrides[1] + oOffsets[0];

                for (DimSize_t kX = 0; kX < kDim[0]; ++kX) {

                    for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                        const DimSize_t iX = oX * stride[0] + kX * dilation[0];

                        weightsGrad[kOffsets[1] + kX] +=
                            input[iOffsets[1] + iX] * oGrad[oOffsets[1] + oX];
                    }
                }
            }
        }
    }
}

/**
 * @brief computes bias backpropagation for conv1D operation
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * @note formula :
 * Bias grad:
 * for i in 0..bias_size:
 *  for n in 0..output_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *   dbi     dbi  Yn
 * with : dYn / dbi = 1
 *
 * Hence the partial derivative of the loss wrt bias is the
 * output loss. Hence the bias grad is just the sum of the
 * loss values over the batch
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param[in] oDims output tensor dimensions
 * @param[in] oStrides nb of elements contained per dimension of the output
 * tensor
 * @param[in] oGrad output tensor gradients
 * @param[inout] biasesGrad biases gradients
 */
template <class B, class O>
static void conv1DBackwardBias(const array<DimSize_t, 3> &oDims,
                               const array<DimSize_t, 2> &oStrides,
                               const O *oGrad,
                               B *biasesGrad) {
    array<DimSize_t, 2> oOffsets{0, 0};

    for (DimSize_t batchIdx = 0; batchIdx < oDims[0]; ++batchIdx) {
        oOffsets[0] = batchIdx * oStrides[0];

        for (DimSize_t oChannel = 0; oChannel < oDims[1]; ++oChannel) {
            oOffsets[1] = oChannel * oStrides[1] + oOffsets[0];

            for (DimSize_t oIdx = 0; oIdx < oDims[2]; oIdx++) {
                biasesGrad[oChannel] += oGrad[oOffsets[1] + oIdx];
            }
        }
    }
}

/**
 * @brief Backward kernel for 1D Convolution on CPU backend.
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 *
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param[in] const stride
 * @param[in] const kernelDims
 * @param[in] const iDims input data dimensions
 * @param[in] const oDims output data dimmensions
 * @param[in] const oChannels output channel number
 * @param[in] const input_ const input Tensor.
 * @param[in] const weights_ const weight Tensor.
 * @param[in] const biases_ const Biais Tensor.
 * @param[in] const output_ Output Tensor.
 * @param[in] const oGrad_ gradients of output data
 * @param[inout] iGrad_ gradients of input data
 * @param[inout] weightsGrad_ gradients of the kernel weights
 * @param[inout] biasesGrad_ gradients of the kernel biases
 */
template <class I, class W, class B, class O>
void ConvImpl1D_cpu_backward_kernel(const array<DimSize_t, 1> &stride,
                                    const array<DimSize_t, 1> &dilation,
                                    const array<DimSize_t, 1> &kernelDim,
                                    const array<DimSize_t, 3> &inputDims,
                                    const array<DimSize_t, 3> &outputDims,
                                    const void *input_,
                                    const void *weights_,
                                    const void *oGrad_,
                                    void *iGrad_,
                                    void *weightsGrad_,
                                    void *biasesGrad_) {

    const I *input = static_cast<const I *>(input_);
    I *iGrad = static_cast<I *>(iGrad_);
    const I *oGrad = static_cast<const I *>(oGrad_);
    const W *weights = static_cast<const W *>(weights_);
    W *weightsGrad = static_cast<W *>(weightsGrad_);

    //////////////////////////////
    // COMPUTING STRIDES
    //////////////////////////////
    // NOTE: The ...Stride var represent the number of values contained in
    // each dimension they will be used to compute the index offset of
    // values while iterating on each tensor
    // NOTE: They are 1 item shorter than their corresponding tensor as the
    // number of total elements is not used except for gradient initialization

    // {batch_stride, channel_stride, dim0_stride, dim1_stride}
    const array<DimSize_t, 2> inputStrides{inputDims[1] * inputDims[2],
                                           inputDims[2]};
    const DimSize_t nbEltsInput = inputDims[0] * inputStrides[0];

    // {batch_stride, channel_stride, dim0_stride, dim1_stride}
    const array<DimSize_t, 2> outputStrides{outputDims[1] * outputDims[2],
                                            outputDims[2]};

    // NOTE: kernel dims = {iChannel, oChannel, kernelDim0, kernelDim1}
    // kernel_strides = {iChannel, oChannel, kernelDim0}
    const array<DimSize_t, 2> kernelStrides{
        inputDims[1] * kernelDim[0],
        kernelDim[0],
    };
    const DimSize_t nbEltsKernel = outputDims[1] * kernelStrides[0];

    std::fill(iGrad, iGrad + nbEltsInput, I(0));
    std::fill(weightsGrad, weightsGrad + nbEltsKernel, W(0));

    conv1DBackwardInput(stride,
                        dilation,
                        kernelDim,
                        kernelStrides,
                        weights,
                        outputDims,
                        outputStrides,
                        oGrad,
                        inputDims,
                        inputStrides,
                        iGrad);

    conv1DBackwardWeights(stride,
                          dilation,
                          inputDims,
                          inputStrides,
                          input,
                          outputDims,
                          outputStrides,
                          oGrad,
                          kernelDim,
                          kernelStrides,
                          weightsGrad);

    if (biasesGrad_ != nullptr) {
        B *biasesGrad = static_cast<B *>(biasesGrad_);
        std::fill(biasesGrad, biasesGrad + outputDims[1], B(0));
        conv1DBackwardBias(outputDims, outputStrides, oGrad, biasesGrad);
    }
}

// Kernels registration to implementation entry point
REGISTRAR(ConvImpl1D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Float32, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl1D_cpu_forward_kernel<float, float, float, float>,
           ConvImpl1D_cpu_backward_kernel<float, float, float, float>});
REGISTRAR(ConvImpl1D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Float16, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl1D_cpu_forward_kernel<half_float::half,
                                         half_float::half,
                                         half_float::half,
                                         half_float::half>,
           ConvImpl1D_cpu_backward_kernel<half_float::half,
                                          half_float::half,
                                          half_float::half,
                                          half_float::half>});
REGISTRAR(ConvImpl1D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Float64, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl1D_cpu_forward_kernel<double, double, double, double>,
           ConvImpl1D_cpu_backward_kernel<double, double, double, double>});
REGISTRAR(ConvImpl1D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Int32, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl1D_cpu_forward_kernel<std::int32_t,
                                         std::int32_t,
                                         std::int32_t,
                                         std::int32_t>,
           ConvImpl1D_cpu_backward_kernel<std::int32_t,
                                          std::int32_t,
                                          std::int32_t,
                                          std::int32_t>});

/**
 * @brief Forward kernel for 2D Convolution on CPU backend.
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param params tuple of Attributes from the Operator
 * @param inputDims Array of input dimensions.
 * @param input_ const input Tensor.
 * @param weights_ const weight Tensor.
 * @param biases_ const Biais Tensor.
 * @param output_ Output Tensor.
 */
template <class I, class W, class B, class O>
void ConvImpl2D_cpu_forward_kernel(const array<DimSize_t, 2> &strideDims,
                                   const array<DimSize_t, 2> &dilationDims,
                                   const array<DimSize_t, 2> &kernelDims,
                                   const array<DimSize_t, 4> &inputDims,
                                   DimSize_t outChannels,
                                   const void *input_,
                                   const void *weights_,
                                   const void *biases_,
                                   void *output_) {
    // FIXME: missing convolution attributes as arguments
    const I *input = static_cast<const I *>(input_);
    const W *weights = static_cast<const W *>(weights_);
    const B *biases = static_cast<const B *>(biases_);
    O *output = static_cast<O *>(output_);

    // output H size
    const DimSize_t dilated_kernel_x =
        dilationDims[0] * (kernelDims[0] - 1) + 1;
    const std::size_t oxSize = static_cast<std::size_t>(std::floor(
        static_cast<float>(inputDims[2] - dilated_kernel_x + strideDims[0]) /
        static_cast<float>(strideDims[0])));
    // output W size
    const DimSize_t dilated_kernel_y =
        dilationDims[1] * (kernelDims[1] - 1) + 1;
    const std::size_t oySize = static_cast<std::size_t>(std::floor(
        static_cast<float>(inputDims[3] - dilated_kernel_y + strideDims[1]) /
        static_cast<float>(strideDims[1])));

    // TODO: kernel computation
    // output (batch, outCh, Xout, Yout)
    // input  (batch, inCh, Xin, Yin)
    // weight (outCh, inCh, kernelX, kernelY)
    // does not take Dilation attribute into account
    const std::size_t outChannels_s = oxSize * oySize;

    if (dilated_kernel_x == 3 && dilated_kernel_y == 3) {
#ifdef _OPENMP
        #pragma omp parallel for collapse(2) if (inputDims[0] * outChannels >= 16)
#endif
        for (int batch = 0; batch < static_cast<int>(inputDims[0]); ++batch) {
            for (int outCh = 0; outCh < static_cast<int>(outChannels); ++outCh) {
                std::size_t oIndex = (outCh + batch*outChannels) * outChannels_s;

                // If bias = nullptr, set B(0)
                B biasVal = (biases != nullptr) ? biases[outCh] : B(0);
                std::fill(output + oIndex, output + oIndex + outChannels_s, biasVal);
                for (std::size_t inCh = 0; inCh < inputDims[1]; ++inCh) {
                    oIndex = (outCh + batch*outChannels) * outChannels_s;
                    std::size_t iIndex = (inCh + batch * inputDims[1]) *
                                         inputDims[2] * inputDims[3];
                    const std::size_t wIndex =
                        (inCh + outCh * inputDims[1]) * 9;
                    if (strideDims[0] == 1 && strideDims[1] == 1) {
                        for (std::size_t ox = 0; ox < oxSize;
                             ++ox, oIndex += oySize, iIndex -= inputDims[3]) {
                            for (std::size_t oy = 0; oy < oySize; ++oy) {
                                output[oIndex + oy] +=
                                    weights[wIndex + 0] * input[iIndex + oy] +
                                    weights[wIndex + 1] *
                                        input[iIndex + oy + 1] +
                                    weights[wIndex + 2] *
                                        input[iIndex + oy + 2];
                            }
                            iIndex += inputDims[3];
                            for (std::size_t oy = 0; oy < oySize; ++oy) {
                                output[oIndex + oy] +=
                                    weights[wIndex + 3] * input[iIndex + oy] +
                                    weights[wIndex + 4] *
                                        input[iIndex + oy + 1] +
                                    weights[wIndex + 5] *
                                        input[iIndex + oy + 2];
                            }
                            iIndex += inputDims[3];
                            for (std::size_t oy = 0; oy < oySize; ++oy) {
                                output[oIndex + oy] +=
                                    weights[wIndex + 6] * input[iIndex + oy] +
                                    weights[wIndex + 7] *
                                        input[iIndex + oy + 1] +
                                    weights[wIndex + 8] *
                                        input[iIndex + oy + 2];
                            }
                        }
                    } else {
                        for (std::size_t ox = 0; ox < oxSize; ++ox,
                                         oIndex += oySize,
                                         iIndex += (strideDims[0] -
                                                    2) * inputDims[3]) {
                            for (std::size_t oy = 0; oy < oySize; ++oy) {
                                output[oIndex + oy] +=
                                    weights[wIndex + 0] *
                                        input[iIndex + oy * strideDims[1]] +
                                    weights[wIndex + 1] *
                                        input[iIndex + oy * strideDims[1] +
                                              1] +
                                    weights[wIndex + 2] *
                                        input[iIndex + oy * strideDims[1] + 2];
                            }
                            iIndex += inputDims[3];
                            for (std::size_t oy = 0; oy < oySize; ++oy) {
                                output[oIndex + oy] +=
                                    weights[wIndex + 3] *
                                        input[iIndex + oy * strideDims[1]] +
                                    weights[wIndex + 4] *
                                        input[iIndex + oy * strideDims[1] +
                                              1] +
                                    weights[wIndex + 5] *
                                        input[iIndex + oy * strideDims[1] + 2];
                            }
                            iIndex += inputDims[3];
                            for (std::size_t oy = 0; oy < oySize; ++oy) {
                                output[oIndex + oy] +=
                                    weights[wIndex + 6] *
                                        input[iIndex + oy * strideDims[1]] +
                                    weights[wIndex + 7] *
                                        input[iIndex + oy * strideDims[1] +
                                              1] +
                                    weights[wIndex + 8] *
                                        input[iIndex + oy * strideDims[1] + 2];
                            }
                        }
                    }
                }
            }
        }
    } else if (dilated_kernel_x == 1 && dilated_kernel_y == 1) {
#ifdef _OPENMP
        #pragma omp parallel for collapse(2) if (inputDims[0] * outChannels >= 16)
#endif
        for (int batch = 0; batch < static_cast<int>(inputDims[0]); ++batch) {
            for (int outCh = 0; outCh < static_cast<int>(outChannels); ++outCh) {
                std::size_t oIndex = (outCh + batch*outChannels) * outChannels_s;

                // If bias = nullptr, set B(0)
                B biasVal = (biases != nullptr) ? biases[outCh] : B(0);
                std::fill(output + oIndex, output + oIndex + outChannels_s, biasVal);
                for (std::size_t inCh = 0; inCh < inputDims[1]; ++inCh) {
                    oIndex = (outCh + batch*outChannels) * outChannels_s;
                    std::size_t iIndex = (inCh + batch * inputDims[1]) *
                                         inputDims[2] * inputDims[3];
                    const std::size_t wIndex = (inCh + outCh * inputDims[1]);
                    if (strideDims[0] == 1 && strideDims[1] == 1) {
                        for (std::size_t i = 0; i < outChannels_s; ++i) {
                            output[oIndex + i] += weights[wIndex] * input[iIndex + i];
                        }
                    } else {
                        for (std::size_t ox = 0; ox < oxSize;
                             ++ox,
                                         oIndex += oySize,
                                         iIndex +=
                                         inputDims[3] * strideDims[0]) {
                            for (std::size_t oy = 0, iy = 0; oy < oySize;
                                 ++oy, iy += strideDims[1]) {
                                output[oIndex + oy] +=
                                    weights[wIndex + 0] * input[iIndex + iy];
                            }
                        }
                    }
                }
            }
        }
    } else {
#ifdef _OPENMP
        #pragma omp parallel for collapse(2) if (inputDims[0] * outChannels >= 16)
#endif
        for (int batch = 0; batch < static_cast<int>(inputDims[0]); ++batch) {
            for (int outCh = 0; outCh < static_cast<int>(outChannels); ++outCh) {
                std::size_t oIndex = (outCh + batch*outChannels) * outChannels_s;

                // If bias = nullptr, set B(0)
                B biasVal = (biases != nullptr) ? biases[outCh] : B(0);
                std::fill(output + oIndex, output + oIndex + outChannels_s, biasVal);
                for (std::size_t inCh = 0; inCh < inputDims[1]; ++inCh) {
                    oIndex = (outCh + batch*outChannels) * outChannels_s;
                    std::size_t iIndex_channel =
                        (inCh + batch * inputDims[1]) * inputDims[2] *
                        inputDims[3];
                    const std::size_t wIndex = (inCh + outCh * inputDims[1]) *
                                               kernelDims[0] * kernelDims[1];

                    // loop over each ouput line
                    for (std::size_t ox = 0; ox < oxSize;
                         ++ox,
                                     oIndex += oySize,
                                     iIndex_channel +=
                                     inputDims[3] * strideDims[0]) {
                        // loop over associated input line
                        for (std::size_t ky = 0, ix = 0; ky < kernelDims[1];
                             ++ky, ix += inputDims[3] * dilationDims[0]) {
                            // loop over the entire line
                            for (std::size_t oy = 0, iy = 0; oy < oySize;
                                 ++oy, iy += strideDims[1]) {
                                const std::size_t iIndex =
                                    iIndex_channel + ix + iy;
                                // loop over elements assosicated with one
                                // output
                                for (std::size_t kx = 0; kx < kernelDims[0];
                                     ++kx) {
                                    output[oIndex + oy] +=
                                        weights[wIndex + kernelDims[0] * ky +
                                                kx] *
                                        input[iIndex + kx * dilationDims[1]];
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief perform backpropagation for the input
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * @note formula :
 * for i in 0..input_size:
 *  for n in 0..weight_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *    dXi    dXi  Yn
 * with : dYn / dXi = w_k
 * for each input value
 * for each weight
 * for each output
 * multiply the weight with the associated value
 * @note kernel & stride are passed as single integers as they are just arrays
 * of length 1
 * @note reminder that kernel dimensions are
 * {outChannels, inChannels, {kernelDims}}
 * <=> {oDims[1], iDims[1], kernelDim}
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam O Output data type.
 * @param[in] stride stride parameter of the convolution operator
 * @param[in] dilation dilation parameter of the convolution operator
 * @param[in] kDims dimension of the kernel
 * @param[in] kStrides nb of elements contained per dimension of the kernel
 * @param[in] weights weights values
 * @param[in] oDims dimensions of the output
 * @param[in] oStrides nb of elements contained per dimension of the output
 * @param[in] oGrad output gradient
 * @param[in] iDims input dimensions
 * @param[in] iStrides nb of elements contained per dimension of the input
 * @param[inout] iGrad gradients of the input to update
 */
template <class I, class W, class O>
void conv2DBackwardInput(const array<DimSize_t, 2> &stride,
                         const array<DimSize_t, 2> &dilation,
                         const array<DimSize_t, 2> &kDims,
                         const array<DimSize_t, 3> &kStrides,
                         const W *weights,
                         const array<DimSize_t, 4> &oDims,
                         const array<DimSize_t, 3> &oStrides,
                         const O *oGrad,
                         const array<DimSize_t, 4> &iDims,
                         const array<DimSize_t, 3> &iStrides,
                         I *iGrad) {
    // records index offsets for each dimension that have a stride (== all
    // dimension except the last) for every parsed tensor
    array<DimSize_t, 3> kOffset{};
    array<DimSize_t, 3> iOffset{};
    array<DimSize_t, 3> oOffset{};

    for (std::size_t batch = 0; batch < iDims[0]; ++batch) {
        iOffset[0] = batch * iStrides[0];
        oOffset[0] = batch * oStrides[0];

        for (DimSize_t oChannel = 0; oChannel < oDims[1]; oChannel++) {
            oOffset[1] = (oChannel * oStrides[1]) + oOffset[0];
            kOffset[0] = (oChannel * kStrides[0]);

            for (std::size_t iChannel = 0; iChannel < iDims[1]; ++iChannel) {
                iOffset[1] = (iChannel * iStrides[1]) + iOffset[0];
                kOffset[1] = iChannel * kStrides[1] + kOffset[0];

                for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                    oOffset[2] = (oX * oStrides[2]) + oOffset[1];

                    auto iX = oX * stride[0];
                    iOffset[2] = (iX * iStrides[2]) + iOffset[1];

                    for (DimSize_t oY = 0; oY < oDims[3]; ++oY) {
                        auto oIdx = oOffset[2] + oY;

                        auto iY = oY * stride[1];
                        auto iIdx = iOffset[2] + iY;

                        for (DimSize_t kX = 0; kX < kDims[0]; ++kX) {
                            auto kDilX = kX * dilation[0];
                            auto iDilKXOffset = kDilX * iStrides[2];

                            kOffset[2] = (kX * kStrides[2]) + kOffset[1];

                            for (DimSize_t kY = 0; kY < kDims[1]; ++kY) {
                                auto kDilY = kY * dilation[1];

                                iGrad[iIdx + iDilKXOffset + kDilY] +=
                                    weights[kOffset[2] + kY] * oGrad[oIdx];
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief computes weight backpropagation for conv2D operation
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * weight grad
 * for i in 0..weight_size:
 *  for n in 0..output_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *   dwi     dwi  Yn
 * with : dYn / dwi = x_k
 * @tparam I input dtype
 * @tparam W weight dtype
 * @tparam O output dtype
 * @param[in] iDims input data dimensions
 * @param[in] iBatchStride nb element in each input data batch
 * @param[in] iChannelStride nb element in each input data channel
 * @param[in] input input data
 * @param[in] oDims output data dimmensions
 * @param[in] oBatchStride nb element in each output data batch
 * @param[in] oChannelStride nb element in each output data channel
 * @param[in] oGrad gradients of output data
 * @param[in] stride
 * @param[in] kernelDims
 * @param[inout] weightsGrad gradients of the kernel weights
 */
template <class I, class W, class O>
void conv2DBackwardWeights(const array<DimSize_t, 4> &iDims,
                           const array<DimSize_t, 3> &iStrides,
                           const I *input,
                           const array<DimSize_t, 4> &oDims,
                           const array<DimSize_t, 3> &oStrides,
                           const O *oGrad,
                           const array<DimSize_t, 2> &kDim,
                           const array<DimSize_t, 3> &kStrides,
                           const array<DimSize_t, 2> &stride,
                           const array<DimSize_t, 2> &dilation,
                           W *weightsGrad) {
    // records index offsets for each dimension that have a stride (== all
    // dimension except the last) for every parsed tensor
    array<DimSize_t, 3> iOffsets{0, 0, 0};
    array<DimSize_t, 3> oOffsets{0, 0, 0};
    array<DimSize_t, 3> kOffsets{0, 0, 0};

    for (DimSize_t batchIdx = 0; batchIdx < oDims[0]; ++batchIdx) {
        iOffsets[0] = batchIdx * iStrides[0];
        oOffsets[0] = batchIdx * oStrides[0];

        for (DimSize_t iChannel = 0; iChannel < iDims[1]; ++iChannel) {
            iOffsets[1] = iChannel * iStrides[1] + iOffsets[0];
            kOffsets[0] = iChannel * kStrides[0];

            for (DimSize_t oChannel = 0; oChannel < oDims[1]; ++oChannel) {
                oOffsets[1] = oChannel * oStrides[1] + oOffsets[0];
                kOffsets[1] = oChannel * kStrides[1] + kOffsets[0];

                for (DimSize_t kX = 0; kX < kDim[0]; ++kX) {
                    kOffsets[2] = kX * kStrides[2] + kOffsets[1];
                    for (DimSize_t kY = 0; kY < kDim[1]; ++kY) {

                        for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                            const DimSize_t iX =
                                oX * stride[0] + kX * dilation[0];

                            oOffsets[2] = oX * oStrides[2] + oOffsets[1];
                            iOffsets[2] = iX * iStrides[2] + iOffsets[1];

                            for (DimSize_t oY = 0; oY < oDims[3]; ++oY) {
                                const DimSize_t iY =
                                    oY * stride[1] + kY * dilation[1];

                                weightsGrad[kOffsets[2] + kY] +=
                                    input[iOffsets[2] + iY] *
                                    oGrad[oOffsets[2] + oY];
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief computes bias backpropagation for conv2D operation
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * @note formula :
 * Bias grad:
 * for i in 0..bias_size:
 *  for n in 0..output_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *   dbi     dbi  Yn
 * with : dYn / dbi = 1
 *
 * Hence the partial derivative of the loss wrt bias is the
 * output loss Hence the bias grad is just the sum of the
 * loss values over the batch
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param[in] oDims output tensor dimensions
 * @param[in] oStrides nb of elements contained per dimension of the
 * output
 * @param[in] oGrad output tensor gradients
 * @param[inout] biasesGrad biases gradients
 */
template <class B, class O>
static void conv2DBackwardBias(const array<DimSize_t, 4> &oDims,
                               const array<DimSize_t, 3> &oStrides,
                               const O *oGrad,
                               B *biasesGrad) {
    // records all index offsets for output tensor
    array<DimSize_t, 3> oOffsets{};
    for (DimSize_t batchIdx = 0; batchIdx < oDims[0]; ++batchIdx) {
        oOffsets[0] = batchIdx * oStrides[0];

        for (DimSize_t oChannel = 0; oChannel < oDims[1]; ++oChannel) {
            oOffsets[1] = oChannel * oStrides[1] + oOffsets[0];

            for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                oOffsets[2] = oX * oStrides[2] + oOffsets[1];

                for (DimSize_t oY = 0; oY < oDims[3]; ++oY) {
                    biasesGrad[oChannel] += oGrad[oOffsets[2] + oY];
                }
            }
        }
    }
}

/**
 * @brief Backward kernel for 2D Convolution on CPU backend.
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 *
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param[in] const stride attribute of conv operator
 * @param[in] const dilation attribute of conv operator
 * @param[in] const kernelDims
 * @param[in] const iDims input data dimensions
 * @param[in] const oDims output data dimmensions
 * @param[in] const input_ input tensor.
 * @param[in] const weights_ kernel tensor.
 * @param[in] const oGrad_ output tensor gradient.
 * @param[inout] iGrad_ input tensor gradient.
 * @param[inout] weightsGrad_  kernel weights tensor gradients
 * @param[inout] biasesGrad_  kernel biases tensor gradients
 */
template <class I, class W, class B, class O>
void ConvImpl2D_cpu_backward_kernel(const array<DimSize_t, 2> &stride,
                                    const array<DimSize_t, 2> &dilation,
                                    const array<DimSize_t, 2> &kernelDims,
                                    const array<DimSize_t, 4> &inputDims,
                                    const array<DimSize_t, 4> &outputDims,
                                    const void *input_,
                                    const void *weights_,
                                    const void *oGrad_,
                                    void *iGrad_,
                                    void *weightsGrad_,
                                    void *biasesGrad_) {

    const I *input = static_cast<const I *>(input_);
    I *iGrad = static_cast<I *>(iGrad_);
    const I *outputGrad = static_cast<const I *>(oGrad_);
    const W *weights = static_cast<const W *>(weights_);
    W *weightsGrad = static_cast<W *>(weightsGrad_);

    //////////////////////////////
    // COMPUTING STRIDES
    //////////////////////////////
    // NOTE: The ...Stride var represent the number of values contained in
    // each dimension they will be used to compute the index offset of
    // values while iterating on each tensor
    // NOTE: They are 1 item shorter than their corresponding tensor as the
    // number of total elements is not used except for gradient initialization

    // {batch_stride, channel_stride, dim0_stride, dim1_stride}
    const array<DimSize_t, 3> inputStrides{
        inputDims[1] * inputDims[2] * inputDims[3],
        inputDims[2] * inputDims[3],
        inputDims[3]};
    const DimSize_t nbEltsInput = inputDims[0] * inputStrides[0];

    // {batch_stride, channel_stride, dim0_stride, dim1_stride}
    const array<DimSize_t, 3> outputStrides{
        outputDims[1] * outputDims[2] * outputDims[3],
        outputDims[2] * outputDims[3],
        outputDims[3]};

    // NOTE: kernel dims = {iChannel, oChannel, kernelDim0, kernelDim1}
    // kernel_strides = {iChannel, oChannel, kernelDim0}
    const array<DimSize_t, 3> kernelStrides{
        inputDims[1] * kernelDims[0] * kernelDims[1],
        kernelDims[0] * kernelDims[1],
        kernelDims[1]};

    const DimSize_t nbEltsKernel = outputDims[1] * kernelStrides[0];

    ////////////////////////////
    // prepping gradient arrays
    std::fill(iGrad, iGrad + nbEltsInput, I(0));
    std::fill(weightsGrad, weightsGrad + nbEltsKernel, W(0));

    conv2DBackwardInput(stride,
                        dilation,
                        kernelDims,
                        kernelStrides,
                        weights,
                        outputDims,
                        outputStrides,
                        outputGrad,
                        inputDims,
                        inputStrides,
                        iGrad);

    conv2DBackwardWeights(inputDims,
                          inputStrides,
                          input,
                          outputDims,
                          outputStrides,
                          outputGrad,
                          kernelDims,
                          kernelStrides,
                          stride,
                          dilation,
                          weightsGrad);

    if (biasesGrad_ != nullptr) {
        B *biasesGrad = static_cast<B *>(biasesGrad_);
        std::fill(biasesGrad, biasesGrad + outputDims[1], B(0));
        conv2DBackwardBias(outputDims, outputStrides, outputGrad, biasesGrad);
    }
}

// Kernels registration to implementation entry point
REGISTRAR(ConvImpl2D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Float32, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           Aidge::ConvImpl2D_cpu_forward_kernel<float, float, float, float>,
           Aidge::ConvImpl2D_cpu_backward_kernel<float, float, float, float>});
REGISTRAR(ConvImpl2D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Float16, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           Aidge::ConvImpl2D_cpu_forward_kernel<half_float::half,
                                                half_float::half,
                                                half_float::half,
                                                half_float::half>,
           Aidge::ConvImpl2D_cpu_backward_kernel<half_float::half,
                                                 half_float::half,
                                                 half_float::half,
                                                 half_float::half>});
REGISTRAR(
    ConvImpl2D_cpu,
    {{DataType::Any, DataFormat::NCHW}, {DataType::Float64, DataFormat::NCHW}},
    {ProdConso::inPlaceModel,
     Aidge::ConvImpl2D_cpu_forward_kernel<double, double, double, double>,
     Aidge::ConvImpl2D_cpu_backward_kernel<double, double, double, double>});
REGISTRAR(ConvImpl2D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Int32, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl2D_cpu_forward_kernel<std::int32_t,
                                         std::int32_t,
                                         std::int32_t,
                                         std::int32_t>,
           ConvImpl2D_cpu_backward_kernel<std::int32_t,
                                          std::int32_t,
                                          std::int32_t,
                                          std::int32_t>});

/**
 * @brief Forward kernel for 3D Convolution on CPU backend.
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param strideDims stride dimensions
 * @param dilationDims dilation dimensions
 * @param kDims kernel dimensions
 * @param iDims input dimensions.
 * @param oDims output dimensions.
 * @param input_ const input Tensor.
 * @param weights_ const weight Tensor.
 * @param biases_ const Biais Tensor.
 * @param output_ Output Tensor.
 */
template <class I, class W, class B, class O>
void ConvImpl3D_cpu_forward_kernel(const array<DimSize_t, 3> &strideDims,
                                   const array<DimSize_t, 3> &dilationDims,
                                   const array<DimSize_t, 3> &kDims,
                                   const array<DimSize_t, 5> &iDims,
                                   const array<DimSize_t, 5> &oDims,
                                   const void *input_,
                                   const void *weights_,
                                   const void *biases_,
                                   void *output_) {

    ////////////////////////////////////////////////////////////////////////
    // TENSOR CASTING
    // FIXME: missing convolution attributes as arguments
    const I *input = static_cast<const I *>(input_);
    const W *weights = static_cast<const W *>(weights_);
    const B *biases = static_cast<const B *>(biases_);
    O *output = static_cast<O *>(output_);

    // const array<DimSize_t, 3> dilatedKernelDims{
    //     dilationDims[0] * kDims[0] + 1,
    //     dilationDims[1] * kDims[1] + 1,
    //     dilationDims[2] * kDims[2] + 1};

    ////////////////////////////////////////////////////////////////////////
    // strides
    // for each array they represent
    // the number of elems contained in a given dimension
    const array<DimSize_t, 4> iStride{
        iDims[1] * iDims[2] * iDims[3] * iDims[4],
        iDims[2] * iDims[3] * iDims[4],
        iDims[3] * iDims[4],
        iDims[4]};
    const array<DimSize_t, 4> oStride{
        oDims[1] * oDims[2] * oDims[3] * oDims[4],
        oDims[2] * oDims[3] * oDims[4],
        oDims[3] * oDims[4],
        oDims[4]};
    const array<DimSize_t, 4> kStride{
        iDims[1] * kDims[0] * kDims[1] * kDims[2],
        kDims[0] * kDims[1] * kDims[2],
        kDims[1] * kDims[2],
        kDims[2]};

    ////////////////////////////////////////////////////////////////////////
    // index offsets
    // NOTE:
    // in/out dims = {batch, in/outChannels,
    // in/outDims[0],in/outDims[1],in/outDims[2]}
    array<DimSize_t, 4> iOffset{0, 0, 0, 0};
    array<DimSize_t, 4> oOffset{0, 0, 0, 0};
    // NOTE:
    // kernel dims = {outChannels, inChannels, kernelDims[0],
    //                kernelDims[1], kernelDims[2]}
    array<DimSize_t, 4> kOffset{0, 0, 0, 0};
    array<DimSize_t, 2> kDilOffset{0, 0};

    ////////////////////////////////////////////////////////////////////////
    // COMPUTATION
    for (DimSize_t batch = 0; batch < iDims[0]; ++batch) {
        oOffset[0] = batch * oStride[0];
        iOffset[0] = batch * iStride[0];
        for (DimSize_t oChannel = 0; oChannel < oDims[1]; ++oChannel) {
            oOffset[1] = oChannel * oStride[1] + oOffset[0];
            kOffset[0] = oChannel * kStride[0];

            // Filling given channel with corresponding bias value
            if (biases != nullptr) {
                B biasVal = biases[oChannel];
                std::fill(output + oOffset[1],
                          output + oOffset[1] + oStride[1],
                          biasVal);
            }

            for (DimSize_t iChannel = 0; iChannel < iDims[1]; ++iChannel) {
                iOffset[1] = iChannel * iStride[1] + iOffset[0];
                kOffset[1] = iChannel * kStride[1] + kOffset[0];

                for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                    iOffset[2] = oX * strideDims[0] * iStride[2] + iOffset[1];
                    oOffset[2] = oX * oStride[2] + oOffset[1];

                    for (DimSize_t oY = 0; oY < oDims[3]; ++oY) {
                        iOffset[3] =
                            oY * strideDims[1] * iStride[3] + iOffset[2];
                        oOffset[3] = oY * oStride[3] + oOffset[2];

                        for (DimSize_t oZ = 0; oZ < oDims[4]; ++oZ) {
                            auto oIdx = oOffset[3] + oZ;
                            auto iIdx = iOffset[3] + oZ * strideDims[2];

                            for (DimSize_t kX = 0; kX < kDims[0]; ++kX) {
                                kOffset[2] = kX * kStride[2] + kOffset[1];
                                kDilOffset[0] =
                                    kX * dilationDims[0] * iStride[2];

                                for (DimSize_t kY = 0; kY < kDims[1]; ++kY) {
                                    kOffset[3] = kY * kStride[3] + kOffset[2];
                                    kDilOffset[1] =
                                        kY * dilationDims[1] * iStride[3] +
                                        kDilOffset[0];

                                    for (DimSize_t kZ = 0; kZ < kDims[2];
                                         ++kZ) {
                                        output[oIdx] +=
                                            weights[kOffset[3] + kZ] *
                                            input[iIdx + kDilOffset[1] +
                                                  kZ * dilationDims[2]];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief perform backpropagation for the input
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * @note formula :
 * for i in 0..input_size:
 *  for n in 0..weight_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *    dXi    dXi  Yn
 * with : dYn / dXi = w_k
 * for each input value
 * for each weight
 * for each output
 * multiply the weight with the associated value
 * @note kernel & stride are passed as single integers as they are just
 * arrays of length 1
 * @note reminder that kernel dimensions are
 * {outChannels, inChannels, {kernelDims}}
 * <=> {oDims[1], iDims[1], kernelDim}
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam O Output data type.
 * @param[in] stride stride parameter of the convolution operator
 * @param[in] dilation dilation parameter of the convolution operator
 * @param[in] kDims dimension of the kernel
 * @param[in] kStrides nb of elements contained per dimension of the kernel
 * @param[in] weights weights values
 * @param[in] oDims dimensions of the output
 * @param[in] oStrides nb of elements contained per dimension of the output
 * @param[in] oGrad output gradient
 * @param[in] iDims input dimensions
 * @param[in] iStrides nb of elements contained per dimension of the input
 * @param[inout] iGrad gradients of the input to update
 */
template <class I, class W, class O>
void conv3DBackwardInput(const array<DimSize_t, 3> &stride,
                         const array<DimSize_t, 3> &dilation,
                         const array<DimSize_t, 3> &kDims,
                         const array<DimSize_t, 4> &kStrides,
                         const W *weights,
                         const array<DimSize_t, 5> &oDims,
                         const array<DimSize_t, 4> &oStrides,
                         const O *oGrad,
                         const array<DimSize_t, 5> &iDims,
                         const array<DimSize_t, 4> &iStrides,
                         I *iGrad) {
    // records index offsets for each dimension that have a stride (== all
    // dimension except the last) for every parsed tensor
    // these serve as checkpoints to avoid recomputing indexes at every
    // iteration
    array<DimSize_t, 4> iOffset{};
    array<DimSize_t, 4> oOffset{};
    array<DimSize_t, 4> kOffset{};
    array<DimSize_t, 2> iDilkernelOffset{}; // input offset for dilated kernel

    for (DimSize_t batch = 0; batch < iDims[0];
         ++batch, iOffset[0] += iStrides[0], oOffset[0] += oStrides[0]) {

        for (DimSize_t oChannel = 0; oChannel < oDims[1]; oChannel++) {
            oOffset[1] = oChannel * oStrides[1] + oOffset[0];
            kOffset[0] = oChannel * kStrides[0];

            for (DimSize_t iChannel = 0; iChannel < iDims[1]; ++iChannel) {
                iOffset[1] = iChannel * iStrides[1] + iOffset[0];
                kOffset[1] = iChannel * kStrides[1] + kOffset[0];

                for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                    oOffset[2] = oX * oStrides[2] + oOffset[1];
                    iOffset[2] = oX * stride[0] * iStrides[2] + iOffset[1];

                    for (DimSize_t oY = 0; oY < oDims[3]; ++oY) {
                        oOffset[3] = oY * oStrides[3] + oOffset[2];
                        iOffset[3] = oY * stride[1] * iStrides[3] + iOffset[2];

                        for (DimSize_t oZ = 0; oZ < oDims[4]; ++oZ) {
                            auto oIdx = oOffset[3] + oZ;
                            auto iIdx = iOffset[3] + oZ * stride[2];

                            for (DimSize_t kX = 0; kX < kDims[0]; ++kX) {
                                kOffset[2] = kX * kStrides[2] + kOffset[1];
                                iDilkernelOffset[0] =
                                    kX * dilation[0] * iStrides[2];

                                for (DimSize_t kY = 0; kY < kDims[1]; ++kY) {
                                    kOffset[3] = kY * kStrides[3] + kOffset[2];
                                    iDilkernelOffset[1] =
                                        kY * dilation[1] * iStrides[3] +
                                        iDilkernelOffset[0];

                                    for (DimSize_t kZ = 0; kZ < kDims[2];
                                         ++kZ) {

                                        iGrad[iIdx + iDilkernelOffset[1] +
                                              kZ * dilation[2]] +=
                                            weights[kOffset[3] + kZ] *
                                            oGrad[oIdx];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief computes weight backpropagation for conv3D operation
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * weight grad
 * for i in 0..weight_size:
 *  for n in 0..output_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *   dwi     dwi  Yn
 * with : dYn / dwi = x_k
 * @tparam I input dtype
 * @tparam W weight dtype
 * @tparam O output dtype
 * @param[in] iDims input data dimensions
 * @param[in] iStrides nb element in each dimension of input tensor
 * @param[in] input input data
 * @param[in] oDims output data dimmensions
 * @param[in] oStrides nb element in each dimension of output tensor
 * @param[in] oGrad gradients of output data
 * @param[in] kDim dimensions of kernel (not taking in count
 * In/OutChannels)
 * @param[in] kStrides nb element in each dimension of kernel tensor
 * (taking in count In/OutChannels)
 * @param[in] stride attribute of the convolution operator
 * @param[in] dilation attribute of the convolution operator
 * @param[inout] weightsGrad gradients of the kernel weights
 */
template <class I, class W, class O>
void conv3DBackwardWeights(const array<DimSize_t, 5> &iDims,
                           const array<DimSize_t, 4> &iStrides,
                           const I *input,
                           const array<DimSize_t, 5> &oDims,
                           const array<DimSize_t, 4> &oStrides,
                           const O *oGrad,
                           const array<DimSize_t, 3> &kDims,
                           const array<DimSize_t, 4> &kStrides,
                           const array<DimSize_t, 3> &stride,
                           const array<DimSize_t, 3> &dilation,
                           W *weightsGrad) {
    // records index offsets for each dimension that have a stride that is
    // not 1 (=> all dimension except the last) for every parsed tensor
    array<DimSize_t, 4> iOffsets{0, 0, 0, 0};
    array<DimSize_t, 4> oOffsets{0, 0, 0, 0};
    array<DimSize_t, 4> kOffsets{0, 0, 0, 0};
    array<DimSize_t, 3> iDilKernelOffsets{0, 0, 0};

    for (DimSize_t batch = 0; batch < iDims[0]; ++batch) {
        iOffsets[0] = batch * iStrides[0];
        oOffsets[0] = batch * oStrides[0];

        for (DimSize_t oChannel = 0; oChannel < oDims[1]; ++oChannel) {
            oOffsets[1] = oChannel * oStrides[1] + oOffsets[0];
            kOffsets[0] = oChannel * kStrides[0];

            for (DimSize_t iChannel = 0; iChannel < iDims[1]; ++iChannel) {
                iOffsets[1] = iChannel * iStrides[1] + iOffsets[0];
                kOffsets[1] = iChannel * kStrides[1] + kOffsets[0];

                for (DimSize_t kX = 0; kX < kDims[0]; ++kX) {
                    kOffsets[2] = kX * kStrides[2] + kOffsets[1];
                    iDilKernelOffsets[0] = kX * dilation[0] * iStrides[2];

                    for (DimSize_t kY = 0; kY < kDims[1]; ++kY) {
                        kOffsets[3] = kY * kStrides[3] + kOffsets[2];
                        iDilKernelOffsets[1] = kY * dilation[1] * iStrides[3] +
                                               iDilKernelOffsets[0];

                        for (DimSize_t kZ = 0; kZ < kDims[2]; ++kZ) {
                            iDilKernelOffsets[2] =
                                kZ * dilation[2] + iDilKernelOffsets[1];

                            for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                                oOffsets[2] = oX * oStrides[2] + oOffsets[1];
                                iOffsets[2] =
                                    oX * stride[0] * iStrides[2] + iOffsets[1];

                                for (DimSize_t oY = 0; oY < oDims[3]; ++oY) {
                                    oOffsets[3] =
                                        oY * oStrides[3] + oOffsets[2];
                                    iOffsets[3] =
                                        oY * stride[1] * iStrides[3] +
                                        iOffsets[2];

                                    for (DimSize_t oZ = 0, iZ = 0;
                                         oZ < oDims[4];
                                         ++oZ) {

                                        weightsGrad[kOffsets[3] + kZ] +=
                                            input[iOffsets[3] + iZ +
                                                  iDilKernelOffsets[2]] *
                                            oGrad[oOffsets[3] + oZ];
                                        iZ += stride[2];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief computes bias backpropagation for conv3D operation
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 * @note formula :
 * Bias grad:
 * for i in 0..bias_size:
 *  for n in 0..output_size:
 *    dL     dYn  dL
 *   ---- = ---- ----
 *   dbi     dbi  Yn
 * with : dYn / dbi = 1
 *
 * Hence the partial derivative of the loss wrt bias is the
 * output loss Hence the bias grad is just the sum of the
 * loss values over the batch
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param[in] oDims output tensor dimensions
 * @param[in] oStrides nb of elements contained per dimension of the
 * output
 * @param[in] oGrad output tensor gradients
 * @param[inout] biasesGrad biases gradients
 */
template <class B, class O>
static void conv3DBackwardBias(const array<DimSize_t, 5> &oDims,
                               const array<DimSize_t, 4> &oStrides,
                               const O *oGrad,
                               B *biasesGrad) {
    // records all index offsets for output tensor
    array<DimSize_t, 4> oOffsets{0, 0, 0, 0};
    for (DimSize_t batchIdx = 0; batchIdx < oDims[0]; ++batchIdx) {
        oOffsets[0] = batchIdx * oStrides[0];

        for (DimSize_t oChannel = 0; oChannel < oDims[1]; ++oChannel) {
            oOffsets[1] = oChannel * oStrides[1] + oOffsets[0];

            for (DimSize_t oX = 0; oX < oDims[2]; ++oX) {
                oOffsets[2] = oX * oStrides[2] + oOffsets[1];

                for (DimSize_t oY = 0; oY < oDims[3]; ++oY) {
                    oOffsets[3] = oY * oStrides[3] + oOffsets[2];
                    for (DimSize_t oZ = 0; oZ < oDims[4]; ++oZ) {
                        biasesGrad[oChannel] += oGrad[oOffsets[3] + oZ];
                    }
                }
            }
        }
    }
}

/**
 * @brief Backward kernel for 3D Convolution on CPU backend.
 * @note INPUT & OUTPUT convention is the same as in the
 * forward function
 *
 * @tparam I Input data type.
 * @tparam W Weight data type.
 * @tparam B Bias data type.
 * @tparam O Output data type.
 * @param[in] const stride attribute of conv operator
 * @param[in] const dilation attribute of conv operator
 * @param[in] const kernelDims
 * @param[in] const iDims input data dimensions
 * @param[in] const oDims output data dimmensions
 * @param[in] const input_ input tensor.
 * @param[in] const weights_ kernel tensor.
 * @param[in] const oGrad_ output tensor gradient.
 * @param[inout] iGrad_ input tensor gradient.
 * @param[inout] weightsGrad_  kernel weights tensor gradients
 * @param[inout] biasesGrad_  kernel biases tensor gradients
 */
template <class I, class W, class B, class O>
void ConvImpl3D_cpu_backward_kernel(const array<DimSize_t, 3> &stride,
                                    const array<DimSize_t, 3> &dilation,
                                    const array<DimSize_t, 3> &kernelDims,
                                    const array<DimSize_t, 5> &inputDims,
                                    const array<DimSize_t, 5> &outputDims,
                                    const void *input_,
                                    const void *weights_,
                                    const void *oGrad_,
                                    void *iGrad_,
                                    void *weightsGrad_,
                                    void *biasesGrad_) {

    const I *input = static_cast<const I *>(input_);
    I *iGrad = static_cast<I *>(iGrad_);
    const I *outputGrad = static_cast<const I *>(oGrad_);
    const W *weights = static_cast<const W *>(weights_);
    W *weightsGrad = static_cast<W *>(weightsGrad_);

    //////////////////////////////
    // COMPUTING STRIDES
    //////////////////////////////
    // NOTE: The ...Stride var represent the number of values contained
    // in each dimension they will be used to compute the index offset
    // of values while iterating on each tensor NOTE: They are 1 item
    // shorter than their corresponding tensor as the number of total
    // elements is not used except for gradient initialization

    // {batch_stride, channel_stride, dim0_stride, dim1_stride}
    const array<DimSize_t, 4> inputStrides{
        inputDims[1] * inputDims[2] * inputDims[3] * inputDims[4],
        inputDims[2] * inputDims[3] * inputDims[4],
        inputDims[3] * inputDims[4],
        inputDims[4]};
    const DimSize_t nbEltsInput = inputDims[0] * inputStrides[0];

    // {batch_stride, channel_stride, dim0_stride, dim1_stride}
    const array<DimSize_t, 4> outputStrides{
        outputDims[1] * outputDims[2] * outputDims[3] * outputDims[4],
        outputDims[2] * outputDims[3] * outputDims[4],
        outputDims[3] * outputDims[4],
        outputDims[4]};

    // NOTE: kernel dims = {iChannel, oChannel, kernelDim0, kernelDim1}
    // kernel_strides = {iChannel, oChannel, kernelDim0}
    const array<DimSize_t, 4> kernelStrides{
        inputDims[1] * kernelDims[0] * kernelDims[1] * kernelDims[2],
        kernelDims[0] * kernelDims[1] * kernelDims[2],
        kernelDims[1] * kernelDims[2],
        kernelDims[2]};

    const DimSize_t nbEltsKernel = outputDims[1] * kernelStrides[0];

    ////////////////////////////
    // prepping gradient arrays
    std::fill(iGrad, iGrad + nbEltsInput, I(0));
    std::fill(weightsGrad, weightsGrad + nbEltsKernel, W(0));

    conv3DBackwardInput(stride,
                        dilation,
                        kernelDims,
                        kernelStrides,
                        weights,
                        outputDims,
                        outputStrides,
                        outputGrad,
                        inputDims,
                        inputStrides,
                        iGrad);

    conv3DBackwardWeights(inputDims,
                          inputStrides,
                          input,
                          outputDims,
                          outputStrides,
                          outputGrad,
                          kernelDims,
                          kernelStrides,
                          stride,
                          dilation,
                          weightsGrad);

    if (biasesGrad_ != nullptr) {
        B *biasesGrad = static_cast<B *>(biasesGrad_);
        std::fill(biasesGrad, biasesGrad + outputDims[1], B(0));
        conv3DBackwardBias(outputDims, outputStrides, outputGrad, biasesGrad);
    }
}

// Kernels registration to implementation entry point
REGISTRAR(ConvImpl3D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Float32, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl3D_cpu_forward_kernel<float, float, float, float>,
           ConvImpl3D_cpu_backward_kernel<float, float, float, float>});
REGISTRAR(ConvImpl3D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Float16, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl3D_cpu_forward_kernel<half_float::half,
                                         half_float::half,
                                         half_float::half,
                                         half_float::half>,
           ConvImpl3D_cpu_backward_kernel<half_float::half,
                                          half_float::half,
                                          half_float::half,
                                          half_float::half>});
REGISTRAR(ConvImpl3D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Float64, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl3D_cpu_forward_kernel<double, double, double, double>,
           ConvImpl3D_cpu_backward_kernel<double, double, double, double>});
REGISTRAR(ConvImpl3D_cpu,
          {{DataType::Any, DataFormat::NCHW},
           {DataType::Int32, DataFormat::NCHW}},
          {ProdConso::inPlaceModel,
           ConvImpl3D_cpu_forward_kernel<std::int32_t,
                                         std::int32_t,
                                         std::int32_t,
                                         std::int32_t>,
           ConvImpl3D_cpu_backward_kernel<std::int32_t,
                                          std::int32_t,
                                          std::int32_t,
                                          std::int32_t>});
} // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_CONVIMPL_KERNELS_H_ */
