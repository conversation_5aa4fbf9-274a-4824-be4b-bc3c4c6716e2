/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_SUBIMPL_H_
#define AIDGE_CPU_OPERATOR_SUBIMPL_H_

#include "aidge/backend/cpu/operator/OperatorImpl.hpp"
#include "aidge/operator/Sub.hpp"
#include "aidge/utils/Registrar.hpp"

#include <vector>

namespace Aidge {
// Operator implementation entry point for the backend
using SubImpl_cpu = OperatorImpl_cpu<Sub_Op,
    void(std::vector<std::size_t>, std::vector<std::size_t>, const std::vector<std::size_t>&, const void*, const void*,void*),  
    void(const std::size_t,
         const std::size_t,
         const std::size_t,
         const std::vector<std::size_t>&,
         const std::vector<std::size_t>&,
         const std::vector<std::size_t>&,
         const void*,
         void*,
         void*)
>;

// Implementation entry point registration to Operator
REGISTRAR(Sub_Op, "cpu", Aidge::SubImpl_cpu::create);
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_SUBIMPL_H_ */
