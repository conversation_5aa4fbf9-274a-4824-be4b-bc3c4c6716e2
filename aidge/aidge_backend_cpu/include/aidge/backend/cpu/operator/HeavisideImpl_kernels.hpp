/********************************************************************************
 * Copyright (c) 2025 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_HEAVISIDEIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_HEAVISIDEIMPL_KERNELS_H_

#include "aidge/utils/Registrar.hpp"

#include <cstddef> // std::size_t
#include <cmath>

#include "aidge/backend/cpu/operator/HeavisideImpl.hpp"
#include "aidge/utils/ErrorHandling.hpp"

namespace Aidge {

template <class I, class O>
void HeavisideImplCpuForwardKernel(std::size_t inputLength,
                                   const void *input_,
                                   void *output_,
                                   const float value) {
    const I *input = static_cast<const I *>(input_);
    O *output = static_cast<O *>(output_);

    for (std::size_t i = 0; i < inputLength; ++i) {
        output[i] = (input[i] > 0) ? 1 : (input[i] == 0 ? value : 0);
    }
}


// Surrogate Gradient
template <class O, class GO, class GI>
void HeavisideImplCpuBackwardKernel(std::size_t inputLength,
                                    const void* output_,
                                    const void* grad_output_,
                                    void* grad_input_) {

    /*
     * Heaviside is approximated by an arctan function for backward :
     * S ~= \frac{1}{\pi}\text{arctan}(\pi U \frac{\alpha}{2})
     * \frac{dS}{dU} = \frac{\alpha}{2} \frac{1}{(1+(\frac{\pi U \alpha}{2})^2)}}
     * */

    const O* output = static_cast<const O*>(output_);
    const GO* grad_output = static_cast<const GO*>(grad_output_);
    GI* grad_input = static_cast<GI*>(grad_input_);

    for (size_t i = 0; i < inputLength; ++i) {
        grad_input[i] += grad_output[i] * static_cast<O>(1.0 / (1.0 + (output[i] * M_PI) * (output[i] * M_PI)));
    }
}

// Kernels registration to implementation entry point
REGISTRAR(HeavisideImplCpu,
          {DataType::Float32},
          {ProdConso::inPlaceModel,
           Aidge::HeavisideImplCpuForwardKernel<float, float>,
           Aidge::HeavisideImplCpuBackwardKernel<float,float,float>});
} // namespace Aidge

#endif // AIDGE_CPU_OPERATOR_HEAVISIDEIMPL_KERNELS_H__H_
