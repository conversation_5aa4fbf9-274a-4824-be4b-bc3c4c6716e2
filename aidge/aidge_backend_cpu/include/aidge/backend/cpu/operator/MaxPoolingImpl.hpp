/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_MaxPOOLINGIMPL_H_
#define AIDGE_CPU_OPERATOR_MaxPOOLINGIMPL_H_

#include <array>
#include <memory>
#include <tuple>
#include <vector>

#include "aidge/backend/cpu/operator/OperatorImpl.hpp"
#include "aidge/operator/MaxPooling.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"
#include "aidge/backend/cpu/data/GetCPUPtr.h"

namespace Aidge {
// Operator implementation entry point for the backend
using MaxPooling2D_Op = MaxPooling_Op<2>;
using MaxPoolingImpl2D_cpu = OperatorImpl_cpu<MaxPooling_Op<2>,
    void(const std::array<DimSize_t, 2>&,
                            const std::array<DimSize_t, 2>&,
                            const std::array<DimSize_t, 2>&,
                            const bool,
                            const std::array<DimSize_t, 4> &,
                            const void *,
                            void *),
    void(const std::array<DimSize_t, 2>&,
                            const std::array<DimSize_t, 2>&,
                            const std::array<DimSize_t, 2>&,
                            const bool,
                            const std::array<DimSize_t, 4> &,
                            const void *,
                            void *)>;

// Implementation entry point registration to Operator
REGISTRAR(MaxPooling2D_Op, "cpu", Aidge::MaxPoolingImpl2D_cpu::create);
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_MaxPOOLINGIMPL_H_ */
