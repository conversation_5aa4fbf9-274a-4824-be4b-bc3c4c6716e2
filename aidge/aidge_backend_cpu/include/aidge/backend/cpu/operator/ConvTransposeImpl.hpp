
/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_CONVTRANSPOSEIMPL_H_
#define AIDGE_CPU_OPERATOR_CONVTRANSPOSEIMPL_H_

#include <array>

#include "aidge/backend/cpu/operator/OperatorImpl.hpp"
#include "aidge/operator/ConvTranspose.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {

using std::array;

// Operator implementation entry point for the backend
using ConvTranspose1D_Op = ConvTranspose_Op<1>;
using ConvTransposeImpl1D_cpu =
    OperatorImpl_cpu<ConvTranspose1D_Op,
                     void(const array<DimSize_t,1> &,
                          const array<DimSize_t,1> &,
                          const array<DimSize_t,1> &,
                          const array<DimSize_t, 3> &,
                          const array<DimSize_t, 3> &,
                          const void *,
                          const void *,
                          const void *,
                          void *)>;

using ConvTranspose2D_Op = ConvTranspose_Op<2>;
using ConvTransposeImpl2D_cpu =
 OperatorImpl_cpu<ConvTranspose2D_Op,
                                        void(const array<DimSize_t, 2> &,
                                             const array<DimSize_t, 2> &,
                                             const array<DimSize_t, 2> &,
                                             const array<DimSize_t, 4> &,
                                             const array<DimSize_t, 4> &,
                                             const void *,
                                             const void *,
                                             const void *,
                                             void *)>;

using ConvTranspose3D_Op = ConvTranspose_Op<3>;
using ConvTransposeImpl3D_cpu =
 OperatorImpl_cpu<ConvTranspose3D_Op,
                                        void(const array<DimSize_t, 3> &,
                                             const array<DimSize_t, 3> &,
                                             const array<DimSize_t, 3> &,
                                             const array<DimSize_t, 5> &,
                                             const array<DimSize_t, 5> &,
                                             const void *,
                                             const void *,
                                             const void *,
                                             void *)>;

// Implementation entry point registration to Operator
REGISTRAR(ConvTranspose1D_Op, "cpu", ConvTransposeImpl1D_cpu::create);
REGISTRAR(ConvTranspose2D_Op, "cpu", ConvTransposeImpl2D_cpu::create);
REGISTRAR(ConvTranspose3D_Op, "cpu", ConvTransposeImpl3D_cpu::create);

} // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_CONVTRANSPOSEIMPL_H_ */
