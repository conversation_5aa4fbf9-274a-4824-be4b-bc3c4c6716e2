/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_AVGPOOLINGIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_AVGPOOLINGIMPL_KERNELS_H_

#include <array>
#include <tuple>
#include <cmath>

#include "aidge/backend/cpu/data/GetCPUPtr.h"
#include "aidge/backend/cpu/operator/AvgPoolingImpl.hpp"
#include "aidge/data/Data.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {

template <typename T>
using Acc_T = typename std::conditional<std::is_floating_point<T>::value, T, double>::type;

template <typename T>
typename std::enable_if<std::is_floating_point<T>::value, T>::type
castFromFloat(T value, RoundingMode /*roundingMode*/) {
  return value;
}

template <typename T>
typename std::enable_if<!std::is_floating_point<T>::value, T>::type
castFromFloat(double value, RoundingMode roundingMode) {
  return static_cast<T>(round(value, roundingMode));
}

/**
 * @brief Forward kernel for 2D AvgPoolingolution on CPU backend.
 * @tparam I Input data type.
 * @tparam O Output data type.
 * @param params tuple of Attributes from the Operator
 * @param dims Array of input dimensions.
 * @param input_ const input Tensor.
 * @param output_ Output Tensor.
 */
template <class I, class O>
void AvgPoolingImpl2D_cpu_forward_kernel(const std::array<DimSize_t, 2>& strideDims,
                                        const std::array<DimSize_t, 2>& kernelDims,
                                        const std::array<DimSize_t, 2>& dilations,
                                        const std::array<DimSize_t, 4> &dims,
                                        bool ceilMode,
                                        RoundingMode roundingMode,
                                        const void *input_,
                                        void *output_) {
    const I *input = static_cast<const I *>(input_);
    O *output = static_cast<O *>(output_);

    // output H size
    const std::size_t oxSize = 
        ceilMode 
        ? static_cast<std::size_t>(std::ceil(static_cast<float>(dims[2] - (kernelDims[0] - 1) * dilations[0] - 1 + strideDims[0]) /
                                            static_cast<float>(strideDims[0])))
        : static_cast<std::size_t>(std::floor(static_cast<float>(dims[2] - (kernelDims[0] - 1) * dilations[0] - 1 + strideDims[0]) /
                                            static_cast<float>(strideDims[0])));
    // output W size
    const std::size_t oySize = 
        ceilMode 
        ? static_cast<std::size_t>(std::ceil(static_cast<float>(dims[3] - (kernelDims[1] - 1) * dilations[1] - 1 + strideDims[1]) /
                                            static_cast<float>(strideDims[1])))
        : static_cast<std::size_t>(std::floor(static_cast<float>(dims[3] - (kernelDims[1] - 1) * dilations[1] - 1 + strideDims[1]) /
                                            static_cast<float>(strideDims[1])));

    using signedsize = std::make_signed<std::size_t>::type;

#ifdef _OPENMP
    #pragma omp parallel for collapse(2) if (dims[0] * dims[1] >= 16)
#endif
    for (int batch = 0; batch < static_cast<int>(dims[0]); ++batch) {
        for (int ch = 0; ch < static_cast<int>(dims[1]); ++ch) {
            const std::size_t oIndex = (ch + batch * dims[1]) * oxSize * oySize;
            const std::size_t iIndex = (ch + batch * dims[1]) * dims[2] * dims[3];

            for (std::size_t ox = 0; ox < oxSize; ++ox) {
                const signedsize difx = static_cast<signedsize>(-ox * strideDims[0]);
                const std::size_t sxMin = static_cast<std::size_t>(std::max(difx, signedsize(0)));
                const std::size_t sxMax = (static_cast<signedsize>(dims[2]) + difx) < 0 ? 0 : ((dims[2] + difx) > kernelDims[0] ? kernelDims[0] : dims[2] + difx);

                for (std::size_t oy = 0; oy < oySize; ++oy) {
                    const signedsize dify = static_cast<signedsize>(-oy * strideDims[1]);
                    const std::size_t syMin = static_cast<std::size_t>(std::max(dify, signedsize(0)));
                    const std::size_t syMax = (static_cast<signedsize>(dims[3]) + dify) < 0 ? 0 : ((dims[3] + dify) > kernelDims[1] ? kernelDims[1] : dims[3] + dify);

                    const std::size_t oIndexFull = oIndex + ox * oySize + oy;
                    const std::size_t ix = ox * strideDims[0];
                    const std::size_t iy = oy * strideDims[1];

                    Acc_T<I> sum = static_cast<Acc_T<I>>(0);
                    std::size_t count = 0;

                    for (unsigned int sy = syMin; sy < syMax; ++sy) {
                        for (unsigned int sx = sxMin; sx < sxMax; ++sx) {
                            // Apply dilation factor
                            const std::size_t dilated_sx = sx * dilations[0];
                            const std::size_t dilated_sy = sy * dilations[1];

                            // Ensure within bounds
                            if ((ix + dilated_sx) < dims[2] && (iy + dilated_sy) < dims[3]) {
                                sum += static_cast<Acc_T<I>>(input[iIndex + (ix + dilated_sx) * dims[3] + (iy + dilated_sy)]);
                                ++count;
                            }
                        }
                    }

                    output[oIndexFull] = count > 0 ? castFromFloat<O>(sum / count, roundingMode) : 0;
                }
            }
        }
    }
}

// Kernels registration to implementation entry point
REGISTRAR(AvgPoolingImpl2D_cpu,
    {{DataType::Float32, DataFormat::NCHW}, {DataType::Float32, DataFormat::NCHW}},
    {ProdConso::inPlaceModel, Aidge::AvgPoolingImpl2D_cpu_forward_kernel<float, float>, nullptr});
REGISTRAR(AvgPoolingImpl2D_cpu,
    {{DataType::Int32, DataFormat::NCHW}, {DataType::Int32, DataFormat::NCHW}},
    {ProdConso::inPlaceModel, Aidge::AvgPoolingImpl2D_cpu_forward_kernel<std::int32_t, std::int32_t>, nullptr});
REGISTRAR(AvgPoolingImpl2D_cpu,
    {{DataType::Float64, DataFormat::NCHW}, {DataType::Float64, DataFormat::NCHW}},
    {ProdConso::inPlaceModel, Aidge::AvgPoolingImpl2D_cpu_forward_kernel<double, double>, nullptr});
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_AVGPOOLINGIMPL_KERNELS_H_ */
