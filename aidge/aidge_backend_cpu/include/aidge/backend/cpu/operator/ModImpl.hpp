/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_MODIMPL_H_
#define AIDGE_CPU_OPERATOR_MODIMPL_H_

#include <memory>
#include <tuple>
#include <vector>

#include "aidge/backend/cpu/operator/OperatorImpl.hpp"
#include "aidge/operator/Mod.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {
// Operator implementation entry point for the backend
using ModImpl_cpu = OperatorImpl_cpu<Mod_Op,
    void(bool, const std::size_t, const std::size_t, const std::size_t, const void*, const void*,void*)>;

// Implementation entry point registration to Operator
REGISTRAR(Mod_Op, "cpu", Aidge::ModImpl_cpu::create);
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_MODIMPL_H_ */
