/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_MaxPOOLINGIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_MaxPOOLINGIMPL_KERNELS_H_

#include <array>
#include <cmath>
#include <cstdint>
#include <tuple>


#include "aidge/backend/cpu/operator/MaxPoolingImpl.hpp"
#include "aidge/backend/cpu/data/GetCPUPtr.h"
#include "aidge/data/Data.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {
/**
 * @brief Forward kernel for 2D MaxPoolingolution on CPU backend.
 * @tparam I Input data type.
 * @tparam O Output data type.
 * @param attrs tuple of Attributes from the Operator
 * @param dims Array of input dimensions.
 * @param input_ const input Tensor.
 * @param output_ Output Tensor.
 */
template <class I, class O>
void MaxPoolingImpl2D_cpu_forward_kernel(
  const std::array<DimSize_t, 2>& strideDims,
  const std::array<DimSize_t, 2>& kernelDims,
  const std::array<DimSize_t, 2>& dilations,
  const bool ceilMode,
  const std::array<DimSize_t, 4> &dims,
  const void *input_,
  void *output_)
{
  const I *input = static_cast<const I *>(input_);
  O *output = static_cast<O *>(output_);

  // output H size
  auto hOut = static_cast<float>(
    dims[2] - (kernelDims[0] - 1) * dilations[0] - 1 + strideDims[0]
  ) / static_cast<float>(strideDims[0]);
  const std::size_t outXSize = ceilMode
    ? static_cast<std::size_t>(std::ceil(hOut))
    : static_cast<std::size_t>(std::floor(hOut));

  // output W size
  auto wOut = static_cast<float>( 
      dims[3] - ( kernelDims[1] - 1) * dilations[1] - 1 + strideDims[1]
    ) / static_cast<float>(strideDims[1]);

  const std::size_t outYSize = ceilMode
    ? static_cast<std::size_t>(std::ceil(wOut))
    : static_cast<std::size_t>(std::floor(wOut));

  using signedsize = std::make_signed<std::size_t>::type;

#ifdef _OPENMP
    #pragma omp parallel for collapse(2) if (dims[0] * dims[1] >= 16)
#endif
  for (int batch = 0; batch < static_cast<int>(dims[0]); ++batch){
    for (int channel = 0; channel < static_cast<int>(dims[1]); ++channel){
      auto batchChannelIndex = (channel + batch * dims[1]);
      const std::size_t outputBaseIndex = batchChannelIndex * outXSize * outYSize;
      const std::size_t inputBaseIndex = batchChannelIndex * dims[2] * dims[3];
      for (std::size_t outX = 0; outX < outXSize; ++outX) {
        const signedsize negStrideX = static_cast<signedsize>(
		-outX * strideDims[0]
	);
        const std::size_t kernelXMin = static_cast<std::size_t>(
          std::max(negStrideX, signedsize(0))
        );
        /* Compute kernelXMax */
        std::size_t kernelXMax = dims[2] + negStrideX;
        if ((static_cast<signedsize>(dims[2]) + negStrideX) < 0){
          kernelXMax = 0;
        }
        else if (kernelXMax > kernelDims[0]){
          kernelXMax = kernelDims[0];
        }
        for (std::size_t outY = 0; outY < outYSize; ++outY) {
          const signedsize negStrideY = static_cast<signedsize>(-outY * strideDims[1]);
          const std::size_t kernelYMin = static_cast<std::size_t>(
            std::max(negStrideY, signedsize(0))
          );
          /* Compute kernelYMax */
          std::size_t kernelYMax = dims[3] + negStrideY;
          const std::size_t outputIndex = outputBaseIndex + outX * outYSize + outY;
          const std::size_t strideXoffset = outX * strideDims[0];
          const std::size_t strideYoffset = outY * strideDims[1];
          I poolValue(0.0);
          bool valid = false;
          if (static_cast<signedsize>(dims[3]) + negStrideY < 0){
            kernelYMax = 0;
          }
          else if(kernelYMax > kernelDims[1]){
            kernelYMax = kernelDims[1];
          }
          for (unsigned int kY = kernelYMin; kY < kernelYMax ; ++kY){
            for (unsigned int kX = kernelXMin; kX < kernelXMax; ++kX){
              // Apply dilation factor to kernel indices
              const std::size_t dilatedkernelX = kX * dilations[0];
              const std::size_t dilatedkernelY = kY * dilations[1];
              // Ensure indices are within bounds
              auto inputXPostDilation = strideXoffset + dilatedkernelX;
              auto inputYPostDilation = strideYoffset + dilatedkernelY;
              if (inputXPostDilation < dims[2] && inputYPostDilation < dims[3]){
                const I inputValue = input[
                inputBaseIndex + inputXPostDilation * dims[3] 
                + inputYPostDilation
                ];
                if (!valid || inputValue > poolValue) {
                  poolValue = inputValue;
                  valid = true;
                }
              }
            }
          }
          output[outputIndex] = poolValue;
        }
      }
    }
  }
} 


template <class I, class O>
void MaxPoolingImpl2D_cpu_backward_kernel(
  const std::array<DimSize_t, 2>& strideDims,
  const std::array<DimSize_t, 2>& kernelDims,
  const std::array<DimSize_t, 2>& dilations,
  const bool ceilMode,
  const std::array<DimSize_t, 4> &dims,
  const void *input_,
  void *grad_
)
{
  const I *input = static_cast<const I *>(input_);
  I *grad = static_cast<I *>(grad_);

  // output H size
  auto hOut = static_cast<float>(
    dims[2] - (kernelDims[0] - 1) * dilations[0] - 1 + strideDims[0]
  ) / static_cast<float>(strideDims[0]);
  const std::size_t outXSize = ceilMode
    ? static_cast<std::size_t>(std::ceil(hOut))
    : static_cast<std::size_t>(std::floor(hOut));

  // output W size
  auto wOut = static_cast<float>( 
      dims[3] - ( kernelDims[1] - 1) * dilations[1] - 1 + strideDims[1]
    ) / static_cast<float>(strideDims[1]);

  const std::size_t outYSize = ceilMode
    ? static_cast<std::size_t>(std::ceil(wOut))
    : static_cast<std::size_t>(std::floor(wOut));

  using signedsize = std::make_signed<std::size_t>::type;

  for (std::size_t batch = 0; batch < dims[0]; ++batch){
    for (std::size_t channel = 0; channel < dims[1]; ++channel){
      auto batchChannelIndex = (channel + batch * dims[1]);
      const std::size_t inputBaseIndex = batchChannelIndex * dims[2] * dims[3];
      for (std::size_t outX = 0; outX < outXSize; ++outX) {
        const signedsize negStrideX = static_cast<signedsize>(
          -outX * strideDims[0]
        );
        const std::size_t kernelXMin = static_cast<std::size_t>(
          std::max(negStrideX, signedsize(0))
        );
        /* Compute kernelXMax */
        std::size_t kernelXMax = dims[2] + negStrideX;
        if ((static_cast<signedsize>(dims[2]) + negStrideX) < 0){
          kernelXMax = 0;
        }
        else if (kernelXMax > kernelDims[0]){
          kernelXMax = kernelDims[0];
        }
        for (std::size_t outY = 0; outY < outYSize; ++outY) {
          const signedsize negStrideY = static_cast<signedsize>(-outY * strideDims[1]);
          const std::size_t kernelYMin = static_cast<std::size_t>(
            std::max(negStrideY, signedsize(0))
          );
          /* Compute kernelYMax */
          std::size_t kernelYMax = dims[3] + negStrideY;
          const std::size_t strideXoffset = outX * strideDims[0];
          const std::size_t strideYoffset = outY * strideDims[1];
          I poolValue(0.0);
          bool valid = false;
          if (static_cast<signedsize>(dims[3]) + negStrideY < 0){
            kernelYMax = 0;
          }
          else if(kernelYMax > kernelDims[1]){
            kernelYMax = kernelDims[1];
          }
	  std::size_t saveIndex = 0;
          for (unsigned int kY = kernelYMin; kY < kernelYMax ; ++kY){
            for (unsigned int kX = kernelXMin; kX < kernelXMax; ++kX){
              // Apply dilation factor to kernel indices
              const std::size_t dilatedkernelX = kX * dilations[0];
              const std::size_t dilatedkernelY = kY * dilations[1];
              // Ensure indices are within bounds
              auto inputXPostDilation = strideXoffset + dilatedkernelX;
              auto inputYPostDilation = strideYoffset + dilatedkernelY;
              if (inputXPostDilation < dims[2] && inputYPostDilation < dims[3]){
		std::size_t inputIndex = 
			inputBaseIndex + inputXPostDilation * dims[3] 
			+ inputYPostDilation;
                const I inputValue = input[inputIndex];
                if (!valid || inputValue > poolValue) {
                  poolValue = inputValue;
		  saveIndex = inputIndex;
                  valid = true;
                }
              }
            }
          }
	  if (valid){
		grad[saveIndex]++;
	  }
        }
      }
    }
  }
}




// Kernels registration to implementation entry point
REGISTRAR(MaxPoolingImpl2D_cpu,
    {DataType::Float32},
    {
          ProdConso::inPlaceModel,
          Aidge::MaxPoolingImpl2D_cpu_forward_kernel<float, float>,
          Aidge::MaxPoolingImpl2D_cpu_backward_kernel<float, float>,
    }
);
REGISTRAR(MaxPoolingImpl2D_cpu,
    {DataType::Float64},
    {
          ProdConso::inPlaceModel,
          Aidge::MaxPoolingImpl2D_cpu_forward_kernel<double, double>,
          Aidge::MaxPoolingImpl2D_cpu_backward_kernel<double, double>,
    }
);
REGISTRAR(MaxPoolingImpl2D_cpu,
    {DataType::Int32},
    {
          ProdConso::inPlaceModel,
          Aidge::MaxPoolingImpl2D_cpu_forward_kernel<int32_t, int32_t>,
          Aidge::MaxPoolingImpl2D_cpu_backward_kernel<int32_t, int32_t>,
    }
);
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_MaxPOOLINGIMPL_KERNELS_H_ */
