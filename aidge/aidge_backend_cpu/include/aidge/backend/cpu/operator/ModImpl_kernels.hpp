/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_MODIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_MODIMPL_KERNELS_H_

#include <numeric>     // std::accumulate
#include <cstddef>     // std::size_t
#include <cstdint>     // std::int32_t, std::int64_t
#include <functional>  // std::multiplies

#include "aidge/utils/Registrar.hpp"

#include "aidge/backend/cpu/data/Broadcasting.hpp"
#include "aidge/backend/cpu/operator/ModImpl.hpp"

namespace Aidge {

template <typename T,  
    typename std::enable_if<std::is_integral<T>::value>::type* = nullptr>
static inline T modulus(T a, T b) {
    return a % b;
}

template <typename T,  
    typename std::enable_if<!std::is_integral<T>::value>::type* = nullptr>
static inline T modulus(T /*a*/, T /*b*/) {
    AIDGE_THROW_OR_ABORT(std::runtime_error, "Mod Operator with fmod attribute set to false only supports integer types.");
}

template <class I1, class I2, class O>
constexpr void ModImpl_cpu_forward_kernel(bool fmod,
                                const std::size_t input1size_,
                                const std::size_t input2size_,
                                const std::size_t output1size_,
                                const void* input1_,
                                const void* input2_,
                                void* output_) {

    const I1* input_1 = static_cast<const I1*>(input1_);
    const I2* input_2 = static_cast<const I2*>(input2_);
    O* output = static_cast<O*>(output_);

// suppose values are contiguous in memory
    for (std::size_t i = 0; i < output1size_; ++i) {
        const std::size_t in1_id = (input1size_ != 1) ? i : 0;
        const std::size_t in2_id = (input2size_ != 1) ? i : 0;
        if (fmod) {
            output[i] = static_cast<O>(std::fmod(input_1[in1_id], input_2[in2_id]));
        }
        else {
            output[i] = static_cast<O>(modulus(input_1[in1_id], input_2[in2_id]));
        }
    }
}

// Kernels registration to implementation entry point
REGISTRAR(ModImpl_cpu,
    {DataType::Float32},
    {ProdConso::inPlaceModel, Aidge::ModImpl_cpu_forward_kernel<float, float, float>, nullptr});
REGISTRAR(ModImpl_cpu,
    {DataType::Float64},
    {ProdConso::inPlaceModel, Aidge::ModImpl_cpu_forward_kernel<double, double, double>, nullptr});
REGISTRAR(ModImpl_cpu,
    {DataType::Int32},
    {ProdConso::inPlaceModel, Aidge::ModImpl_cpu_forward_kernel<std::int32_t, std::int32_t, std::int32_t>, nullptr});
REGISTRAR(ModImpl_cpu,
    {DataType::UInt64},
    {ProdConso::inPlaceModel, Aidge::ModImpl_cpu_forward_kernel<std::uint64_t, std::uint64_t, std::uint64_t>, nullptr});
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_MODIMPL_KERNELS_H_ */
