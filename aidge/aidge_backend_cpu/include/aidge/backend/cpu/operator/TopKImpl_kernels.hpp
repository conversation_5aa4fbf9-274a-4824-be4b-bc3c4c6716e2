/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_TOPKIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_TOPKIMPL_KERNELS_H_

#include <algorithm>   // std::for_each
#include <cstddef>     // std::size_t
#include <cstdint>     // std::int32_t
#include <functional>  //std::multiplies
#include <numeric>     //std::accumulate
#include <vector>

#include "aidge/backend/cpu/operator/TopKImpl.hpp"
#include "aidge/data/Data.hpp"
#include "aidge/operator/TopK.hpp"
#include "aidge/utils/Registrar.hpp"

namespace Aidge {

template <class I, class O>
void TopKImpl_cpu_forward_kernel(int64_t axis,
                                 bool largest,
                                 bool /*sorted*/,
                                 IOIndex_t k,
                                 const std::vector<DimSize_t>& inputDims,
                                 const void* input_,
                                 void* output_,
                                 void* indices_)
{
    const I* input = static_cast<const I*>(input_);
    O* output = static_cast<O*>(output_);
    int64_t* indices = static_cast<int64_t*>(indices_);

    const std::size_t nb_dims = inputDims.size();
    const std::size_t stride_pre = std::accumulate(inputDims.cbegin(), inputDims.cbegin() + axis, 1, std::multiplies<std::size_t>());
    const std::size_t stride_post = std::accumulate(inputDims.crbegin(), inputDims.crbegin() + nb_dims -1 - axis, 1, std::multiplies<std::size_t>());

    const std::size_t dim_i = inputDims[axis];
    std::vector<std::pair<I, int64_t>> buffer(dim_i);

#ifdef _OPENMP
    #pragma omp parallel for collapse(2) if (stride_pre * stride_post >= 16)
#endif
    for (int pre = 0; pre < static_cast<int>(stride_pre); ++pre) {
        for (int post = 0; post < static_cast<int>(stride_post); ++post) {
            const std::size_t idx_i = pre * dim_i * stride_post + post;
            const std::size_t idx_o = pre * k * stride_post + post;

            for (size_t i = 0; i < dim_i; ++i) {
                const auto idx = idx_i + i * stride_post;
                buffer[i] = std::make_pair(input[idx], i);
            }

            if (largest) {
                std::partial_sort(buffer.begin(), buffer.begin() + k, buffer.end(),
                    [](const auto& lhs, const auto& rhs) { return lhs.first > rhs.first; });
            }
            else {
                std::partial_sort(buffer.begin(), buffer.begin() + k, buffer.end(),
                    [](const auto& lhs, const auto& rhs) { return lhs.first < rhs.first; });
            }

            for (size_t i = 0; i < k; ++i) {
                output[idx_o + i] = buffer[i].first;
                indices[idx_o + i] = buffer[i].second;
            }
        }
    }
}

// Kernels registration to implementation entry point
REGISTRAR(TopKImpl_cpu,
    {
        {{DataType::Float32}, {DataType::Any}},
        {{DataType::Float32}, {DataType::Int64}}
    },
    {ProdConso::inPlaceModel, Aidge::TopKImpl_cpu_forward_kernel<float, float>, nullptr});
REGISTRAR(TopKImpl_cpu,
    {
        {{DataType::Float64}, {DataType::Any}},
        {{DataType::Float64}, {DataType::Int64}}
    },
    {ProdConso::inPlaceModel, Aidge::TopKImpl_cpu_forward_kernel<double, double>, nullptr});
REGISTRAR(TopKImpl_cpu,
    {
        {{DataType::Int32}, {DataType::Any}},
        {{DataType::Int32}, {DataType::Int64}}
    },
    {ProdConso::inPlaceModel, Aidge::TopKImpl_cpu_forward_kernel<int32_t, int32_t>, nullptr});
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_TOPKIMPL_KERNELS_H_ */
