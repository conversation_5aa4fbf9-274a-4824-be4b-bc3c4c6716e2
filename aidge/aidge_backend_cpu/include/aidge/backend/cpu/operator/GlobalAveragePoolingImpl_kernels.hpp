/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_GLOBALAVERAGEPOOLINGIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_GLOBALAVERAGEPOOLINGIMPL_KERNELS_H_

#include <cstddef>     // std::size_t
#include <vector>

#include "aidge/backend/cpu/operator/GlobalAveragePoolingImpl.hpp"
#include "aidge/data/Tensor.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {

template <typename T>
typename std::enable_if_t<std::is_floating_point<T>::value, T>
static stableMean(const T* vec, std::size_t size) {
    T mean{0};
    for (std::size_t i = 0; i < size; ++i) {
        mean = std::fma(vec[i] - mean, static_cast<T>(1) / static_cast<T>(i + 1), mean);
    }
    return mean;
}

// Specialization for integers: perform the mean computation in float
template <typename T>
typename std::enable_if_t<!std::is_floating_point<T>::value, double>
static stableMean(const T* vec, std::size_t size) {
    double mean{0};
    for (std::size_t i = 0; i < size; ++i) {
        mean = std::fma<double>(static_cast<double>(vec[i]) - mean, 1.0 / static_cast<double>(i + 1), mean);
    }
    return mean;
}

template <typename T>
typename std::enable_if_t<std::is_floating_point<T>::value, T>
static castFromFloat(T value, RoundingMode /*roundingMode*/) {
    return value;
}

template <typename T>
typename std::enable_if_t<!std::is_floating_point<T>::value, T>
static castFromFloat(double value, RoundingMode roundingMode) {
    return static_cast<T>(round(value, roundingMode));
}

template <DataType DT_I, DataType DT_O = DT_I>
void GlobalAveragePoolingImpl_cpu_forward_kernel(RoundingMode roundingMode, const std::shared_ptr<Tensor>& inputTensor, void *output_) {

    // computation
    using I = cpptype_t<DT_I>;
    using O = cpptype_t<DT_O>;
    const I *input = static_cast<const I *>(inputTensor->getImpl()->rawPtr());
    O *output = static_cast<O *>(output_);

    const auto& dims = inputTensor->dims();
    DimSize_t nb_elems = std::accumulate(dims.begin(), dims.end(), std::size_t(1),
                                         std::multiplies<std::size_t>());
  
    const DimSize_t in_batch_nb_elems{nb_elems / dims[0]};
    const DimSize_t in_channel_nb_elems{in_batch_nb_elems / dims[1]};
    const DimSize_t out_batch_nb_elems{dims[1]};

    // parse channel by channel and fill each output with the average of the
    // values in the channel
#ifdef _OPENMP
    #pragma omp parallel for collapse(2) if (dims[0] * dims[1] >= 16)
#endif
    for (int batch = 0; batch < static_cast<int>(dims[0]); ++batch) {
        for (int channel = 0; channel < static_cast<int>(dims[1]); ++channel) {
            const I *filter_start = std::next(
                input, (batch * in_batch_nb_elems) + (channel * in_channel_nb_elems));
            output[batch * out_batch_nb_elems + channel] = castFromFloat<O>(stableMean<I>(filter_start, in_channel_nb_elems), roundingMode);
        }
    }
}

// Kernels registration to implementation entry point
REGISTRAR(GlobalAveragePoolingImpl_cpu,
    {DataType::Float32},
    {ProdConso::defaultModel, Aidge::GlobalAveragePoolingImpl_cpu_forward_kernel<DataType::Float32>, nullptr});
REGISTRAR(GlobalAveragePoolingImpl_cpu,
    {DataType::Float64},
    {ProdConso::defaultModel, Aidge::GlobalAveragePoolingImpl_cpu_forward_kernel<DataType::Float64>, nullptr});
REGISTRAR(GlobalAveragePoolingImpl_cpu,
    {DataType::Int32},
    {ProdConso::defaultModel, Aidge::GlobalAveragePoolingImpl_cpu_forward_kernel<DataType::Int32>, nullptr});
} // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_GLOBALAVERAGEPOOLINGIMPL_KERNELS_H_ */
