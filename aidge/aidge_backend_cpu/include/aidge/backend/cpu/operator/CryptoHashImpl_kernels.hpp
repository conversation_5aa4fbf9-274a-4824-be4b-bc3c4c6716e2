/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_CRYPTOHASHIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_CRYPTOHASHIMPL_KERNELS_H_

#include "aidge/utils/Registrar.hpp"

#include "aidge/backend/cpu/operator/CryptoHashImpl.hpp"

#ifdef WITH_OPENSSL
namespace Aidge {
template <class I, class O>
void CryptoHashImpl_cpu_forward_kernel(std::size_t inputLength,
                                     const void* input_,
                                     void* output_) {

    const I* input = static_cast<const I*>(input_);
    O* output = static_cast<O*>(output_);

    // output must be at least SHA256_DIGEST_LENGTH bytes length
    SHA256(reinterpret_cast<const uint8_t*>(input), inputLength * sizeof(I), reinterpret_cast<uint8_t*>(output));
}

// Kernels registration to implementation entry point
REGISTRAR(CryptoHashImpl_cpu,
    {{DataType::UInt8, DataFormat::Any}, {DataType::UInt8}},
    {ProdConso::inPlaceModel, Aidge::CryptoHashImpl_cpu_forward_kernel<uint8_t, uint8_t>, nullptr});
REGISTRAR(CryptoHashImpl_cpu,
    {{DataType::UInt8, DataFormat::Any}, {DataType::UInt64}},
    {ProdConso::inPlaceModel, Aidge::CryptoHashImpl_cpu_forward_kernel<uint8_t, uint64_t>, nullptr});
REGISTRAR(CryptoHashImpl_cpu,
    {{DataType::Float32, DataFormat::Any}, {DataType::UInt8}},
    {ProdConso::inPlaceModel, Aidge::CryptoHashImpl_cpu_forward_kernel<float, uint8_t>, nullptr});
REGISTRAR(CryptoHashImpl_cpu,
    {{DataType::Float32, DataFormat::Any}, {DataType::UInt64}},
    {ProdConso::inPlaceModel, Aidge::CryptoHashImpl_cpu_forward_kernel<float, uint64_t>, nullptr});
REGISTRAR(CryptoHashImpl_cpu,
    {{DataType::Float64, DataFormat::Any}, {DataType::UInt8}},
    {ProdConso::inPlaceModel, Aidge::CryptoHashImpl_cpu_forward_kernel<double, uint8_t>, nullptr});
}  // namespace Aidge
#endif

#endif /* AIDGE_CPU_OPERATOR_CRYPTOHASHIMPL_KERNELS_H_ */
