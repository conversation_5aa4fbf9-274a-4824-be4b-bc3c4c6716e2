/********************************************************************************
 * Copyright (c) 2025 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_DROPOUTIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_DROPOUTIMPL_KERNELS_H_

#include <cstddef>   // std::size_t
#include <memory>
#include <random>

#include "aidge/backend/cpu/operator/DropoutImpl.hpp"
#include "aidge/data/DataType.hpp"
#include "aidge/utils/Registrar.hpp"


namespace Aidge {

template <DataType DT_I, DataType DT_O = DT_I>
void DropoutImpl_cpu_forward_kernel(float probability,
                                    std::size_t nb_elements,
                                    unsigned int seed,
                                    const void* input_,
                                    void* output_)
{
    using I = cpptype_t<DT_I>;
    using O = cpptype_t<DT_O>;
    const I *input = static_cast<const I *>(input_);
    O *output = static_cast<O *>(output_);

    // const unsigned int seed = static_cast<unsigned int>(std::random_device{}());
    std::mt19937 rng(seed);
    std::bernoulli_distribution bernoulli_dist(1.0f - probability); //bernoulli keep_prob

    const I scale = I(1.0) / static_cast<I>(1.0f - probability);

    for (std::size_t i = 0; i < nb_elements; ++i)
    {
        output[i] = bernoulli_dist(rng) ? static_cast<O>(input[i] * scale)  : static_cast<O>(0.0);
    }

}

REGISTRAR(DropoutImpl_cpu,
          {DataType::Float32},
          {ProdConso::defaultModel, DropoutImpl_cpu_forward_kernel<DataType::Float32>, nullptr});

REGISTRAR(DropoutImpl_cpu,
          {DataType::Float64},
          {ProdConso::defaultModel, DropoutImpl_cpu_forward_kernel<DataType::Float64>, nullptr});

} // namespace aidge

#endif  // AIDGE_CPU_OPERATOR_DROPOUTIMPL_KERNELS_H_
