/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_DIVIMPL_KERNELS_H_
#define AIDGE_CPU_OPERATOR_DIVIMPL_KERNELS_H_

#include <numeric>     // std::accumulate
#include <cstddef>     // std::size_t
#include <cstdint>     // std::int32_t, std::int64_t
#include <functional>  // std::multiplies

#include "aidge/backend/cpu/operator/MulImpl_kernels.hpp"
#include "aidge/utils/Registrar.hpp"

#include "aidge/backend/cpu/data/Broadcasting.hpp"
#include "aidge/backend/cpu/operator/DivImpl.hpp"

namespace Aidge {
// template <class I1, class I2, class O>
// void DivImpl_cpu_forward_kernel(const std::vector<std::size_t>& input1Dims,
//                                 const std::vector<std::size_t>& input2Dims,
//                                 const std::vector<std::size_t>& outputDims,
//                                 const void* input1_,
//                                 const void* input2_,
//                                 void* output_) {

//     const I1* input_1 = static_cast<const I1*>(input1_);
//     const I2* input_2 = static_cast<const I2*>(input2_);
//     O* output = static_cast<O*>(output_);

//     const std::size_t totalElements = std::accumulate(outputDims.cbegin(), outputDims.cend(), std::size_t(1), std::multiplies<std::size_t>());

// 	for (std::size_t oIndex = 0; oIndex < totalElements; ++oIndex)
// 	{
// 		std::vector<std::size_t> indexes = getMultiDimIndices(outputDims, oIndex);

// 		std::size_t idx1 = getFlattenedIndex(input1Dims, indexes);
// 		std::size_t idx2 = getFlattenedIndex(input2Dims, indexes);

//         // TODO assert if input_2 is bad?
//         output[oIndex] = input_1[idx1] / input_2[idx2];
//     }
// }

template <class I1, class I2, class O>
constexpr void DivImpl_cpu_forward_kernel(const std::size_t input1size_,
                                const std::size_t input2size_,
                                const std::size_t output1size_,
                                const void* input1_,
                                const void* input2_,
                                void* output_) {

    const I1* input_1 = static_cast<const I1*>(input1_);
    const I2* input_2 = static_cast<const I2*>(input2_);
    O* output = static_cast<O*>(output_);

// suppose values are contiguous in memory
    for (std::size_t i = 0; i < output1size_; ++i) {
        const std::size_t in1_id = (input1size_ != 1) ? i : 0;
        const std::size_t in2_id = (input2size_ != 1) ? i : 0;
        output[i] = static_cast<O>(input_1[in1_id] / input_2[in2_id]);
    }
}


template <class I1, class I2, class O>
void DivImpl_cpu_backward_kernel(const std::size_t input0Length,
                               const std::size_t input1Length,
                               const std::size_t gradOutputLength,
                               const std::vector<std::size_t>& dims0,
                               const std::vector<std::size_t>& dims1,
                               const std::vector<std::size_t>& outputDims,
                               const void* input0_,
                               const void* input1_,
                               const void* grad_output_,
                               void* gradientInput0_,
                               void* gradientInput1_)
{
    const I1* input0 = static_cast<const I1*>(input0_);  // a
    const I2* input1 = static_cast<const I2*>(input1_);  // b
    const O* grad_output = static_cast<const O*>(grad_output_);
    auto* grad_input_0 = static_cast<I1*>(gradientInput0_);  // gradient w.r.t. a
    auto* grad_input_1 = static_cast<I2*>(gradientInput1_);  // gradient w.r.t. b

    std::fill_n(grad_input_0, input0Length, static_cast<I1>(0));
    std::fill_n(grad_input_1, input1Length, static_cast<I2>(0));

    // Broadcast dims0 and dims1 to match the shape of outputDims
    auto broadcastedDims0 = getBroadcastedDims(outputDims, dims0);
    auto broadcastedDims1 = getBroadcastedDims(outputDims, dims1);

    for (std::size_t i = 0; i < gradOutputLength; ++i) {
        auto idxOutputGrad = getMultiDimIndices(outputDims, i);
        std::vector<std::size_t> idxInput0(broadcastedDims0.size());
        std::vector<std::size_t> idxInput1(broadcastedDims1.size());

        // Map output indices to input indices, considering broadcasting
        for (std::size_t dimension = 0; dimension < broadcastedDims0.size(); ++dimension) {
            idxInput0[dimension] = (broadcastedDims0[dimension] == 1) ? 0 : idxOutputGrad[dimension];
        }

        for (std::size_t dimension = 0; dimension < broadcastedDims1.size(); ++dimension) {
            idxInput1[dimension] = (broadcastedDims1[dimension] == 1) ? 0 : idxOutputGrad[dimension];
        }

        auto idx0 = getFlattenedIndex(broadcastedDims0, idxInput0);
        auto idx1 = getFlattenedIndex(broadcastedDims1, idxInput1);

        // grad_a = grad_output * (1/b)
        grad_input_0[idx0] += static_cast<I1>(grad_output[i] / input1[idx1]);
        
        // grad_b = grad_output * (-a/b²)
        grad_input_1[idx1] += static_cast<I2>(grad_output[i] * (-input0[idx0] / (input1[idx1] * input1[idx1])));
    }
}


// Kernels registration to implementation entry point
REGISTRAR(DivImpl_cpu,
    {DataType::Float32},
    {ProdConso::inPlaceModel, Aidge::DivImpl_cpu_forward_kernel<float, float, float>, Aidge::DivImpl_cpu_backward_kernel<float, float, float>});
REGISTRAR(DivImpl_cpu,
    {DataType::Float64},
    {ProdConso::inPlaceModel, Aidge::DivImpl_cpu_forward_kernel<double, double, double>, Aidge::DivImpl_cpu_backward_kernel<double, double, double>});
REGISTRAR(DivImpl_cpu,
    {DataType::Int32},
    {ProdConso::inPlaceModel, Aidge::DivImpl_cpu_forward_kernel<std::int32_t, std::int32_t, std::int32_t>, 
          Aidge::DivImpl_cpu_backward_kernel<std::int32_t, std::int32_t, std::int32_t>});
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_DIVIMPL_KERNELS_H_ */
