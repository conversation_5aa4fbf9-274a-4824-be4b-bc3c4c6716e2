/********************************************************************************
 * Copyright (c) 2025 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_DROPOUTIMPL_H_
#define AIDGE_CPU_OPERATOR_DROPOUTIMPL_H_

#include <cstddef> // std::size_t

#include "aidge/backend/cpu/operator/OperatorImpl.hpp"
#include "aidge/operator/Dropout.hpp"
#include "aidge/utils/Registrar.hpp"

namespace Aidge {

// Operator implementation entry point for the backend
using DropoutImpl_cpu = OperatorImpl_cpu<Dropout_Op,
    void(float,
        std::size_t,
        unsigned int,
        const void*,
        void*)>;

// Implementation entry point registration to Operator
REGISTRAR(Dropout_Op, "cpu", Aidge::DropoutImpl_cpu::create);

}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_DROPOUTIMPL_H_ */
