/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_DIVIMPL_H_
#define AIDGE_CPU_OPERATOR_DIVIMPL_H_

#include <memory>
#include <tuple>
#include <vector>

#include "aidge/backend/cpu/operator/OperatorImpl.hpp"
#include "aidge/operator/Div.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {
// Operator implementation entry point for the backend
using DivImpl_cpu = OperatorImpl_cpu<Div_Op,
    void(const std::size_t, const std::size_t, const std::size_t, const void*, const void*,void*),
    void(const std::size_t,
        const std::size_t,
        const std::size_t,
        const std::vector<std::size_t>,
        const std::vector<std::size_t>,
        const std::vector<std::size_t>,
        const void*,
        const void*,
        const void*,
        void*,
        void*)>;

// Implementation entry point registration to Operator
REGISTRAR(Div_Op, "cpu", Aidge::DivImpl_cpu::create);
}  // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_DIVIMPL_H_ */
