/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#ifndef AIDGE_CPU_OPERATOR_CONVIMPL_H_
#define AIDGE_CPU_OPERATOR_CONVIMPL_H_

#include <array>

#include "aidge/backend/cpu/operator/OperatorImpl.hpp"
#include "aidge/operator/Conv.hpp"
#include "aidge/utils/Registrar.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {

// Operator implementation entry point for the backend
using Conv1D_Op = Conv_Op<1>;
using ConvImpl1D_cpu = OperatorImpl_cpu<Conv_Op<1>,
                                        void(const std::array<DimSize_t, 1> &,
                                             const std::array<DimSize_t, 1> &,
                                             const std::array<DimSize_t, 1> &,
                                             const std::array<DimSize_t, 3> &,
                                             DimSize_t,
                                             const void *,
                                             const void *,
                                             const void *,
                                             void *),
                                        void(const std::array<DimSize_t, 1> &,
                                             const std::array<DimSize_t, 1> &,
                                             const std::array<DimSize_t, 1> &,
                                             const std::array<DimSize_t, 3> &,
                                             const std::array<DimSize_t, 3> &,
                                             const void *,
                                             const void *,
                                             const void *,
                                             void *,
                                             void *,
                                             void *)>;

using Conv2D_Op = Conv_Op<2>;
using ConvImpl2D_cpu = OperatorImpl_cpu<Conv2D_Op,
                                        void(const std::array<DimSize_t, 2> &,
                                             const std::array<DimSize_t, 2> &,
                                             const std::array<DimSize_t, 2> &,
                                             const std::array<DimSize_t, 4> &,
                                             DimSize_t,
                                             const void *,
                                             const void *,
                                             const void *,
                                             void *),
                                        void(const std::array<DimSize_t, 2> &,
                                             const std::array<DimSize_t, 2> &,
                                             const std::array<DimSize_t, 2> &,
                                             const std::array<DimSize_t, 4> &,
                                             const std::array<DimSize_t, 4> &,
                                             const void *,
                                             const void *,
                                             const void *,
                                             void *,
                                             void *,
                                             void *)>;

using Conv3D_Op = Conv_Op<3>;
using ConvImpl3D_cpu = OperatorImpl_cpu<Conv3D_Op,
                                        void(const std::array<DimSize_t, 3> &,
                                             const std::array<DimSize_t, 3> &,
                                             const std::array<DimSize_t, 3> &,
                                             const std::array<DimSize_t, 5> &,
                                             const std::array<DimSize_t, 5> &,
                                             const void *,
                                             const void *,
                                             const void *,
                                             void *),
                                        void(const std::array<DimSize_t, 3> &,
                                             const std::array<DimSize_t, 3> &,
                                             const std::array<DimSize_t, 3> &,
                                             const std::array<DimSize_t, 5> &,
                                             const std::array<DimSize_t, 5> &,
                                             const void *,
                                             const void *,
                                             const void *,
                                             void *,
                                             void *,
                                             void *)>;

// Implementation entry point registration to Operator
REGISTRAR(Conv1D_Op, "cpu", Aidge::ConvImpl1D_cpu::create);
REGISTRAR(Conv2D_Op, "cpu", Aidge::ConvImpl2D_cpu::create);
REGISTRAR(Conv3D_Op, "cpu", Aidge::ConvImpl3D_cpu::create);

} // namespace Aidge

#endif /* AIDGE_CPU_OPERATOR_CONVIMPL_H_ */
