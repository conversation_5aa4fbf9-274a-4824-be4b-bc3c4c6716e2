#ifndef AIDGE_UTILS_SYS_INFO_CPU_VERSION_INFO_H
#define AIDGE_UTILS_SYS_INFO_CPU_VERSION_INFO_H

#include <fmt/core.h>
#include "aidge/backend/cpu_version.h"

namespace Aidge {

constexpr inline const char * getBackendCPUProjectVersion(){
    return PROJECT_VERSION;
}

constexpr inline const char * getBackendCPUGitHash(){
    return PROJECT_GIT_HASH;
}

void showBackendCpuVersion() {
    fmt::println("Aidge backend CPU: {} ({}), {} {}", getBackendCPUProjectVersion(), getBackendCPUGitHash(), __DATE__, __TIME__);
        // Compiler version
    #if defined(__clang__)
    /* Clang/LLVM. ---------------------------------------------- */
        fmt::println("Clang/LLVM compiler version: {}.{}.{}\n", __clang_major__ , __clang_minor__, __clang_patchlevel__);
    #elif defined(__ICC) || defined(__INTEL_COMPILER)
    /* Intel ICC/ICPC. ------------------------------------------ */
        fmt::println("Intel ICC/ICPC compiler version: {}\n", __INTEL_COMPILER);
    #elif defined(__GNUC__) || defined(__GNUG__)
    /* GNU GCC/G++. --------------------------------------------- */
        fmt::println("GNU GCC/G++ compiler version: {}.{}.{}", __GNUC__, __GNUC_MINOR__, __GNUC_PATCHLEVEL__);
    #elif defined(_MSC_VER)
    /* Microsoft Visual Studio. --------------------------------- */
        fmt::println("Microsoft Visual Studio compiler version: {}\n", _MSC_VER);
    #else
        fmt::println("Unknown compiler\n");
    #endif

}
}  // namespace Aidge
#endif  // AIDGE_UTILS_SYS_INFO_CPU_VERSION_INFO_H
