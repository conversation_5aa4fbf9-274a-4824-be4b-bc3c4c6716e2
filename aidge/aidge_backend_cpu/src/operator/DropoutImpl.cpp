/********************************************************************************
 * Copyright (c) 2025 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include "aidge/backend/cpu/operator/DropoutImpl.hpp"

#include <stdexcept> // std::runtime_erro
#include <random>    // std::random_device

#include "aidge/backend/cpu/data/GetCPUPtr.h"
#include "aidge/data/Tensor.hpp"
#include "aidge/operator/Dropout.hpp"
#include "aidge/utils/ErrorHandling.hpp"
#include "aidge/utils/Registrar.hpp"


#include "aidge/backend/cpu/operator/DropoutImpl_kernels.hpp"

template <>
void Aidge::DropoutImpl_cpu::forward() {
    const Dropout_Op& op_ = dynamic_cast<const Dropout_Op&>(mOp);
    // Check if input is provided
    AIDGE_ASSERT(op_.getInput(0), "missing input #0 in Dropout Operator.");

    // Get random seed
    const unsigned int seed = static_cast<unsigned int>(std::random_device{}());

    // Find the correct kernel type
    const auto impl = Registrar<DropoutImpl_cpu>::create(getBestMatch(getRequiredSpec()));

    // Call kernel
    impl.forward(op_.probability(),
                op_.getInput(0)->size(),
                seed,
                std::static_pointer_cast<Tensor>(mOp.getRawInput(0))->getImpl()->rawPtr(),
                std::static_pointer_cast<Tensor>(mOp.getRawOutput(0))->getImpl()->rawPtr());
}

template <>
void Aidge::DropoutImpl_cpu::backward() {
    AIDGE_THROW_OR_ABORT(std::runtime_error, "Backward not yet implemented for Dropout_Op on backend cpu");
}