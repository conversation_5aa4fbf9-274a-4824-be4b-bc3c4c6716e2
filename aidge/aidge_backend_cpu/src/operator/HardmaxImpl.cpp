/********************************************************************************
 * Copyright (c) 2024 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include <cassert>
#include <vector>

#include "aidge/data/Tensor.hpp"
#include "aidge/operator/Hardmax.hpp"
#include "aidge/utils/Types.h"

#include "aidge/backend/cpu/operator/HardmaxImpl.hpp"
#include "aidge/backend/cpu/operator/HardmaxImpl_kernels.hpp"

// This function is a specialization of the operator OperatorImpl_cpu::forward
// function It's purpose is to :
// 1. Retrieve all tensors for the operations, hence ensuring that all
//    in/outputs are indeed connected.
// 2. Assert thing that couldn't be checked when creating the operator.
// 3. Retrieve the best operator implementation regarding the input tensor
//    types and the operator configuration.
template <> void Aidge::HardmaxImpl_cpu::forward() {
    const Hardmax_Op &op_ = dynamic_cast<const Hardmax_Op &>(mOp);

    // Check if input is provided
    assert(op_.getInput(0) && "missing input");

    // Find the correct kernel type
    const auto impl =
        Registrar<HardmaxImpl_cpu>::create(getBestMatch(getRequiredSpec()));

    // Call kernel
    impl.forward(op_.axis(),
                 op_.getInput(0)->dims(),
                 op_.getInput(0)->getImpl()->rawPtr(),
                 op_.getOutput(0)->getImpl()->rawPtr());
}

// As there is currently no backward kernel for this operator.
// This function is a placeholder.
template <> void Aidge::HardmaxImpl_cpu::backward() {
    AIDGE_THROW_OR_ABORT(
        std::runtime_error,
        "Backward not yet implemented for Hardmax_Op on backend cpu");
}
