/********************************************************************************
 * Copyright (c) 2023 CEA-List
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 *
 * SPDX-License-Identifier: EPL-2.0
 *
 ********************************************************************************/

#include "aidge/backend/cpu/operator/ConvImpl.hpp"
#include "aidge/backend/cpu/operator/ConvImpl_kernels.hpp"

#include <memory>
#include <vector>

#include "aidge/backend/cpu/data/GetCPUPtr.h"
#include "aidge/operator/Conv.hpp"
#include "aidge/utils/ErrorHandling.hpp"
#include "aidge/utils/Types.h"

namespace Aidge {

template <>
void ConvImpl1D_cpu::forward() {
    const auto& op_ = static_cast<const Conv_Op<1>&>(mOp);

    AIDGE_ASSERT(op_.getInput(0), "missing input #0 in Conv Operator.");
    AIDGE_ASSERT(op_.getInput(1), "missing input #1 in Conv Operator.");

    // Find the correct kernel type
    const auto impl = Registrar<ConvImpl1D_cpu>::create(getBestMatch(getRequiredSpec()));

    // Convert input data (no overhead if not needed!)
    // TODO: right now, if needed, memory will be allocated/deallocated at each
    // call to forward(). We might put the following shared_ptr as members of
    // this class to avoid that.
    std::shared_ptr<Tensor> input0Fallback, input1Fallback, input2Fallback;
    const auto& input0 = op_.getInput(0)->refCastFrom(input0Fallback, *op_.getOutput(0));
    const auto& input1 = op_.getInput(1)->refCastFrom(input1Fallback, *op_.getOutput(0));
    const auto& input2 = (op_.getInput(2)) ? op_.getInput(2)->refCastFrom(input2Fallback, *op_.getOutput(0)) : Tensor();

    // Call kernel
    impl.forward(
        op_.strideDims(),
        op_.dilationDims(),
        op_.kernelDims(),
        op_.getInput(0)->template dims<3>(), // input dimensions
        dynamic_cast<const Conv_Op<1> &>(mOp).outChannels(),    // outChannels
        input0.getImpl()->rawPtr(),                             // input
        input1.getImpl()->rawPtr(),                             // weight
        op_.getInput(2) ? input2.getImpl()->rawPtr() : nullptr, // bias
        getCPUPtr(mOp.getRawOutput(0))                          // output
    );
}

template <>
void ConvImpl1D_cpu::backward() {
    const auto &op = dynamic_cast<const Conv1D_Op &>(mOp);
    const auto &outputGrad = op.getOutput(0)->grad();
    AIDGE_ASSERT(outputGrad, "{}: missing ouput #0 gradient", op.type());
    AIDGE_ASSERT(op.getInput(0)->grad(),
                 "{}: missing data input(#0) gradient",
                 op.type());
    AIDGE_ASSERT(op.getInput(1)->grad(),
                 "{}: missing weight input(#1) gradient",
                 op.type());

    std::shared_ptr<Tensor> inputDataGradFallback, inputWeightGradFallback,
        inputBiasGradFallback;
    const auto &inputDataGrad =
        op.getInput(0)->grad()->refCastFrom(inputDataGradFallback,
                                            *(op.getOutput(0)));
    const auto &inputWeightGrad =
        op.getInput(1)->grad()->refCastFrom(inputWeightGradFallback,
                                            *(op.getOutput(0)));
    const auto &inputBiasGrad =
        (op.getInput(2) && op.getInput(2)->grad())
            ? op.getInput(2)->grad()->refCastFrom(inputBiasGradFallback,
                                                  *(op.getOutput(0)))
            : Tensor();

    // Call kernel
    const auto impl =
        Registrar<ConvImpl1D_cpu>::create(getBestMatch(getRequiredSpec()));
    impl.backward(
        op.strideDims(),
        op.dilationDims(),
        op.kernelDims(),
        op.getInput(0)->template dims<3>(),
        op.getOutput(0)->template dims<3>(),

        getCPUPtr(op.getInput(0)),
        getCPUPtr(op.getInput(1)),
        getCPUPtr(outputGrad),
        inputDataGrad.getImpl()->rawPtr(),
        inputWeightGrad.getImpl()->rawPtr(),
        op.getInput(2) ? inputBiasGrad.getImpl()->rawPtr() : nullptr);
}

template <>
void ConvImpl2D_cpu::forward() {
    const auto& op_ = dynamic_cast<const Conv_Op<2>&>(mOp);

    AIDGE_ASSERT(op_.getInput(0), "missing input #0 in Conv Operator.");
    AIDGE_ASSERT(op_.getInput(1), "missing input #1 in Conv Operator.");

    // Find the correct kernel type
    const auto impl = Registrar<ConvImpl2D_cpu>::create(getBestMatch(getRequiredSpec()));

    // Convert input data (no overhead if not needed!)
    // TODO: right now, if needed, memory will be allocated/deallocated at each
    // call to forward(). We might put the following shared_ptr as members of
    // this class to avoid that.
    std::shared_ptr<Tensor> input0Fallback, input1Fallback, input2Fallback;
    const auto& input0 = op_.getInput(0)->refCastFrom(input0Fallback, *op_.getOutput(0));
    const auto& input1 = op_.getInput(1)->refCastFrom(input1Fallback, *op_.getOutput(0));
    const auto& input2 = (op_.getInput(2)) ? op_.getInput(2)->refCastFrom(input2Fallback, *op_.getOutput(0)) : Tensor();

    // Call kernel
    impl.forward(op_.strideDims(),
            op_.dilationDims(),
            op_.kernelDims(),
            op_.getInput(0)->template dims<4>(), // input dimensions
            dynamic_cast<const Conv_Op<2>&>(mOp).outChannels(), // outChannels
            input0.getImpl()->rawPtr(), // input
            input1.getImpl()->rawPtr(), // weight
            op_.getInput(2) ? input2.getImpl()->rawPtr() : nullptr, // bias
            getCPUPtr(mOp.getRawOutput(0)) // output
            );
}


template <>
void ConvImpl2D_cpu::backward() {
    const auto &op = dynamic_cast<const Conv2D_Op &>(mOp);
    const auto &outputGrad = op.getOutput(0)->grad();
    AIDGE_ASSERT(outputGrad, "{}: missing ouput #0 gradient", op.type());
    AIDGE_ASSERT(op.getInput(0)->grad(),
                 "{}: missing data input(#0) gradient",
                 op.type());
    AIDGE_ASSERT(op.getInput(1)->grad(),
                 "{}: missing weight input(#1) gradient",
                 op.type());

    std::shared_ptr<Tensor> inputDataGradFallback, inputWeightGradFallback,
        inputBiasGradFallback;
    const auto &inputDataGrad =
        op.getInput(0)->grad()->refCastFrom(inputDataGradFallback,
                                            *(op.getOutput(0)));
    const auto &inputWeightGrad =
        op.getInput(1)->grad()->refCastFrom(inputWeightGradFallback,
                                            *(op.getOutput(0)));
    const auto &inputBiasGrad =
        (op.getInput(2) && op.getInput(2)->grad())
            ? op.getInput(2)->grad()->refCastFrom(inputBiasGradFallback,
                                                  *(op.getOutput(0)))
            : Tensor();

    // Call kernel
    const auto impl =
        Registrar<ConvImpl2D_cpu>::create(getBestMatch(getRequiredSpec()));
    impl.backward(
        op.strideDims(),
        op.dilationDims(),
        op.kernelDims(),
        op.getInput(0)->template dims<4>(),
        op.getOutput(0)->template dims<4>(),

        getCPUPtr(op.getInput(0)),
        getCPUPtr(op.getInput(1)),
        getCPUPtr(outputGrad),
        inputDataGrad.getImpl()->rawPtr(),
        inputWeightGrad.getImpl()->rawPtr(),
        op.getInput(2) ? inputBiasGrad.getImpl()->rawPtr() : nullptr);
}

template <>
void Aidge::ConvImpl3D_cpu::forward() {
    const auto& op_ = dynamic_cast<const Conv_Op<3>&>(mOp);

    AIDGE_ASSERT(op_.getInput(0), "missing input #0 in Conv Operator.");
    AIDGE_ASSERT(op_.getInput(1), "missing input #1 in Conv Operator.");


    // Convert input data (no overhead if not needed!)
    // TODO: right now, if needed, memory will be allocated/deallocated at each
    // call to forward(). We might put the following shared_ptr as members of
    // this class to avoid that.
    std::shared_ptr<Tensor> input0Fallback, input1Fallback, input2Fallback;
    const auto& input0 = op_.getInput(0)->refCastFrom(input0Fallback, *op_.getOutput(0));
    const auto& input1 = op_.getInput(1)->refCastFrom(input1Fallback, *op_.getOutput(0));
    const auto& input2 = (op_.getInput(2)) ? op_.getInput(2)->refCastFrom(input2Fallback, *op_.getOutput(0)) : Tensor();

    // Find the correct kernel type
    const auto impl = Registrar<ConvImpl3D_cpu>::create(getBestMatch(getRequiredSpec()));
    // Call kernel
    impl.forward(op_.strideDims(),
            op_.dilationDims(),
            op_.kernelDims(),
            op_.getInput(0)->template dims<5>(), // input dimensions
            op_.getOutput(0)->template dims<5>(), // input dimensions
            input0.getImpl()->rawPtr(), // input
            input1.getImpl()->rawPtr(), // weight
            op_.getInput(2) ? input2.getImpl()->rawPtr() : nullptr, // bias
            getCPUPtr(mOp.getRawOutput(0)) // output
            );
}

template <> void ConvImpl3D_cpu::backward() {
    const auto &op = dynamic_cast<const Conv3D_Op &>(mOp);
    const auto &outputGrad = op.getOutput(0)->grad();
    AIDGE_ASSERT(outputGrad, "{}: missing ouput #0 gradient", op.type());
    AIDGE_ASSERT(op.getInput(0)->grad(),
                 "{}: missing data input(#0) gradient",
                 op.type());
    AIDGE_ASSERT(op.getInput(1)->grad(),
                 "{}: missing weight input(#1) gradient",
                 op.type());

    std::shared_ptr<Tensor> inputDataGradFallback, inputWeightGradFallback,
        inputBiasGradFallback;
    const auto &inputDataGrad =
        op.getInput(0)->grad()->refCastFrom(inputDataGradFallback,
                                            *(op.getOutput(0)));
    const auto &inputWeightGrad =
        op.getInput(1)->grad()->refCastFrom(inputWeightGradFallback,
                                            *(op.getOutput(0)));
    const auto &inputBiasGrad =
        (op.getInput(2) && op.getInput(2)->grad())
            ? op.getInput(2)->grad()->refCastFrom(inputBiasGradFallback,
                                                  *(op.getOutput(0)))
            : Tensor();

    // Call kernel
    const auto impl =
        Registrar<ConvImpl3D_cpu>::create(getBestMatch(getRequiredSpec()));
    impl.backward(
        op.strideDims(),
        op.dilationDims(),
        op.kernelDims(),
        op.getInput(0)->template dims<5>(),
        op.getOutput(0)->template dims<5>(),

        getCPUPtr(op.getInput(0)),
        getCPUPtr(op.getInput(1)),
        getCPUPtr(outputGrad),
        inputDataGrad.getImpl()->rawPtr(),
        inputWeightGrad.getImpl()->rawPtr(),
        op.getInput(2) ? inputBiasGrad.getImpl()->rawPtr() : nullptr);
}

} // namespace Aidge
