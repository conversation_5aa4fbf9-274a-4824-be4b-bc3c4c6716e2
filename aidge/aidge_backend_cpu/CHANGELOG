# Verson 0.5.0 (January 31, 2025)

# Verson 0.4.0 (December 6, 2024)

# Version 0.2.2 (May 14, 2024)

* Remove implmentation for Operators soly handling memory and format
 - Concat
 - Gather
 - Memorize
 - Pop
 - Reshape
 - Slice
 - Transpose
* Fix ReLU backward kernel
* Add `showCpuVersion()` function to show which compiler was used

# Version 0.2.1 (April 11, 2024)

Fix: explicit linkage with fmt

# Version 0.2.0 (April 10, 2024)

# Version 0.1.0 (January 23, 2024)

Initial release
