import aidge_core
from aidge_core.export_utils import ExportNodeCpp, ExportNode
from aidge_export_cpp import ExportLibCpp, ROOT

class AdvancedCustomExportLib(ExportLibCpp):
    """Advanced custom export library with fallback to parent implementations"""
    _name = "advanced_custom_export"
    
    # Inherit all static files from ExportLibCpp
    static_files = ExportLibCpp.static_files.copy()
    
    def get_export_node(self, spec: aidge_core.ImplSpec) -> ExportNode:
        """Override to provide fallback mechanism"""
        # First try to find in our custom registry
        if self.get_operator().type() in self._export_node_registry:
            for registered_spec, export_node in self._export_node_registry[self.get_operator().type()]:
                if registered_spec == spec:
                    return export_node
        
        # Fallback to parent ExportLibCpp registry
        if self.get_operator().type() in ExportLibCpp._export_node_registry:
            for registered_spec, export_node in ExportLibCpp._export_node_registry[self.get_operator().type()]:
                if registered_spec == spec:
                    return export_node
        
        return None

# Register only the operators you want to customize
@AdvancedCustomExportLib.register("Conv2D", aidge_core.ImplSpec(aidge_core.IOSpec(aidge_core.dtype.any)))
class AdvancedCustomConv2D(ExportNodeCpp):
    def __init__(self, node, mem_info):
        super().__init__(node, mem_info)
        
        # Your advanced custom implementation
        self.attributes["optimization_level"] = "high"
        self.attributes["custom_kernel"] = "optimized_conv2d"
        
        # Use custom templates
        self.config_template = str(ROOT / "templates" / "configuration" / "custom_conv2d_config.jinja")
        self.forward_template = str(ROOT / "templates" / "kernel_forward" / "custom_conv2d_forward.jinja")
        
        # Custom kernel files to copy
        self.kernels_to_copy = [
            "path/to/custom/optimized_conv2d.hpp",
            "path/to/custom/conv2d_utils.hpp"
        ]
        
    def export(self, export_folder):
        # Custom export logic with optimizations
        configs = []
        
        # Copy custom kernel files
        for kernel in self.kernels_to_copy:
            # Copy kernel implementation
            pass  # Implement your file copying logic
            
        # Generate custom configuration
        configs.append(f"layers/{self.name}.h")
        
        return configs
        
    def forward(self):
        # Custom forward pass generation
        actions = []
        
        if not self.is_last:
            actions.append(f"setup_optimized_output({self.name});")
            
        actions.append(f"optimized_conv2d_forward({self.name});")
        
        return actions

# Don't register ReLU - it will fallback to ExportLibCpp's implementation
# This way, Conv2D uses your custom implementation, but ReLU uses the default
