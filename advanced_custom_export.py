import aidge_core
from aidge_core.export_utils import ExportNodeCpp, ExportNode
from aidge_export_cpp import ExportLibCpp, ROOT

class AdvancedCustomExportLib(ExportLibCpp):
    """Advanced custom export library with fallback to parent implementations"""
    _name = "advanced_custom_export"
    
    # Inherit all static files from ExportLibCpp
    static_files = ExportLibCpp.static_files.copy()
    
    def get_export_node(self, spec: aidge_core.ImplSpec) -> ExportNode:
        """Override to provide fallback mechanism"""
        # First try to find in our custom registry
        if self.get_operator().type() in self._export_node_registry:
            for registered_spec, export_node in self._export_node_registry[self.get_operator().type()]:
                if registered_spec == spec:
                    return export_node
        
        # Fallback to parent ExportLibCpp registry
        if self.get_operator().type() in ExportLibCpp._export_node_registry:
            for registered_spec, export_node in ExportLibCpp._export_node_registry[self.get_operator().type()]:
                if registered_spec == spec:
                    return export_node
        
        return None

# Register only the operators you want to customize
@AdvancedCustomExportLib.register("Conv2D", aidge_core.ImplSpec(aidge_core.IOSpec(aidge_core.dtype.any)))
class AdvancedCustomConv2D(ExportNodeCpp):
    def __init__(self, node, mem_info):
        super().__init__(node, mem_info)
        
        # Your advanced custom implementation
        self.attributes["optimization_level"] = "high"
        self.attributes["custom_kernel"] = "optimized_conv2d"
        
        # Use custom templates
        self.config_template = str(ROOT / "templates" / "configuration" / "custom_conv2d_config.jinja")
        self.forward_template = str(ROOT / "templates" / "kernel_forward" / "custom_conv2d_forward.jinja")
        
        # Custom kernel files to copy
        self.kernels_to_copy = [
            "path/to/custom/optimized_conv2d.hpp",
            "path/to/custom/conv2d_utils.hpp"
        ]
        
    def export(self, export_folder):
        # Custom export logic with optimizations
        configs = []
        
        # Copy custom kernel files
        for kernel in self.kernels_to_copy:
            # Copy kernel implementation
            pass  # Implement your file copying logic
            
        # Generate custom configuration
        configs.append(f"layers/{self.name}.h")
        
        return configs
        
    def forward(self):
        # Custom forward pass generation
        actions = []
        
        if not self.is_last:
            actions.append(f"setup_optimized_output({self.name});")
            
        actions.append(f"optimized_conv2d_forward({self.name});")
        
        return actions

# Don't register ReLU - it will fallback to ExportLibCpp's implementation
# This way, Conv2D uses your custom implementation, but ReLU uses the default

# Utility functions for MetaOperator handling
def is_metaoperator(node: aidge_core.Node) -> bool:
    """Check if a node is a MetaOperator"""
    return isinstance(node.get_operator(), aidge_core.MetaOperatorOp)

def get_metaoperator_nodes(node: aidge_core.Node, node_type: str = None):
    """Get nodes from MetaOperator, optionally filtered by type"""
    if not is_metaoperator(node):
        return []

    micro_graph = node.get_operator().get_micro_graph()
    nodes = micro_graph.get_nodes()

    if node_type:
        return [n for n in nodes if n.type() == node_type]
    return nodes

def process_node_or_metaop(node: aidge_core.Node):
    """Example of processing a node that might be a MetaOperator"""
    if is_metaoperator(node):
        print(f"Node {node.name()} is a MetaOperator")

        # Get all nodes inside the MetaOperator
        internal_nodes = get_metaoperator_nodes(node)
        print(f"Contains {len(internal_nodes)} internal nodes:")

        for internal_node in internal_nodes:
            print(f"  - {internal_node.name()} ({internal_node.type()})")

        # Get specific types from MetaOperator
        conv_nodes = get_metaoperator_nodes(node, "Conv2D")
        relu_nodes = get_metaoperator_nodes(node, "ReLU")

        print(f"Conv2D nodes: {len(conv_nodes)}")
        print(f"ReLU nodes: {len(relu_nodes)}")

    else:
        print(f"Node {node.name()} is a regular {node.type()} operator"

# Example usage in export context
def handle_metaoperator_in_export(node: aidge_core.Node):
    """Handle MetaOperator during export process"""
    if is_metaoperator(node):
        # For MetaOperators, you might want to:
        # 1. Export the entire MetaOperator as a single unit
        # 2. Or expand it and export individual components

        # Option 1: Export as single unit
        return export_metaoperator_as_unit(node)

        # Option 2: Expand and export components
        # internal_nodes = get_metaoperator_nodes(node)
        # return export_expanded_metaoperator(internal_nodes)
    else:
        # Regular operator export
        return export_regular_operator(node)

def export_metaoperator_as_unit(node: aidge_core.Node):
    """Export MetaOperator as a single optimized unit"""
    # Implementation for exporting MetaOperator as one piece
    pass

def export_regular_operator(node: aidge_core.Node):
    """Export regular operator"""
    # Implementation for regular operator export
    pass
