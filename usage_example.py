import aidge_core
from aidge_export_cpp import ExportLibCpp
from custom_export_lib import MyCustomExportLib

# Method 1: Use multiple export libraries with priority
# The scheduler_export function will try libraries in order
def export_with_custom_lib(model, scheduler, export_folder):
    """Export using custom library first, fallback to default ExportLibCpp"""
    aidge_core.export_utils.scheduler_export(
        scheduler,
        export_folder,
        [MyCustomExportLib, ExportLibCpp],  # Priority order: custom first, then default
        memory_manager=aidge_core.mem_info.compute_default_mem_info
    )

# Method 2: Set backend explicitly for specific operators
def export_with_mixed_backends(model, scheduler, export_folder):
    """Set custom backend for specific operators, default for others"""
    
    # Set custom backend for specific operators
    for node in model.get_nodes():
        if node.type() in ["Conv2D", "ReLU"]:  # Your custom operators
            node.get_operator().set_backend("my_custom_export")
        else:  # Use default for all others
            node.get_operator().set_backend("export_cpp")
    
    # Export with both libraries available
    aidge_core.export_utils.scheduler_export(
        scheduler,
        export_folder,
        [MyCustomExportLib, ExportLibCpp],
        memory_manager=aidge_core.mem_info.compute_default_mem_info
    )

# Example usage
if __name__ == "__main__":
    # Create your model
    model = aidge_core.sequential([
        aidge_core.Conv2D(3, 64, [3, 3], name="conv1"),
        aidge_core.ReLU(name="relu1"),
        aidge_core.Conv2D(64, 128, [3, 3], name="conv2"),
        aidge_core.ReLU(name="relu2")
    ])
    
    # Configure model
    model.compile("cpu", aidge_core.dtype.float32)
    
    # Create scheduler
    scheduler = aidge_core.SequentialScheduler(model)
    
    # Export using method 1 (automatic priority)
    export_with_custom_lib(model, scheduler, "export_auto_priority")
    
    # Export using method 2 (explicit backend setting)
    export_with_mixed_backends(model, scheduler, "export_mixed_backends")
