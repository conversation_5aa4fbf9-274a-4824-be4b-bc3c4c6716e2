build:ubuntu_cpp:
  stage: build
  needs: []
  tags:
    - docker

  script:
    - ./setup.sh --all --tests

build:ubuntu_python:
  stage: build
  needs: []
  tags:
    - docker

  script:
    - python3 -m pip install virtualenv
    - virtualenv venv
    - source venv/bin/activate
    - python3 -m pip install requests # Required for unit test
    - ./setup.sh --all --python --tests
  artifacts:
    expire_in: 1 week
    paths:
      - venv/
