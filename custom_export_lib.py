import aidge_core
from aidge_core.export_utils import ExportNodeCpp
from aidge_export_cpp import ExportLibCpp, ROOT

class MyCustomExportLib(ExportLibCpp):
    """Custom export library that extends ExportLibCpp with custom implementations"""
    _name = "my_custom_export"
    
    # Inherit all static files from ExportLibCpp
    static_files = ExportLibCpp.static_files.copy()
    
    # Add any additional static files if needed
    # static_files.update({
    #     "path/to/my/custom/file.hpp": "include/custom/"
    # })

# Now register your custom implementations
@MyCustomExportLib.register("Conv2D", aidge_core.ImplSpec(aidge_core.IOSpec(aidge_core.dtype.any)))
class MyCustomConv2D(ExportNodeCpp):
    def __init__(self, node, mem_info):
        super().__init__(node, mem_info)
        
        # Your custom implementation here
        self.attributes["custom_attribute"] = "my_value"
        
        # Custom templates
        self.config_template = "path/to/my/custom/conv2d_config.jinja"
        self.forward_template = "path/to/my/custom/conv2d_forward.jinja"
        
    def export(self, export_folder):
        # Your custom export logic
        return super().export(export_folder)
        
    def forward(self):
        # Your custom forward logic
        return super().forward()

@MyCustomExportLib.register("ReLU", aidge_core.ImplSpec(aidge_core.IOSpec(aidge_core.dtype.any)))
class MyCustomReLU(ExportNodeCpp):
    def __init__(self, node, mem_info):
        super().__init__(node, mem_info)
        
        # Your custom ReLU implementation
        self.attributes["activation"] = "CustomRectifier"
        
    def export(self, export_folder):
        # Custom export logic for ReLU
        return []
        
    def forward(self):
        # Custom forward logic for ReLU
        return []
